<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="auto">
            <el-form-item label="承包商名称" prop="contractorName">
                <el-input v-model="queryParams.contractorName" placeholder="请输入承包商名称" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="承包商类型" prop="contractorType">
                <el-select v-model="queryParams.contractorType" placeholder="请选择承包商类型" clearable style="width: 214px;">
                    <el-option v-for="dict in cbs_contractor_type" :key="dict.value" :label="dict.label"
                        :value="dict.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="主要负责人" prop="principal">
                <el-input v-model="queryParams.principal" placeholder="请输入主要负责人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['contractor:basic:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['contractor:basic:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="Upload" @click="handleImport"
                    v-hasPermi="['contractor:basic:import']">导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport"
                    v-hasPermi="['contractor:basic:export']">导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="importTemplate">下载模板</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="basicList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" show-overflow-tooltip />
            <el-table-column label="承包商名称" align="center" prop="contractorName" show-overflow-tooltip />
            <el-table-column label="承包商类型" align="center" prop="contractorType" show-overflow-tooltip>
                <template #default="scope">
                    <dict-tag :options="cbs_contractor_type" :value="scope.row.contractorType" />
                </template>
            </el-table-column>
            <el-table-column label="主要负责人" align="center" prop="principal" show-overflow-tooltip />
            <el-table-column label="联系方式" align="center" prop="managerPhone" show-overflow-tooltip />
            <el-table-column label="营业执照" align="center" prop="businessLicenseUrl" show-overflow-tooltip />
            <el-table-column label="信用代码" align="center" prop="creditCode" show-overflow-tooltip />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['contractor:basic:edit']">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['contractor:basic:remove']">删除</el-button>
                    <el-button link type="primary" icon="Info" @click="handleDetails(scope.row)"
                        v-hasPermi="['contractor:basic:details']">详情</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改承包商基本信息对话框 -->
        <el-dialog :title="title" v-model="open" width="800px" append-to-body>
            <el-form ref="basicRef" :model="form" :rules="rules" label-width="auto">
                <el-form-item label="承包商名称" prop="contractorName">
                    <el-input v-model="form.contractorName" placeholder="请输入承包商名称" />
                </el-form-item>
                <el-form-item label="承包商类型" prop="contractorType">
                    <el-select v-model="form.contractorType" placeholder="请选择承包商类型">
                        <el-option v-for="dict in cbs_contractor_type" :key="dict.value" :label="dict.label"
                            :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="主要负责人" prop="principal">
                    <el-input v-model="form.principal" placeholder="请输入主要负责人" />
                </el-form-item>
                <el-form-item label="联系方式" prop="managerPhone">
                    <el-input v-model="form.managerPhone" placeholder="请输入联系方式" />
                </el-form-item>
                <el-form-item label="营业执照" prop="businessLicenseUrl">
                    <file-upload v-model="form.businessLicenseUrl" />
                </el-form-item>
                <el-form-item label="信用代码" prop="creditCode">
                    <el-input v-model="form.creditCode" placeholder="请输入信用代码" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 导入对话框 -->
        <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
            <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
                :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
                :auto-upload="false" drag>
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitFileForm">确 定</el-button>
                    <el-button @click="upload.open = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 详情对话框 -->
        <el-dialog title="承包商详情" v-model="detailsOpen" width="1000px" append-to-body>
            <!-- 上部分基本信息 -->
            <div class="details-header">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="承包商名称">{{ detailsForm.contractorName }}</el-descriptions-item>
                    <el-descriptions-item label="信用代码">{{ detailsForm.creditCode }}</el-descriptions-item>
                    <el-descriptions-item label="营业执照">
                        <el-image style="width: 100px; height: 100px" :src="detailsForm.businessLicenseUrl"
                            :preview-src-list="[detailsForm.businessLicenseUrl]">
                        </el-image>
                    </el-descriptions-item>
                </el-descriptions>
            </div>

            <!-- 下部分tab切换 -->
            <el-tabs v-model="activeTab" @tab-click="handleTabClick">
                <el-tab-pane label="单位资质" name="qualification">
                    <el-table :data="qualificationList" v-loading="tabLoading">
                        <el-table-column label="资质名称" align="center" prop="qualificationName" />
                        <el-table-column label="资质等级" align="center" prop="qualificationLevel">
                            <template #default="scope">
                                <dict-tag :options="cbs_qualification_level" :value="scope.row.qualificationLevel" />
                            </template>
                        </el-table-column>
                        <el-table-column label="证书编号" align="center" prop="certificateNumber" />
                        <el-table-column label="证书有效期起" align="center" prop="certificateValidityStart" width="180">
                            <template #default="scope">
                                <span>{{ parseTime(scope.row.certificateValidityStart, '{y}-{m}-{d}') }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="证书有效期止" align="center" prop="certificateValidityEnd" width="180">
                            <template #default="scope">
                                <span>{{ parseTime(scope.row.certificateValidityEnd, '{y}-{m}-{d}') }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="附件url" align="center" prop="attachmentUrl" />
                        <el-table-column label="资质类型" align="center" prop="type" />
                    </el-table>
                </el-tab-pane>

                <el-tab-pane label="人员信息" name="person">
                    <el-table :data="personList" v-loading="tabLoading">
                        <el-table-column label="姓名" align="center" prop="name" />
                        <el-table-column label="联系方式" align="center" prop="phone" />
                        <el-table-column label="承包商项目" align="center" prop="contractorProjectId" />
                        <el-table-column label="人员类型" align="center" prop="personType">
                            <template #default="scope">
                                <dict-tag :options="cbs_personnel_type" :value="scope.row.personType" />
                            </template>
                        </el-table-column>
                        <el-table-column label="登记时间" align="center" prop="registrationTime" width="180">
                            <template #default="scope">
                                <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d}') }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="身份证正url" align="center" prop="idCardFrontUrl" width="100">
                            <template #default="scope">
                                <image-preview :src="scope.row.idCardFrontUrl" :width="50" :height="50" />
                            </template>
                        </el-table-column>
                        <el-table-column label="身份证反url" align="center" prop="idCardBackUrl" width="100">
                            <template #default="scope">
                                <image-preview :src="scope.row.idCardBackUrl" :width="50" :height="50" />
                            </template>
                        </el-table-column>
                        <el-table-column label="入场许可期限" align="center" prop="permitDate" width="180">
                            <template #default="scope">
                                <span>{{ parseTime(scope.row.permitDate, '{y}-{m}-{d}') }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="保险url" align="center" prop="insuranceUrl" />
                        <el-table-column label="体检记录url" align="center" prop="medicalRecordUrl" />
                        <el-table-column label="从业资格证书url" align="center" prop="qualificationUrl" />
                        <el-table-column label="从业资格证书有效期起" align="center" prop="qualificationValidityStart"
                            width="180">
                            <template #default="scope">
                                <span>{{ parseTime(scope.row.qualificationValidityStart, '{y}-{m}-{d}') }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="从业资格证书有效期止" align="center" prop="qualificationValidityEnd" width="180">
                            <template #default="scope">
                                <span>{{ parseTime(scope.row.qualificationValidityEnd, '{y}-{m}-{d}') }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>

                <el-tab-pane label="关联项目" name="project">
                    <el-table :data="projectList" v-loading="tabLoading">
                        <el-table-column label="项目编号" align="center" prop="projectNumber" />
                        <el-table-column label="项目名称" align="center" prop="projectName" />
                        <el-table-column label="项目规模" align="center" prop="projectScale">
                            <template #default="scope">
                                <dict-tag :options="cbs_project_scale" :value="scope.row.projectScale" />
                            </template>
                        </el-table-column>
                        <el-table-column label="项目内容" align="center" prop="projectContent" />
                        <el-table-column label="备案机关" align="center" prop="filingAuthority" />
                        <el-table-column label="项目状态" align="center" prop="projectStatus">
                            <template #default="scope">
                                <dict-tag :options="project_status" :value="scope.row.projectStatus" />
                            </template>
                        </el-table-column>
                        <el-table-column label="项目负责人" align="center" prop="projectManager" />
                        <el-table-column label="项目阶段" align="center" prop="projectStage">
                            <template #default="scope">
                                <dict-tag :options="cbs_project_stage" :value="scope.row.projectStage" />
                            </template>
                        </el-table-column>
                        <el-table-column label="项目时间起" align="center" prop="projectStartTime" width="180">
                            <template #default="scope">
                                <span>{{ parseTime(scope.row.projectStartTime, '{y}-{m}-{d}') }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="项目时间止" align="center" prop="projectEndTime" width="180">
                            <template #default="scope">
                                <span>{{ parseTime(scope.row.projectEndTime, '{y}-{m}-{d}') }}</span>
                            </template>
                        </el-table-column>
                        <template #empty>
                            <el-empty description="暂无项目信息" />
                        </template>
                    </el-table>
                </el-tab-pane>

                <el-tab-pane label="违章记录" name="violation">
                    <el-table :data="violationList" v-loading="tabLoading">
                        <el-table-column label="地点" align="center" prop="location" />
                        <el-table-column label="发生时间" align="center" prop="occurrenceTime" width="180">
                            <template #default="scope">
                                <span>{{ parseTime(scope.row.occurrenceTime, '{y}-{m}-{d}') }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="类型" align="center" prop="violationType">
                            <template #default="scope">
                                <dict-tag :options="cbs_violation_type" :value="scope.row.violationType" />
                            </template>
                        </el-table-column>
                        <el-table-column label="处理过程" align="center" prop="handlingProcess" />
                        <el-table-column label="违规描述" align="center" prop="violationDescription" />
                        <el-table-column label="违章人" align="center" prop="violator" />
                    </el-table>
                </el-tab-pane>
                <el-tab-pane label="事故记录" name="accident">
                    <el-table :data="accidentList" v-loading="tabLoading">
                        <el-table-column label="事故类型" align="center" prop="accidentType">
                            <template #default="scope">
                                <dict-tag :options="cbs_accident_type" :value="scope.row.accidentType" />
                            </template>
                        </el-table-column>
                        <el-table-column label="等级" align="center" prop="accidentLevel">
                            <template #default="scope">
                                <dict-tag :options="cbs_accident_level" :value="scope.row.accidentLevel" />
                            </template>
                        </el-table-column>
                        <el-table-column label="发生时间" align="center" prop="occurrenceTime" width="180">
                            <template #default="scope">
                                <span>{{ parseTime(scope.row.occurrenceTime, '{y}-{m}-{d}') }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="地点" align="center" prop="location" />
                        <el-table-column label="原因" align="center" prop="cause" />
                        <el-table-column label="处理过程" align="center" prop="handlingProcess" />
                        <el-table-column label="事故评估报告" align="center" prop="assessmentReportUrl" />
                        <el-table-column label="造成损失" align="center" prop="loss" />
                        <el-table-column label="责任人" align="center" prop="principal" />
                    </el-table>
                </el-tab-pane>

                <el-tab-pane label="工器具记录" name="tools">
                    <el-table :data="toolList" v-loading="tabLoading">
                        <el-table-column label="施工机械" align="center" prop="constructionMachine" />
                        <el-table-column label="工器具" align="center" prop="tools" />
                        <el-table-column label="配件" align="center" prop="accessories" />
                        <el-table-column label="个人防护装备" align="center" prop="protectiveEquipment" />
                        <el-table-column label="物料" align="center" prop="materials" />
                        <el-table-column label="设备检测信息" align="center" prop="equipmentInspection" />
                        <el-table-column label="检测信息有效期" align="center" prop="inspectionValidDate" width="180">
                            <template #default="scope">
                                <span>{{ parseTime(scope.row.inspectionValidDate, '{y}-{m}-{d}') }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
            </el-tabs>
        </el-dialog>
    </div>
</template>

<script setup name="Basic">
import { addBasic, delBasic, getBasic, listBasic, updateBasic } from "@/api/xtcontractormag/BaseInfo/basic";
import useUserStore from "@/store/modules/user";
import { deepClone, formatDate } from "@/utils";
import { getToken } from "@/utils/auth";

const userStore = useUserStore();

const { proxy } = getCurrentInstance();
const { cbs_contractor_type } = proxy.useDict('cbs_contractor_type');

const basicList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const itemsArr = ref([]);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
// 详情数据
const detailsOpen = ref(false);
const detailsForm = ref({});
const activeTab = ref('qualification');
const tabLoading = ref(false);
const qualificationList = ref([]);
const personList = ref([]);
const projectList = ref([]);
const violationList = ref([]);
const accidentList = ref([]);
const toolList = ref([]);

// 详情按钮点击事件
function handleDetails(row) {
    const _id = row.id;
    getBasic(_id).then(response => {
        detailsOpen.value = true;
        detailsForm.value = response.data;
        qualificationList.value = response.data.qualificationList || [];
        personList.value = response.data.personList || [];
        projectList.value = response.data.projectList || [];
        violationList.value = response.data.violationList || [];
        accidentList.value = response.data.accidentList || [];
        toolList.value = response.data.toolList || [];
    });
}

// 处理tab切换
function handleTabClick() {
    // Tab切换时直接使用已有数据，无需重新请求
    activeTab.value = activeTab.value;
}


/*** 用户导入参数 */
const upload = reactive({
    // 是否显示弹出层
    open: false,
    // 弹出层标题
    title: "",
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: "Bearer " + getToken() },
    // 上传的地址
    url: window.xtmapConfig.xtBaseUrl + "/contractor/basic/importData",
});

const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractorName: null,
        contractorType: null,
        principal: null,
        managerPhone: null,
        businessLicenseUrl: null,
        creditCode: null,
    },
    rules: {
        contractorName: [
            { required: true, message: "承包商名称不能为空", trigger: "blur" }
        ],
        contractorType: [
            { required: true, message: "承包商类型不能为空", trigger: "change" }
        ],
        principal: [
            { required: true, message: "主要负责人不能为空", trigger: "blur" }
        ],
        managerPhone: [
            { required: true, message: "联系方式不能为空", trigger: "blur" }
        ],
        //     businessLicenseUrl: [
        //     { required: true, message: "营业执照不能为空", trigger: "blur" }
        // ],
        creditCode: [
            { required: true, message: "信用代码不能为空", trigger: "blur" }
        ],
        deleted: [
            { required: true, message: "$comment不能为空", trigger: "blur" }
        ]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询承包商基本信息列表 */
function getList() {
    loading.value = true;
    listBasic(queryParams.value).then(response => {
        basicList.value = response.rows;
        total.value = response.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: null,
        tenantId: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        contractorName: null,
        contractorType: null,
        principal: null,
        managerPhone: null,
        businessLicenseUrl: null,
        creditCode: null,
        deleted: null
    };
    proxy.resetForm("basicRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    itemsArr.value = deepClone(selection);
    multiple.value = !selection.length;
}

/** 导入按钮操作 */
function handleImport() {
    upload.title = "导入";
    upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
    proxy.download(
        "contractor/basic/export/template",
        {},
        `basic_${new Date().getTime()}.xlsx`
    );
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
    upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
    upload.open = false;
    upload.isUploading = false;
    proxy.$refs["uploadRef"].handleRemove(file);
    proxy.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
    );
    getList();
};

/** 提交上传文件 */
function submitFileForm() {
    upload.url = `${window.xtmapConfig.xtBaseUrl}/contractor/basic/importData?userId=${userStore.id}`;
    proxy.$refs["uploadRef"].submit();
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加承包商基本信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getBasic(_id).then(response => {
        form.value = response.data;
        open.value = true;
        title.value = "修改承包商基本信息";
    });
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["basicRef"].validate(valid => {
        if (valid) {
            if (form.value.id != null) {
                const data = {
                    ...form.value,
                    tenantId: userStore.tenantId,
                    updateBy: userStore.name,
                    updateTime: formatDate(new Date()),
                };
                updateBasic(data).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            } else {
                const data = {
                    ...form.value,
                    tenantId: userStore.tenantId,
                    createBy: userStore.name,
                    createTime: formatDate(new Date()),
                    updateBy: userStore.name,
                    updateTime: formatDate(new Date()),
                    deleted: '0'
                };
                addBasic(data).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row.id ? [row.id] : ids.value;
    proxy.$modal.confirm('是否确认删除承包商基本信息编号为"' + _ids + '"的数据项？').then(function () {
        return delBasic(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
    const params = itemsArr.value.length > 0 ? itemsArr.value : undefined;
    proxy.codeDownload(
        'contractor/basic/export',
        params,
        `basic_${new Date().getTime()}.xlsx`
    );
}

getList();
</script>
<style scoped>
.details-header {
    margin-bottom: 20px;
}
</style>