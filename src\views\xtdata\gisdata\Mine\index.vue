<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">

         <el-form-item label="名称" prop="name">
            <el-input v-model="queryParams.name" placeholder="请输入名称" clearable style="width: 200px"
               @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="所属集团" prop="groupName">
            <el-select v-model="queryParams.groupName" placeholder="所属集团" clearable style="width: 200px">
               <el-option v-for="item in options" :key="item.name" :label="item.name" :value="item.name" />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
               v-hasPermi="['system:post:add']">新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
               v-hasPermi="['system:post:edit']">修改</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
               v-hasPermi="['system:post:remove']">批量删除</el-button>
         </el-col>
         <!-- <el-col :span="1.5">
            <el-button
               type="warning"
               plain
               icon="Download"
               @click="handleExport"
               v-hasPermi="['system:post:export']"
            >导出</el-button>
         </el-col> -->
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange" height="670"
         style="max-height: 670px; overflow-y: auto">
         <el-table-column type="selection" width="55" align="center" />
      

         <el-table-column label="名称" align="center" prop="openPitName" />
         <el-table-column label="矿山编码" align="center" prop="openPitNo" />
         <el-table-column label="所属集团" align="center" prop="groupId">
            <template #default="{ row }">
               <span>{{ getNameById(row.groupId) }}</span>
            </template>
         </el-table-column>
    
         <el-table-column label="矿山类型" align="center" prop="classification">
            <template #default="scope">
               <dict-tag :options="gn_mine_classification" :value="scope.row.classification" />
            </template>
         </el-table-column>
         <el-table-column label="运行状况" align="center" prop="enterpriseState">
            <template #default="scope">
               <dict-tag :options="gn_mine_enterprisestate" :value="scope.row.enterpriseState" />
            </template>
         </el-table-column>
         <el-table-column label="负责人" align="center" prop="enterpriseLegalperson" />
         <el-table-column label="负责人电话" align="center" prop="legalPersonPhone" />
         <!-- <el-table-column label="地址" align="center" prop="address" /> -->

         <!-- <el-table-column label="调度室座机" align="center" prop="controlCenterLandlinePhone" />
         <el-table-column label="填表人" align="center" prop="filler" />
         <el-table-column label="填表人电话" align="center" prop="fillerPhone" />
         <el-table-column label="矿区范围" align="center" prop="openPitBoundary" />
         <el-table-column label="监管主体编号" align="center" prop="supervisingSubject" /> -->


         <el-table-column label="操作" width="230" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:post:edit']">修改</el-button>
               <el-button link type="primary" icon="View" @click="handleLook(scope.row)"
                  v-hasPermi="['system:post:edit']">预览</el-button>
               <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                  v-hasPermi="['system:post:remove']">删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize" @pagination="getList" />

      <!-- 添加或修改岗位对话框 -->
      <el-dialog :title="title" v-model="open" width="700px" append-to-body>
         <el-form ref="postRef" :model="form" :rules="rules" label-width="120px">
            <el-form-item label="名称" prop="openPitName">
               <el-input v-model="form.openPitName" placeholder="请输入名称" />
            </el-form-item>
            <el-form-item label="矿山编码" prop="openPitNo">
               <el-input v-model="form.openPitNo" placeholder="请输入矿山编码" />
            </el-form-item>
           <el-form-item label="所属集团" prop="groupId">
               <el-select v-model="form.groupId" placeholder="所属集团" style="width: 540px">
                  <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id" />
               </el-select>
            </el-form-item>
           
           
            <el-row>
               <el-col span="12">
                  <el-form-item label="运行状况" prop="enterpriseState">
               <el-select v-model="form.enterpriseState" placeholder="请选择" style="width: 540px">
                        <el-option
                           v-for="dict in gn_mine_enterprisestate"
                           :key="dict.value"
                           :label="dict.label"
                           :value="dict.value"
                        ></el-option>
                     </el-select>
            </el-form-item>
               </el-col>
               <el-col span="12">
                  <el-form-item label="矿山类型" prop="classification">
               <el-select v-model="form.classification" placeholder="请选择" style="width: 540px">
                        <el-option
                           v-for="dict in gn_mine_classification"
                           :key="dict.value"
                           :label="dict.label"
                           :value="dict.value"
                        ></el-option>
                     </el-select>
            </el-form-item>
               </el-col>
            </el-row>
            
            <el-row>
               <el-col span="12">
                  <el-form-item label="填表人" prop="filler">
               <el-input v-model="form.filler" placeholder="请输入填表人" />
            </el-form-item>
               </el-col>
               <el-col span="12">
                  <el-form-item label="填表人电话" prop="fillerPhone">
               <el-input v-model="form.fillerPhone" placeholder="请输入填表人电话" />
            </el-form-item>
               </el-col>
            </el-row>
            <el-row>
               <el-col span="12">
                  <el-form-item label="负责人" prop="enterpriseLegalperson">
               <el-input v-model="form.enterpriseLegalperson" placeholder="请输入负责人" />
            </el-form-item>
               </el-col>
               <el-col span="12">
                  <el-form-item label="负责人电话" prop="legalPersonPhone">
               <el-input v-model="form.legalPersonPhone" placeholder="请输入负责人电话" />
            </el-form-item>
               </el-col>
            </el-row>
            
           
            <el-form-item label="地址" prop="address">
               <el-input v-model="form.address" placeholder="请输入地址" />
            </el-form-item>
            <el-form-item label="调度室座机" prop="controlCenterLandlinePhone">
               <el-input v-model="form.controlCenterLandlinePhone" placeholder="请输入调度室座机" />
            </el-form-item>
          
           
            <el-form-item label="矿区范围" prop="openPitBoundary">
               <el-input v-model="form.openPitBoundary" placeholder="请输入矿区范围"  :autosize="{ minRows: 2, maxRows: 4 }"
               type="textarea"/>
            </el-form-item>
            <el-form-item label="监管主体编号" prop="supervisingSubject">
               <el-input v-model="form.supervisingSubject" placeholder="请输入监管主体编号" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>

      <!-- Descriptions 组件 -->
      <el-dialog title="矿山信息" v-model="openInfo" width="600px" append-to-body>
      
         <el-descriptions :column="1" :border="true" label-align="right">
            <el-descriptions-item label-class-name="detail-label" label="名称" >{{ form.openPitName }}</el-descriptions-item>
            <el-descriptions-item label-class-name="detail-label" label="矿山编码">{{ form.openPitNo }}</el-descriptions-item>
            <!-- <el-descriptions-item label-class-name="detail-label" label="所属集团">{{ form.groupId }}</el-descriptions-item> -->
            <el-descriptions-item label="所属集团" label-class-name="detail-label">
              {{getNameById(form.groupId)}}
            </el-descriptions-item>
            <el-descriptions-item label="矿山类型" label-class-name="detail-label">
               <dict-tag :options="gn_mine_classification" :value="form.classification" />
            </el-descriptions-item>
            <el-descriptions-item label="运行状况" label-class-name="detail-label">
               <dict-tag :options="gn_mine_enterprisestate" :value="form.enterpriseState" />
            </el-descriptions-item>
        
            <el-descriptions-item label-class-name="detail-label" label="负责人">{{ form.enterpriseLegalperson }}</el-descriptions-item>
            <el-descriptions-item label-class-name="detail-label" label="负责人电话">{{ form.legalPersonPhone }}</el-descriptions-item>
            <el-descriptions-item label-class-name="detail-label" label="调度室座机">{{ form.controlCenterLandlinePhone }}</el-descriptions-item>
            <el-descriptions-item label-class-name="detail-label" label="填表人">{{ form.filler }}</el-descriptions-item>
            <el-descriptions-item label-class-name="detail-label" label="填表人电话">{{ form.fillerPhone }}</el-descriptions-item>
            <el-descriptions-item label-class-name="detail-label" label="地址">{{ form.address }}</el-descriptions-item>
            <el-descriptions-item label-class-name="detail-label" label="矿区范围">{{ form.openPitBoundary }}</el-descriptions-item>
            <el-descriptions-item label-class-name="detail-label" label="监管主体编号">{{ form.supervisingSubject }}</el-descriptions-item>
         </el-descriptions>

         <template #footer>
            <div class="dialog-footer">
               <el-button @click="closeDescription">关闭</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Post">
import { getMine, addMine, delMine, updateMine } from "@/api/xtdata/organization/Mine";
import { getGroup } from "@/api/xtdata/organization/Group";
const { proxy } = getCurrentInstance();
const { sys_normal_disable, gn_mine_classification,gn_mine_enterprisestate } = proxy.useDict("sys_normal_disable","gn_mine_classification","gn_mine_enterprisestate");

const postList = ref([]);
const openInfo = ref(false);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
//  集团信息
const options = ref([]);

const data = reactive({
   descData: {},
   form: {},
   queryParams: {
      pageNum: 1,
      pageSize: 10,
      name: undefined,
      groupName: undefined,

   },
   rules: {
      

   }
});

const { queryParams, form, rules, descData } = toRefs(data);
/** 查询集团信息 */
function getGroupInfo() {

   getGroup().then(response => {
      options.value = response.rows;

   });
}
function getNameById(id) {
   const item = options.value.find(item => item.id == id);
   return item ? item.name : '未知';
}
/** 查询岗位列表 */
function getList() {
   loading.value = true;

   getMine(queryParams.value).then(response => {
      postList.value = response.rows;
      total.value = response.total;
      loading.value = false;
   });
}
/** 取消按钮 */
function cancel() {
   open.value = false;
   //  reset();
}
function closeDescription() {
   openInfo.value = false;
}

function handleLook(row) {
   openInfo.value = true;
   form.value = row
}
/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
      name: undefined,


   };
   proxy.resetForm("postRef");
}
/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.pageNum = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = "添加矿山信息";
}
/** 修改按钮操作 */
function handleUpdate(row) {
   reset();
   form.value = {};
   form.value = row;
   form.value.classification=row.classification.toString()
   form.value.enterpriseState=row.enterpriseState.toString()
   open.value = true;
   title.value = "修改矿山信息";
}
/** 提交按钮 */
function submitForm() {
   proxy.$refs["postRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateMine(form.value).then(response => {
               proxy.$modal.msgSuccess("修改成功");
               open.value = false;
               getList();
            });
         } else {
            addMine(form.value).then(response => {
               proxy.$modal.msgSuccess("新增成功");
               open.value = false;
               getList();
            });
         }
      }
   });
}
/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id ? [row.id] : ids.value;
   proxy.$modal.confirm('是否确认删除？').then(function () {
      return delMine(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
}
/** 导出按钮操作 */
function handleExport() {
   proxy.download("system/post/export", {
      ...queryParams.value
   }, `post_${new Date().getTime()}.xlsx`);
}

getList();
getGroupInfo()
</script>
<style scoped>
:deep(.detail-label) {
   width: 120px;
 }
</style>