<template>
  <div class="right-base">
    <div class="right-menu">
      <el-button @click="sendMsg"> 提交 </el-button>
      <el-button @click="clearMsg"> 清除 </el-button>
      <el-button @click="addMsg"> 加载 </el-button>
    </div>
    <div class="right-menu2">
      <el-button @click="send"> 发消息 </el-button>
    </div>
  </div>
</template>

<script setup>
import { sendRmqMsg } from "@/api/drill/common/index.js";
import { getMyKey } from "../../common/js/tool";
// 获取xttid

const xttid = getMyKey("xttid");
import { nextTick, ref, reactive, computed, onMounted } from "vue";
const state = reactive({ count: 0 });
const msg = ref("");

function sendMsg() {
  msg.value = JSON.stringify(window.plotPlugin.get());
  // console.log(window.plotPlugin.get(), "111");
  console.log(msg.value, "2222");
  // console.log(JSON.parse(msg.value), "333");
}
function clearMsg() {
  window.plotPlugin.clear();
}
function addMsg() {
  window.plotPlugin.clear();
  window.plotPlugin.set(JSON.parse(msg.value));
}

function send() {
  /* {
    "exchange": "XTEGleaderFanout",
    "routingKey": "",
    "data": "{\"from\":\"dd547088fc748ecc7dc1ee65de10a78a\",\"to\":\"ALL\",\"msg\":{\"msgType\":\"初步研判\",\"leaderUnitInfo\":\"\",\"responseLevel\":\"4\",\"responseLevelName\":\"Ⅳ级响应\",\"sceneInfo\":\"ssssaaa\",\"currentTime\":\"2024-07-24 15:12:40\",\"map\":\"{\\\"plot\\\":{\\\"name\\\":\\\"plot-plugin-app\\\",\\\"data\\\":{\\\"type\\\":\\\"GraphicGroup\\\",\\\"id\\\":\\\"e16a2ed9-7214-4952-a329-ae37c15701f2\\\",\\\"name\\\":\\\"plot-plugin-app\\\",\\\"children\\\":[{\\\"type\\\":\\\"BillboardGraphic\\\",\\\"id\\\":\\\"664156d2-4c0c-411b-b8d5-29b3474190c5\\\",\\\"name\\\":\\\"002-64\\\",\\\"visible\\\":true,\\\"isEditing\\\":false,\\\"allowPick\\\":true,\\\"position\\\":[121.********,30.8075186,0.5],\\\"scale\\\":1,\\\"horizontalOrigin\\\":\\\"center\\\",\\\"verticalOrigin\\\":\\\"center\\\",\\\"rotation\\\":0,\\\"color\\\":\\\"#fff\\\",\\\"textScale\\\":1,\\\"textFont\\\":\\\"30px sans-serif\\\",\\\"image\\\":\\\"http://*************:4321/de-service/plotIcons/应急标绘/事故灾害/002-64.png\\\",\\\"width\\\":20,\\\"height\\\":20,\\\"heightReference\\\":\\\"none\\\"}]}}}\"}}"
} */
  let mapContent = JSON.stringify(window.plotPlugin.get());
  const msgData = reactive({
    exchange: "XTEGleaderFanout",
    routingKey: "",
    data: `{"from":${xttid},"to":"ALL","msg":{"map":${mapContent},"xxx":"这条是指挥席位发的消息 执行席位接收"}}`,
  });
  sendRmqMsg(msgData);
}
onMounted(() => {});
</script>

<style scoped lang="scss">
.right-base {
  background-color: rgba(205, 163, 127, 0.762);
  height: 100vh;
  width: 350px;
  .right-menu {
    padding-top: 300px;
  }

  // background: url("@/assets/xtui/layout/leftbg.png");
  // background-size: 100% 100%;
}
</style>

