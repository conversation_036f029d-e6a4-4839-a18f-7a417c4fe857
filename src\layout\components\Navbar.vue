<template>
  <div class="navbar">
    <!-- <hamburger
      id="hamburger-container"
      :is-active="appStore.sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />
    <breadcrumb
      id="breadcrumb-container"
      class="breadcrumb-container"
      v-if="!settingsStore.topNav"
    />
    <top-nav
      id="topmenu-container"
      class="topmenu-container"
      v-if="settingsStore.topNav"
    /> -->

    <div class="right-menu">
      <div class="tenant-container">
        <el-dropdown @command="handleTenantChange" trigger="click">
          <div class="tenant-wrapper">
            <div class="tenant-name">{{ currentTenantName }}</div>
            <el-icon><caret-bottom /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="(item, index) in userStore.tenantPropertiesData.tenantList"
                :key="index"
                :command="item.id"
              >
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="map-container">
        <el-tooltip content="驾驶舱" effect="dark" placement="bottom">
          <el-icon :size="20" color="#ffffff" @click="linkToMap"><HomeFilled /></el-icon>
        </el-tooltip>
      </div>
      <div class="avatar-container">
        <el-dropdown
          @command="handleCommand"
          class="right-menu-item hover-effect"
          trigger="click"
        >
          <div class="avatar-wrapper">
            <div class="avatar-wrapper-name">{{ userStore.name }}</div>
            <img :src="userStore.avatar" class="user-avatar" />
            <el-icon><caret-bottom /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <router-link to="/user/profile">
                <el-dropdown-item>个人中心</el-dropdown-item>
              </router-link>
              <!-- <el-dropdown-item
                command="setLayout"
                v-if="settingsStore.showSettings"
              >
                <span>布局设置</span>
              </el-dropdown-item> -->
              <el-dropdown-item divided command="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessageBox } from "element-plus";
import Breadcrumb from "@/components/Breadcrumb";
import TopNav from "@/components/TopNav";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";
import HeaderSearch from "@/components/HeaderSearch";
import RuoYiGit from "@/components/RuoYi/Git";
import RuoYiDoc from "@/components/RuoYi/Doc";
import useAppStore from "@/store/modules/app";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";

import { getTenantId } from "@/utils/auth";

const appStore = useAppStore();
const userStore = useUserStore();
const settingsStore = useSettingsStore();

const tenantVal = ref(getTenantId());

// 当前租户名称显示
const currentTenantName = computed(() => {
  const match = userStore.tenantPropertiesData.tenantList?.find(
    (t) => t.id === tenantVal.value
  );
  return match?.name || "请选择租户";
});

// 处理租户切换
const handleTenantChange = (newTenantId) => {
  if (newTenantId && newTenantId !== userStore.tenantId) {
    userStore.switchTenant(newTenantId);
    window.location.reload();
  }
};

function linkToMap() {
  window.open(window.xtmapConfig.xtMapUrl, "_blank");
}

function toggleSideBar() {
  appStore.toggleSideBar();
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      userStore.logOut().then(() => {
        location.href = window.xtmapConfig.nginxPathUrl + "/index";
      });
    })
    .catch(() => {});
}

const emits = defineEmits(["setLayout"]);
function setLayout() {
  emits("setLayout");
}

// 初始化租户信息
onMounted(async () => {
  await userStore.getTenantInfo();
  if (userStore.tenantPropertiesData.tenantList) {
    tenantVal.value = userStore.tenantId;
  }
});
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: static;
  width: 300px;
  // position: relative;
  // background: #fff;
  // box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #ffffff;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }
    .tenant-container {
      position: absolute;
      top: 18px;
      right: 150px;

      .tenant-wrapper {
        display: flex;
        align-items: center;
      }

      .tenant-name {
        margin-right: 5px;
        font-size: 16px;
      }
    }

    .map-container {
      position: absolute;
      top: 5px;
      right: 248px;
      .map-icon {
        width: 36x;
        height: 36px;
      }
    }

    .avatar-container {
      margin-right: 40px;

      .avatar-wrapper {
        margin-top: 10px;
        position: relative;

        .avatar-wrapper-name {
          position: absolute;
          right: 45px;
          top: 7px;
        }

        .user-avatar {
          cursor: pointer;
          width: 30px;
          height: 30px;
          border-radius: 10px;
          // position: absolute;

          // top: 15px;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 10px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
