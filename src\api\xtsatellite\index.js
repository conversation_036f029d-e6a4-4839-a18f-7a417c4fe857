import request from '@/utils/request';

// 查询方法
export function getSatelliteData(params) {
	return request({
		url: '/fireSource',
		headers: {
			isToken: false,
			// repeatSubmit: false
		},
		method: 'get',
		params: params,
	});
}
// 新增用户
export function addUser(data) {
	return request({
		url: '/SendUser',
		headers: {
			isToken: false,
			// repeatSubmit: false
		},
		method: 'post',
		data: data,
	});
}
// 获取用户列表
export function getUserList(params) {
	return request({
		url: '/SendUser',
		headers: {
			isToken: false,
			// repeatSubmit: false
		},
		method: 'get',
		params: params,
	});
}
// 删除用户
export function deleteUser(id) {
	return request({
		url: '/SendUser',
		headers: {
			isToken: false,
			// repeatSubmit: false
		},
		method: 'delete',
		data: id,
	});
}

// 获取ai列表;
export function getAIEventList(params) {
	return request({
		url: '/monitorAI/listEvent',
		headers: {
			isToken: false,
			// repeatSubmit: false
		},
		method: 'get',
		params: params,
	});
}
// 获取短信记录列表;
export function getMsgList(params) {
	return request({
		url: '/sendRecord',
		headers: {
			isToken: false,
			// repeatSubmit: false
		},
		method: 'get',
		params: params,
	});
}

// 新增配置
export function setConfig(data) {
	return request({
		url: '/sendConfig/updateConfig',
		headers: {
			isToken: false,
			// repeatSubmit: false
		},
		method: 'post',
		data: data,
	});
}
// 获取配置
export function getConfig() {
	return request({
		url: '/sendConfig/getConfig',
		headers: {
			isToken: false,
			// repeatSubmit: false
		},
		method: 'get',
	});
}
// 获取车辆树列表
export function getCarTree() {
	return request({
		url: '/gps/info?current=1&size=100',
		headers: {
			isToken: false,
			// repeatSubmit: false
		},
		method: 'get',
	});
}
// 获取GPS位置
export function getCarGPS(data) {
	return request({
		// url: `/gps/track?&gpsInfoId=${data.gpsInfoId}&start=&end=`,
		url: `/gps/track?&gpsInfoId=${data.gpsInfoId}&start=${data.start}&end=${data.end}`,
		headers: {
			isToken: false,
			// repeatSubmit: false
		},
		method: 'get',
	});
}
// 更新方法
// export function register(data) {
// 	return request({
// 		url: '/auth/register',
// 		headers: {
// 			isToken: false,
// 		},
// 		method: 'post',
// 		data: data,
// 	});
// }
