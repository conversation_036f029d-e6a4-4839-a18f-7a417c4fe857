<template>
  <div class="warning">
    <div class="warning-button">
      <div class="warning-button-img"></div>
      <div class="warning-button-click" @click="peoplehandle">人工上报</div>
    </div>
    <div class="warning-ref" ref="listRef">
      <div class="warning-items" v-for="item in WarningList" :key="item.id">
        <div class="warning-items-words">
          <div class="warning-item-title">{{ item.content }}</div>
          <div class="warning-item-address">{{ item.address }}</div>
        </div>
        <div class="warning-items-date">
          {{ item.createTime }}
        </div>
        <div class="warning-items-click" @click="supose(item)">
          <span>处置</span>
        </div>
      </div>
    </div>

    <el-dialog title="接警事件" v-model="dialogTableVisible" width="800">
      <el-form
        ref="ruleFormRef"
        style="max-width: 98%"
        :model="ruleForm"
        :rules="rules"
        label-width="auto"
        :label-position="labelPosition"
        :size="formSize"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="接警人" prop="pickUpPerson" required>
              <el-input v-model="ruleForm.pickUpPerson" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事件名称" prop="accidentName" required>
              <el-input v-model="ruleForm.accidentName" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="接警时间" prop="policeTime" required>
              <el-date-picker
                :teleported="false"
                v-model="ruleForm.policeTime"
                type="date"
                aria-label="Pick a date"
                placeholder="接警时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发生时间" prop="startTime" required>
              <el-date-picker
                :teleported="false"
                v-model="ruleForm.startTime"
                type="date"
                aria-label="Pick a date"
                placeholder="发生时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报警人" prop="callThePolice" required>
              <el-input v-model="ruleForm.callThePolice" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报警电话" prop="policeCall" required>
              <el-input v-model="ruleForm.policeCall" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="事故装置" prop="accidentDevice" required>
              <el-input v-model="ruleForm.accidentDevice" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="涉及化学品" prop="hazardousChemicals" required>
              <el-input v-model="ruleForm.hazardousChemicals" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="事故类型" prop="typeOfAccident" required>
              <!-- <el-input v-model="ruleForm.typeOfAccident" /> -->
              <el-cascader
                :teleported="false"
                style="width: 100%"
                v-model="ruleForm.typeOfAccident"
                :options="typeOfAccidentOptions"
                :props="{ expandTrigger: 'hover' }"
                @change="cascaderhandleChange"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事件状态" prop="status" required>
              <!-- <el-input v-model="ruleForm.status" /> -->
              <el-select
                :teleported="false"
                style="width: 100%"
                v-model="ruleForm.status"
                placeholder="请选择事件状态"
              >
                <el-option
                  v-for="item in statusoptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="涉事企业" prop="companyId" required>
              <el-input v-model="ruleForm.companyId" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="事件地址" prop="address" required>
              <el-input v-model="ruleForm.address" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="事件位置" required>
              <el-row :gutter="24" style="width: 100%">
                <el-col :span="11">
                  <el-form-item prop="longitude">
                    <el-input v-model="ruleForm.longitude" />
                  </el-form-item>
                </el-col>
                <el-col :span="11">
                  <el-form-item prop="latitude">
                    <el-input v-model="ruleForm.latitude" />
                  </el-form-item>
                </el-col>
                <el-col :span="2">
                  <div class="positionPick" @click="positionPick"></div>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="采取措施" prop="accidentOverview" required>
              <el-input v-model="ruleForm.accidentOverview" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="轻伤人数" prop="minorInjuriesNumber" required>
              <el-input v-model="ruleForm.minorInjuriesNumber" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="重伤人数" prop="seriouslyInjuredNumber" required>
              <el-input v-model="ruleForm.seriouslyInjuredNumber" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="死亡人数" prop="finalDeathToll" required>
              <el-input v-model="ruleForm.finalDeathToll" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 10px">
          <div class="dialogbutton">
            <div
              class="masbutton cancer"
              v-if="suposeVisible"
              @click="errorsetForm(ruleFormRef)"
            >
              <span>误报</span>
            </div>
            <div
              class="masbutton cancer"
              v-if="!suposeVisible"
              @click="resetForm(ruleFormRef)"
            >
              <span>取消</span>
            </div>
            <div class="masbutton submit" @click="submitForm(ruleFormRef)">
              <span>确认</span>
            </div>
          </div>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup>
import { parseTime } from "@/utils/ruoyi";
import { addEvents } from "@/api/command/index";
import { ElMessage } from "element-plus";
const props = defineProps({
  WarningList: {
    type: Array,
    required: true,
    default: [],
  },
});
const listRef = ref(null);
const emit = defineEmits(["handle"]);
const labelPosition = ref("left");
const formSize = ref("default");
const ruleFormRef = ref();
let entity = reactive({});
let pointpick_handler = reactive({});
let dialogTableVisible = ref(false);
let suposeVisible = ref(false);
const statusoptions = ref([
  {
    label: "有升级风险",
    value: "1",
    disabled: false,
  },
  {
    label: "有次生衍生事故风险",
    value: "2",
    disabled: false,
  },
  {
    label: "已基本控制",
    value: "3",
    disabled: false,
  },
]);
const typeOfAccidentOptions = ref([
  {
    value: "自然灾害",
    label: "自然灾害",
    children: [
      {
        value: "地震灾害",
        label: "地震灾害",
      },
      {
        value: "洪涝灾害",
        label: "洪涝灾害",
      },
      {
        value: "森林火灾",
        label: "森林火灾",
      },
      {
        value: "水库溃坝",
        label: "水库溃坝",
      },
      {
        value: "滑坡事件",
        label: "滑坡事件",
      },
      {
        value: "泥石流事件",
        label: "泥石流事件",
      },
      {
        value: "地质灾害",
        label: "地质灾害",
      },
      {
        value: "城市内涝",
        label: "城市内涝",
      },
      {
        value: "台风事件",
        label: "台风事件",
      },
      {
        value: "草原火灾",
        label: "草原火灾",
      },
      {
        value: "暴雨事件",
        label: "暴雨事件",
      },
    ],
  },
  {
    value: "事故灾难",
    label: "事故灾难",
    children: [
      {
        value: "煤矿事故",
        label: "煤矿事故",
      },
      {
        value: "火灾事故",
        label: "火灾事故",
      },
      {
        value: "交通事故",
        label: "交通事故",
      },
      {
        value: "燃气泄漏事故",
        label: "燃气泄漏事故",
      },
      {
        value: "危化品事故",
        label: "危化品事故",
      },
      {
        value: "工贸事故",
        label: "工贸事故",
      },
      {
        value: "非煤矿山事故",
        label: "非煤矿山事故",
      },
    ],
  },
  {
    value: "公共卫生事件",
    label: "公共卫生事件",
  },
  {
    value: "公共安全事件",
    label: "公共安全事件",
    children: [
      {
        value: "涉外突发事件",
        label: "涉外突发事件",
      },
    ],
  },
]);
const ruleForm = reactive({
  pickUpPerson: "",
  accidentName: "",
  policeTime: "",
  startTime: "",
  callThePolice: "",
  policeCall: "",
  accidentDevice: "",
  hazardousChemicals: "",
  typeOfAccident: "",
  status: "",
  companyId: "",
  address: "",
  longitude: "",
  latitude: "",
  accidentOverview: "",
  minorInjuriesNumber: "",
  seriouslyInjuredNumber: "",
  finalDeathToll: "",
  createDate: "",
  updateDate: "",
  createBy: "",
  updateBy: "",
  deleted: "",
});
const initentity = () => {
  entity = window.viewer.entities.add({
    label: {
      show: true,
    },
    point: {
      pixelSize: 5,
      color: Cesium.Color.RED,
    },
  });
};
const rules = reactive({
  name: [
    { required: true, message: "Please input Activity name", trigger: "blur" },
    { min: 3, max: 5, message: "Length should be 3 to 5", trigger: "blur" },
  ],
});

const cascaderhandleChange = (value) => {
  // console.log(value);
  ruleForm.typeOfAccident = value[1];
};
const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      initformAdd();
      ruleForm.startTime = parseTime(ruleForm.startTime);
      ruleForm.policeTime = parseTime(ruleForm.policeTime);
      // console.log(ruleForm, "submit!");
      addEvents(ruleForm).then((res) => {
        if (res.code === 200) {
          ElMessage({
            message: "事件上报成功！",
            type: "success",
          });
          formEl.resetFields();
          dialogTableVisible.value = false;
        }
      });
    } else {
      console.log("error submit!", fields);
    }
  });
};
const initformAdd = () => {
  ruleForm.createDate = ruleForm.createDate ? ruleForm.createDate : parseTime(Date.now());
  ruleForm.updateDate = parseTime(Date.now());
  ruleForm.createBy = "admin";
  ruleForm.updateBy = "admin";
  ruleForm.deleted = "0";
  ruleForm.serialCode = "1234";
  ruleForm.status = "0";
  ruleForm.affiliates = "123";
  ruleForm.accidentSceneImg = "0";
  ruleForm.accidentSceneImgName = "0";
  ruleForm.dispositionStatus = 1;
};
const errorsetForm = (formEl) => {
  dialogTableVisible.value = false;
  if (!formEl) return;
  formEl.resetFields();
};

const resetForm = (formEl) => {
  dialogTableVisible.value = false;
  if (!formEl) return;
  formEl.resetFields();
};

const peoplehandle = () => {
  suposeVisible.value = false;
  dialogTableVisible.value = true;
};

const supose = (item) => {
  ruleForm.pickUpPerson = "田东飞";
  ruleForm.accidentName = "SH_圣御工业总部园苯泄露";
  ruleForm.policeTime = "2024-05-09 09:28:53";
  ruleForm.startTime = "2024-05-08 00:00:00";
  ruleForm.callThePolice = "张养浩";
  ruleForm.policeCall = "15036985744";
  ruleForm.accidentDevice = "苯储罐";
  ruleForm.hazardousChemicals = "苯";
  ruleForm.typeOfAccident = "危化品事故";
  ruleForm.status = "1";
  ruleForm.companyId = "中国石化油田分公司物资供应管理中心";
  ruleForm.address = "上海市浦东新区海松路与金钻路交叉路口往东约50米";
  ruleForm.longitude = "121.446462";
  ruleForm.latitude = "30.780995";
  ruleForm.accidentOverview =
    "(1)  厂区人员疏散\n(2)  建立警戒区域\n(3)  消防人员到场\n(4)  现场火情控制";
  ruleForm.minorInjuriesNumber = "1";
  ruleForm.seriouslyInjuredNumber = "1";
  ruleForm.finalDeathToll = "0";
  ruleForm.createDate = "2024-05-09 09:28:53";
  ruleForm.updateDate = "2024-05-09 09:28:53";
  suposeVisible.value = true;
  dialogTableVisible.value = true;
};

const positionPick = () => {
  dialogTableVisible.value = false;
  pointpick_handler = new Cesium.ScreenSpaceEventHandler(window.viewer.scene.canvas);
  pointpick_handler.setInputAction((click) => {
    const cartesian = window.viewer.camera.pickEllipsoid(
      click.position,
      viewer.scene.globe.ellipsoid
    );
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    ruleForm.longitude = Number(Cesium.Math.toDegrees(cartographic.longitude)).toFixed(6);
    ruleForm.latitude = Number(Cesium.Math.toDegrees(cartographic.latitude)).toFixed(6);
    dialogTableVisible.value = true;
    entity.show = true;
    entity.position = cartesian;
    removehandler();
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
const removehandler = () => {
  entity.show = false;
  pointpick_handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
onMounted(() => {
  emitter.on("viewerLoad", (data) => {
    initentity();
  });
  if (window.viewer) {
    initentity();
  }

  const listDom = listRef.value;
  let isScroll = true;
  const scrollContent = () => {
    if (isScroll) {
      listDom.scrollTop += 1;
      if (listDom.scrollTop + listDom.clientHeight >= listDom.scrollHeight) {
        listDom.scrollTop = 0;
      }
    }
  };
  const intervalId = setInterval(scrollContent, 100);
  listDom.addEventListener("mouseover", () => {
    isScroll = false;
  });
  listDom.addEventListener("mouseout", () => {
    isScroll = true;
  });
  onUnmounted(() => {
    clearInterval(intervalId);
  });
});
</script>

<style scoped lang="scss">
.warning {
  width: 400px;
  height: 260px;
  font-size: 14px;
  color: #ffffff;
  padding: 10px;
}
.warning-button {
  height: 100px;
  display: flex;
  justify-content: space-evenly;
  .warning-button-img {
    width: 80px;
    height: 90px;
    background-image: url("@/assets/xtui/command/onduty/iconimg.png");
  }
  .warning-button-click {
    width: 100px;
    height: 100px;
    background-image: url("@/assets/xtui/command/onduty/buttonimg.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    text-align: center;
    display: flex;
    align-content: center;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    cursor: pointer;
  }
}
.warning-ref {
  width: 380px;
  height: 240px;
  overflow-y: auto;
}
.warning-items {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-bottom: 10px;
  height: 50px;
  font-size: 14px;
  font-family: sans-serif;
  font-weight: 700;
  background: url("@/assets/xtui/command/onduty/warningback.png");
  .warning-items-words {
    width: 170px;
    margin-left: 50px;
    text-align: center;
    .warning-item-title {
      color: rgb(60, 213, 255);
      white-space: nowrap; /* 禁止换行 */
      overflow: hidden; /* 隐藏溢出内容 */
      text-overflow: ellipsis; /* 显示省略号 */
      width: 150px; /* 设置容器宽度 */
    }
    .warning-item-address {
      text-align: justify;
    }
  }
  .warning-items-date {
    font-size: 12px;
    width: 70px;
    // color: #97fff6;
    text-align: center;
    word-break: break-all;
  }
  .warning-items-click {
    width: 60px;
    height: 40px;
    background-image: url("@/assets/xtui/command/onduty/buttonimg.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    text-align: center;
    display: flex;
    align-content: center;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 5px;
    color: rgb(77, 225, 255);
    cursor: pointer;
  }
}

.dialogbutton {
  width: 100%;
  display: flex;
  justify-content: space-evenly;
  .masbutton {
    width: 100px;
    height: 32px;
    color: #ffffff;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    text-align: center;
    display: flex;
    align-content: center;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  .submit {
    background-image: url("@/assets/xtui/command/onduty/sunmitback.png");
  }
  .cancer {
    background-image: url("@/assets/xtui/command/onduty/cancleback.png");
  }
}

.positionPick {
  cursor: pointer;
  margin-left: 20px;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
  background-image: url("@/assets/xtui/command/onduty/positionpick.png");
}
</style>
