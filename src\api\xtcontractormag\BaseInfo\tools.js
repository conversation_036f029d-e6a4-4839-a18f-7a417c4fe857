import request from '@/utils/request'

// 查询承包商工器具列表
export function listTools(query) {
  return request({
    url: '/contractor/tools',
    method: 'get',
    params: query
  })
}

// 查询承包商工器具详细
export function getTools(id) {
  return request({
    url: '/contractor/tools/' + id,
    method: 'get'
  })
}

// 新增承包商工器具
export function addTools(data) {
  return request({
    url: '/contractor/tools',
    method: 'post',
    data: data
  })
}

// 修改承包商工器具
export function updateTools(data) {
  return request({
    url: '/contractor/tools',
    method: 'post',
    data: data
  })
}

// 删除承包商工器具
export function delTools(data) {
  return request({
    url: '/contractor/tools/',
    method: 'delete',
    data: data
  })
}
