// import SockJS from 'sockjs-client';  //只要引入sockjs就报错
import Stomp from 'stompjs';

export default class RabbitmqClient {
	/* 
    1. 连接服务端
    2. 监听特定交换机/队列的消息
    3. 实例化一个连接对象，通过对象进行操作
    const options = {
        rmqUrl: 'wss://park.geovisearth.com/rabbitmq/ws',
        subUrl: '/exchange/nanjingpeople',
    }; 
    const rmqObj = new RabbitmqClient(options)
    */
	constructor(options) {
		this.rmqUrl = options.rmqUrl;
		this.subUrl = options.subUrl;
	}
	initMQ(cb) {
		const ws = new WebSocket(this.rmqUrl);
		// const ws = new SockJS(this.rmqUrl);
		this.stompMQClient = Stomp.over(ws);

		this.stompMQClient.connect(
			window.xtmapConfig.rmq.username,
			window.xtmapConfig.rmq.pwd,
			this.onMQConnected(cb),
			this.onError,
			'/'
		);
		// 只有连接通道创建成功了，才有client通道对象，才能调用subscribe
	}
	onMQConnected(cb) {
		return () => {
			console.log('RabbitMQ连接成功， ----准备订阅');
			this.stompMQClient.subscribe(this.subUrl, cb, this.onError());
		};
	}
	onError(err) {
		console.log(err, 'xxx');
	}
}

// function initStompRabbitWs() {
// 	// const ws = new WebSocket('ws://10.1.196.6:15674/ws');
// 	// const ws = new WebSocket('wss://park.geovisearth.com/rabbitmq/ws');
// 	const ws = new WebSocket("ws://120.46.153.149:8085/ws");
// 	const stompMQClient = Stomp.over(ws);

// 	console.log(stompMQClient, "stompMQClient---");

// 	const onMQConnected = () => {
// 	  console.log("RabbitMQ连接-----成功");
// 	  // 订阅交换机，就是/exchange/交换机名称/routingKey (timeStamp)
// 	  // 如果订阅队列，就是/queue/队列名称(可指定/自动生成)
// 	  stompMQClient.subscribe(
// 		// '/exchange/XTEGleader/aaa',
// 		"/exchange/tempxxx",
// 		// '/exchange/tttwwwxtauto',
// 		// '/queue/xtauto',
// 		// '/wwwasdxtauto',

// 		(data) => {
// 		  // stompMQClient.subscribe('/queue/wwwwaaa', (data) => {
// 		  console.log(data, "nnnnnwwwwwwqqq");
// 		  /* const res = data.body;
// 					  // 消息体
// 					  const entity = JSON.parse(res);
// 					  const arr = [entity.content];
// 					  setBulletList((pre) => [].concat(...pre, ...arr));
// 					  // 消息确认
// 					  data.ack(); */
// 		},
// 		onError
// 		// { ack: 'client' }
// 	  );
// 	};

// 	const onError = (r) => {
// 	  console.log(r, "xxxxx222222222");
// 	};
// 	// 从前往后的参数：用户名、密码、连接成功回调、连接错误回调、虚拟路径，默认/

// 	// stompMQClient.connect('rabbit', '123456', onMQConnected, onError, '/'); //星图云
// 	stompMQClient.connect("user", "user", onMQConnected, onError, "/");
// 	// stompMQClient.connect('admin', 'zkxt123465', onMQConnected, onError, '/');
//   }
