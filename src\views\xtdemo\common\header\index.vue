<template>
  <div class="header">
    <div class="name">{{ name }}</div>
    <slot name="menu" class="menu"></slot>
    <div class="three-menus">
      <div
        v-for="(item, index) in threeMenus"
        @click="changeThreeMenu(item.value, index)"
        class="three-menus-item"
        :class="{ isActiveMenu: item.value === currentMidTab }"
        :key="index"
      >
        {{ item.name }}
      </div>
    </div>
    <slot name="userInfo" class="userinfo"></slot>
  </div>
  <div class="mid-panel" v-show="isShowMidpanel">
    <el-button @click="closeMidpanel"> close</el-button>
    <keep-alive>
      <component :xttid="xttid" :is="midTabs[currentMidTab]"></component>
    </keep-alive>
  </div>
</template>

<script setup>
import { nextTick, ref, reactive, computed, onMounted } from "vue";
import DrillInfo from "@/views/xtdemo/common/mid/DrillInfo";
import CaseInfo from "@/views/xtdemo/common/mid/CaseInfo";
import ProcessInfo from "@/views/xtdemo/common/mid/ProcessInfo";
import { getMyKey } from "../js/tool";
// 获取xttid
// console.log(location.href, "location.href");
const xttid = getMyKey("xttid");
// console.log(xttid, "xttid.xttid");

const props = defineProps({
  name: {
    type: String,
    required: true,
    default: "应急综合平台",
  },
});

const isShowMidpanel = ref(false);
const currentMidTab = ref(null);
const midTabs = {
  DrillInfo,
  CaseInfo,
  ProcessInfo,
};

const threeMenus = reactive([
  {
    name: "a信息",
    value: "DrillInfo",
  },
  {
    name: "b信息",
    value: "CaseInfo",
  },
  {
    name: "c流程",
    value: "ProcessInfo",
  },
]);

function changeThreeMenu(val, i) {
  if (currentMidTab.value == val) {
    currentMidTab.value = null;
    isShowMidpanel.value = false;
  } else {
    currentMidTab.value = val;
    isShowMidpanel.value = true;
  }
}
function closeMidpanel() {
  isShowMidpanel.value = false;
  currentMidTab.value = null;
}
onMounted(() => {});
</script>

<style scoped lang="scss">
.header {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  width: 100%;
  height: 90px;
  background: url("@/assets/xtui/layout/headernewbg.png");
  background-size: 100% 100%;
  display: grid;
  grid-template-columns: 1fr 50px 2fr 1fr;
  column-gap: 20px;
  align-items: center;
  .name {
    margin-left: 20px;
    font-family: ysbthzt;
    font-size: 40px;

    letter-spacing: 0em;

    background: linear-gradient(90deg, #ffffff 58%, #97add4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    text-shadow: 0px 2px 4px #78a1c496;
  }
  .three-menus {
    z-index: 99;
    pointer-events: auto;
    width: 500px;
    height: 60px;

    display: flex;
    justify-content: space-evenly;
    align-items: center;
    background-color: rgba(160, 81, 45, 0.531);
    .three-menus-item {
      background-color: rgba(63, 165, 229, 0.305);
      padding: 10px 20px;
      cursor: pointer;
    }
    .isActiveMenu {
      background-color: rgba(63, 165, 229, 0.805);
    }
  }
}
.mid-panel {
  width: 650px;
  height: 400px;
  background-color: #97add4;
  position: absolute;
  top: 300px;
  left: 500px;
}
</style>
