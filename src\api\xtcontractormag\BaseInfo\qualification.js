import request from '@/utils/request'

// 查询承包商资质列表
export function listQualification(query) {
  return request({
    url: '/contractor/qualification',
    method: 'get',
    params: query
  })
}

// 查询承包商资质详细
export function getQualification(id) {
  return request({
    url: '/contractor/qualification/' + id,
    method: 'get'
  })
}

// 新增承包商资质
export function addQualification(data) {
  return request({
    url: '/contractor/qualification',
    method: 'post',
    data: data
  })
}

// 修改承包商资质
export function updateQualification(data) {
  return request({
    url: '/contractor/qualification',
    method: 'post',
    data: data
  })
}

// 删除承包商资质
export function delQualification(data) {
  return request({
    url: '/contractor/qualification/',
    method: 'delete',
    data: data
  })
}
