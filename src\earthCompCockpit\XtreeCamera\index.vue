<template>
  <div class="xtree-camera">
    <el-input
      v-model="filterText"
      style="width: 230px"
      placeholder="请输入监控点名称"
      class="tree-input"
    />

    <el-tree
      class="xtree-list"
      ref="cameraTreeRef"
      style="max-width: 600px"
      :data="cameraTreeData"
      :props="defaultProps"
      @node-click="handleNodeClick"
      @check-change="handleCheckChange"
      show-checkbox
      node-key="id"
      default-expand-all
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
    >
      <template #default="{ node, data }">
        <span class="custom-tree-node">
          <span class="custom-tree-node-span" v-if="!data.children.length">
            <div
              class="node-span-a"
              style="background-color: greenyellow"
              v-if="data.status === 'online'"
            ></div>
            <div class="node-span-a" style="background-color: red" v-else></div>
          </span>
          <span :title="node.label" class="span-ellipsis">{{ node.label }}</span>
        </span>
      </template>
    </el-tree>

    <div class="select">
      <div class="select-icon" @click="selectCamera('rectangle')">
        <img :src="rectangleIcon" title="矩形框选" alt="矩形框选" srcset="" />
      </div>
      <div class="select-split"></div>

      <div class="select-icon" @click="selectCamera('polygon')">
        <img :src="polygonIcon" title="多边形框选" alt="多边形框选" srcset="" />
      </div>
      <div class="select-split"></div>

      <div class="select-icon" @click="selectCamera('circle')">
        <img :src="targetIcon" title="圆形框选" alt="圆形框选" srcset="" />
      </div>
      <div class="select-split"></div>

      <div class="select-icon" @click="clearSelect()">
        <img :src="clearIcon" title="清除框选" alt="清除框选" srcset="" />
      </div>
    </div>
  </div>
  <PopupSlot
    v-if="isPopShow"
    ref="popupRef"
    id="popup-info-xtree-c"
    @closePopup="closePopup"
    popupTitle="资源详情"
  >
    <template #content>
      <div class="video-player">
        <easy-player
          v-if="isPopShow"
          :video-url="videoUrl"
          :live="true"
          style="width: 100%; height: 100%"
        />
      </div>
    </template>
  </PopupSlot>
</template>

<script setup>
import { nextTick, ref, reactive, computed, onMounted } from "vue";

import { ElLoading, ElMessage } from "element-plus";
import PopupSlot from "./PopupSlot";
import PoiUtils from "./PoiUtilsTree.js";
import {
  getVideoTree,
  getVideoUrl,
  getVideoListParams,
  getVideoListData,
} from "@/api/tree/index";

import { PlotPolygon } from "./queryJS/polygon";
import { PlotRectangle } from "./queryJS/rectangle.js";
import { PlotCircle } from "./queryJS/circle.js";

import targetIcon from "@/assets/xtui/tools/target.png";
import rectangleIcon from "@/assets/xtui/tools/rectangle.png";
import polygonIcon from "@/assets/xtui/tools/polygon.png";
import clearIcon from "@/assets/xtui/tools/clear.png";

// ---------------------- 筛选框 ------------------------------
const { proxy } = getCurrentInstance();
const filterText = ref("");
const cameraTreeRef = ref();
watch(filterText, (val) => {
  cameraTreeRef.value.filter(val);
  // console.log(cameraTreeRef.value, "cameraTreeRef.value");
});
const filterNode = (value, data) => {
  // console.log(data, "data"); //每个节点（包括父节点和叶子节点）的单条数据

  if (!value) return true;
  return data.name.includes(value);
};

// ---------------------- 数据列表 ------------------------------

const cameraTreeData = ref([]);
const defaultProps = {
  children: "children",
  label: "name",
};
getVideoTree().then((res) => {
  cameraTreeData.value = res.data;
});

// ---------------------- 点击事件 --------------------

const handleNodeClick = (data, node, event) => {
  // console.log(data, node, event);
  // currentCarInfo.driver = data.driver;
  // currentCarInfo.type = data.type;
};

const handleCheckChange = (data, checked, indeterminate) => {
  // console.log("Node:", data);
  // console.log("Checked:", checked);
  if (checked && !data.children.length && data.uid) {
    data.id = data.uid;
    const e = PoiUtils.createPoi(data, "xtree-c-type", "camera");
    xtreeCameraCustomDS.entities.add(e);
    viewer.flyTo(e);
  } else if (!checked && !data.children.length && data.uid) {
    // 当节点取消选中且没有子节点时，移除相关数据源
    xtreeCameraCustomDS.entities.removeById(data.uid);
    isPopShow.value = false;
    postRenderingFn?.();
  }
};

// ---------------------- popslot --------------------
const isPopShow = ref(false);
const popupItem = ref({});
let postRenderingFn = reactive();

function eventCallbackXtree(viewer, pick) {
  if (
    !pick.id.properties ||
    !pick.id.properties.type._value ||
    pick.id.properties.type._value !== "xtree-c-type"
  ) {
    return;
  }

  const item = pick.id.properties.cameraItem._value;
  getHLSURL(item).then((r) => {
    // r.data.url
    // r.data.stream_id
    popupItem.value = {};
    // popupItem.value = r;
    videoUrl.value = r.data.url;

    isPopShow.value = true;
    nextTick(() => {
      const popDom = window.document.querySelector("#popup-info-xtree-c");
      const height = popDom.offsetHeight;
      const width = popDom.offsetWidth;
      postRenderingFn?.();
      postRenderingFn = viewer.scene.postRender.addEventListener(() => {
        const screenC = viewer.scene.cartesianToCanvasCoordinates(
          pick.id.position._value
        );
        if (screenC) {
          popDom.style.left = screenC.x - 1 + "px";
          popDom.style.top = screenC.y - height - 45 + "px";
        }
      });
    });
  });
}

let hls = null;
const videoUrl = ref("");
// 获取HLS视频流地址
const getHLSURL = async (item) => {
  // console.log(item, "item");

  try {
    const params = {
      devicePath: `${item.device_path}/${item.uid}`,
      disableAudio: true,
      request: "open.video.HLS",
      videoQuality: 1,
    };
    const res = await getVideoUrl(params);

    return res;
  } catch (err) {
    ElMessage({
      message: "设备离线！",
      type: "error",
      plain: true,
    });
  }
};

function closePopup() {
  postRenderingFn?.();
  isPopShow.value = false;
}
// const resultInfo = ref();

// ===============================vue hook================================

let xtreeCameraCustomDS = null;

function czInitFn() {
  xtreeCameraCustomDS = new Cesium.CustomDataSource("xtree-r-data");
  window.viewer.dataSources.add(xtreeCameraCustomDS);
  // xtreeCameraCustomDS.entities.add(tempPoint);
  // xtreeCameraCustomDS.entities.removeAll();
}

onMounted(() => {
  /* emitter.on("viewerLoad", () => {
    console.log("oooo--111111---");
  });
  console.log("oooo-222222----"); */
  czInitFn();
  // PoiUtils.addListener(window.viewer, eventCallbackXtree);
});

onActivated(() => {
  // if (PoiUtils.getListener()) return;
  PoiUtils.addListener(window.viewer, eventCallbackXtree);
  // console.log(PoiUtils.getListener(), "onActivated");
});

onDeactivated(() => {
  if (isPopShow.value) isPopShow.value = false;

  PoiUtils.removeListener();
  // console.log(PoiUtils.getListener(), "onDeactivated");

  // if (typeof postRenderingFn === "function" && postRenderingFn())
  postRenderingFn?.();

  // 关闭面板要不要清空地图点？如果要清空，那就在mounted里创建事件
  // xtreeCameraCustomDS.entities.removeAll();

  // 关闭面板如何清空树节点？
});

// ================================筛选================================

function initHandler() {
  // const plotPolygon = ref();
  // const plotRectangle = ref();
  // const plotCircle = ref();
  // plotRectangle.value = new PlotRectangle(viewer);
  // plotPolygon.value = new PlotPolygon(viewer);
  // plotCircle.value = new PlotCircle(viewer);
}
let drawObj = null;
const drawObjRectangle = new PlotRectangle(viewer);
const drawObjCircle = new PlotCircle(viewer);
const drawObjPolygon = new PlotPolygon(viewer);
const queryResult = ref([]);

function selectCamera(type) {
  let params = {};
  switch (type) {
    case "rectangle":
      drawObj = drawObjRectangle;
      drawObj.draw().then((res) => {
        params = { point: res.position };
        getVideoListData(params).then((res) => {
          if (res.rows.length === 0) {
            ElMessage({
              message: `该区域内暂无数据`,
              type: "warning",
              plain: true,
            });
          } else {
            queryResult.value = res.rows;
            for (const i of queryResult.value) {
              const e = PoiUtils.createPoi(i, "xtree-c-type", "camera");
              xtreeCameraCustomDS.entities.add(e);
            }
          }
        });
      });
      break;

    case "polygon":
      drawObj = drawObjPolygon;
      drawObj.draw().then((res) => {
        params = { point: res.position };
        getVideoListData(params).then((res) => {
          if (res.rows.length === 0) {
            ElMessage({
              message: `该区域内暂无数据`,
              type: "warning",
              plain: true,
            });
          } else {
            queryResult.value = res.rows;
            for (const i of queryResult.value) {
              const e = PoiUtils.createPoi(i, "xtree-c-type", "camera");
              xtreeCameraCustomDS.entities.add(e);
            }
          }
        });
      });

      break;
    case "circle":
      drawObj = drawObjCircle;
      drawObj.draw().then((res) => {
        params = res.params;
        getVideoListParams(params).then((res) => {
          if (res.rows.length === 0) {
            ElMessage({
              message: `该区域内暂无数据`,
              type: "warning",
              plain: true,
            });
          } else {
            queryResult.value = res.rows;
            for (const i of queryResult.value) {
              const e = PoiUtils.createPoi(i, "xtree-c-type", "camera");
              xtreeCameraCustomDS.entities.add(e);
            }
          }
        });
      });
      break;

    default:
      break;
  }
}
function clearSelect() {
  drawObjRectangle.clearData();
  drawObjCircle.clearData();
  drawObjPolygon.clearData();
  if (isPopShow.value) isPopShow.value = false;
  postRenderingFn?.();

  xtreeCameraCustomDS.entities.removeAll();
}
// ======================================================================

function MSG(item) {
  const loading = ElLoading.service({
    lock: true,
    text: "获取GPS信息中...请稍等",
    background: "rgba(0, 0, 0, 0.7)",
  });

  const p = {
    start: proxy
      .dayjs(new Date().getTime() - 24 * 60 * 60 * 1000)
      .format("YYYY-MM-DD HH:mm:ss"),
    end: proxy.dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
  };
  getCarGPS(p).then((res) => {
    // console.log(res);
    loading.close();
    if (res.records.length === 0) {
      // alert("暂无数据");
      ElMessage({
        message: `暂无GPS位置轨迹信息`,
        type: "warning",
        plain: true,
      });
      return;
    }
  });
}
</script>

<style scoped lang="scss">
.xtree-camera {
  padding: 0 10px 10px 10px;
  width: 250px;
  // height: 280px;
  // background-color: rgba(100, 148, 237, 0.534);
  .select {
    border: 1px solid #2a8bda;
    display: flex;
    justify-content: space-evenly;
    padding: 10px 5px;
    margin-top: 5px;
    align-items: center;
    .select-icon {
      background-color: rgba(222, 184, 135, 0);
      display: flex;
      align-items: center;

      &:hover {
        cursor: pointer;
      }
      img {
        width: 28px;
        height: 28px;
      }
    }
    .select-split {
      height: 30px;
      widows: 1px;
      border: 1px solid #c7ced2a7;
    }
  }

  .tree-input {
    margin-bottom: 10px;
    :deep(.el-input__wrapper) {
      background-color: transparent;
      border: 1px solid rgb(90, 174, 226);
      box-shadow: none;
      border-radius: 0;
    }
  }
  .xtree-list {
    height: 300px;
    overflow: scroll;
  }
  :deep(.el-tree) {
    background-color: rgba(0, 51, 255, 0);

    padding: 13px;

    overflow: auto;
    :deep(.el-tree-node__label) {
      font-size: 16px;
      color: rgb(255, 255, 255);
    }
  }

  :deep(.el-tree-node__content) {
    background-color: rgba(137, 43, 226, 0);
  }
  :deep(.el-tree-node.is-current > .el-tree-node__content) {
    /* 当前选中后的颜色 */
    background-color: rgba(48, 138, 234, 0.356);
  }
  :deep(.el-tree-node .el-tree-node__content .el-tree-node__label) {
    color: rgb(255, 255, 255);
  }
  :deep(.el-tree-node .el-tree-node__content:hover) {
    /* 鼠标浮动的颜色 */
    background-color: rgba(37, 107, 183, 0.2);
    /* background-color: rgba(37, 107, 183, 0.469); */
  }
  :deep(.el-tree-node.is-current.is-focusable:hover) {
    /* 鼠标浮动的颜色 */
    background-color: rgba(135, 183, 234, 0);
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    // font-size: 16px;
    padding-right: 8px;
    color: white;
    .custom-tree-node-span {
      // display: flex;
      // align-items: center;
      color: white;
      .node-span-a {
        width: 10px;
        height: 10px;
        margin-right: 5px;
        border-radius: 50%;
      }
    }
    .span-ellipsis {
      width: 90px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
.video-player {
  width: 375px;
  height: 160px;
}
</style>
