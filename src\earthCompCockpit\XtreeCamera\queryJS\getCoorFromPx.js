/**
 * @description: 获取地球坐标
 * @param {obj} viewer viewer对象
 * @param {e} pxCoor e.position
 * @param {string} resultType 设置返回值的显示格式是经纬度还是xyz(cartesian)
 * @return { obj } 返回的默认格式：{ lon: lon, lat: lat, height: height }
 */

// 外层必须传 viewer和e ,所以没有设置fn默认值
export function getCoorFromPx({ resultType = 'jwd', viewer, pxCoor }) {
	//判定标识
	let isOnOsgb;
	let isTerrainOpen;

	let cartesian = null;
	let jwdCoor = null;
	let xyzCoor = null;

	let pick = viewer.scene.pick(pxCoor);

	// 应该用drill pick,获取所有通过点击捕获到的实体,如果有模型,就获取模型，
	// 否则，当模型如果处在地面以下一部分时，pick是会直接判定为点击到地面

	// 有无模型
	if (
		(pick && pick.primitive instanceof Cesium.Cesium3DTileFeature) ||
		(pick && pick.primitive instanceof Cesium.Cesium3DTileset) ||
		(pick && pick.primitive instanceof Cesium.Model)
	) {
		isOnOsgb = true;
	}
	// 有无地形,Cesium.EllipsoidTerrainProvider是用户不加载地形时，cz默认的空地形,所以t时无 , f时有
	if (viewer.terrainProvider instanceof Cesium.EllipsoidTerrainProvider) {
		isTerrainOpen = false;
	} else {
		isTerrainOpen = true;
	}
	if (isOnOsgb) {
		cartesian = viewer.scene.pickPosition(pxCoor);
	} else {
		if (isTerrainOpen) {
			let ray = viewer.scene.camera.getPickRay(pxCoor);
			if (!ray) return;
			cartesian = viewer.scene.globe.pick(ray, viewer.scene);
		} else {
			cartesian = viewer.scene.camera.pickEllipsoid(
				pxCoor,
				viewer.scene.globe.ellipsoid
			);
		}
	}
	if (cartesian) {
		let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
		let lon = parseFloat(
			Cesium.Math.toDegrees(cartographic.longitude).toFixed(6)
		);
		let lat = parseFloat(
			Cesium.Math.toDegrees(cartographic.latitude).toFixed(6)
		);
		let height = cartographic.height > 0 ? cartographic.height : 0.1; // 模型高度

		jwdCoor = { lon: lon, lat: lat, height: height };
		xyzCoor = cartesian;

		return resultType === 'xyz' ? xyzCoor : jwdCoor;

		/* let position = transformCartesianToWGS84(viewer, cartesian);
		if (position.alt < 0) {
			coor = transformWGS84ToCartesian(viewer, position, 0.1);
		} */
	}
	return null;
}

export function cart3ToDegree(dataArr) {
	const data = [];
	let jwdCoor = [];
	for (const i of dataArr) {
		let cartographic = Cesium.Cartographic.fromCartesian(i);
		let lon = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
		let lat = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);

		jwdCoor = [parseFloat(lon), parseFloat(lat)];
		data.push(jwdCoor);
	}

	return data;
}

export function pointsToDegreesArray(points) {
	const degreesArray = [];
	points.map((item) => {
		degreesArray.push(item[0]);
		degreesArray.push(item[1]);
	});
	return degreesArray;
}

/* 
const handler = new Cesium.ScreenSpaceEventHandler(viewer.canvas);
return new Promise((resolve, reject) => {
	handler.setInputAction((e) => {
		let posi = e.position;
		let pickedObj = scene.pick(e.position);

		let coor = getCatesian3FromPX(viewer, endPos);

		resolve(coor);
		reject('--err--');
	}, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}); 
 
handler.setInputAction(function (movement) {
    let endPos = movement.endPosition;
    CreateRemindertip(toolTip, endPos, true);
    if (Cesium.defined(polyline)) {
    anchorpoints.pop();
    let cartesian = getCatesian3FromPX(viewer, endPos);
    anchorpoints.push(cartesian);
    }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE); 
*/
