<script setup>
import { MeasureHeight, Measure<PERSON><PERSON>, MeasureLength } from "./bd";

import { nextTick, ref, reactive, computed, onMounted } from "vue";

function initMeasureTool() {
  window.dheight = new MeasureHeight(window.viewer);
  window.darea = new MeasureArea(window.viewer);
  window.dlength = new MeasureLength(window.viewer);
}
function resetHandler() {
  dheight.resetHandler();
  darea.resetHandler();
  dlength.resetHandler();
}

function ceGao() {
  resetHandler();
  dheight.reset();
  dheight.createEvent();
}
function ceMian() {
  resetHandler();
  darea.reset();
  darea.createEvent();
}
function ceJu() {
  resetHandler();
  dlength.reset();
  dlength.createEvent();
}
function clearAll() {
  dlength.reset();
  darea.reset();
  dheight.reset();
}

onMounted(() => {
  initMeasureTool();
});
</script>

<template>
  <!-- <div class="measure">
    <div class="measure-header">
      <span>地图测量</span>
    </div>
    <div class="measure-menu">
      <el-button class="itembuton" type="primary" plain @click="ceJu">测距</el-button>
      <el-button class="itembuton" type="primary" plain @click="ceMian">测面</el-button>
      <el-button class="itembuton" type="primary" plain @click="ceGao">测高</el-button>
      <el-button class="itembuton" type="primary" plain @click="clearAll">清除</el-button>
    </div>
  </div> -->
  <div class="measure-menu">
    <el-button class="itembuton" type="primary" plain @click="ceJu"
      >测距</el-button
    >
    <el-button class="itembuton" type="primary" plain @click="ceMian"
      >测面</el-button
    >
    <el-button class="itembuton" type="primary" plain @click="ceGao"
      >测高</el-button
    >
    <el-button class="itembuton" type="primary" plain @click="clearAll"
      >清除</el-button
    >
  </div>
</template>

<style scoped lang="scss">
.measure-menu {
  // padding: 10px 10px;
  width: 320px;
  height: 50px;
  display: flex;
  justify-content: space-evenly;
  margin-top: 25px;

  .itembuton {
    background-color: #3f5e6c;
    color: white;
  }
}
.measure {
  width: 300px;
  height: 180px;
  // background: url("@/assets/xtui/tools/dialogback.png");
  // background-size: 100% 100%;
  // display: flex;
  // flex-direction: column;
  // color: antiquewhite;
  // font-family: ysbthzt;
  background: rgba(8, 76, 124, 0.5);
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%)
    2;
  backdrop-filter: blur(10px);
  padding: 8px;
  .measure-header {
    background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center
      center;
    background-size: 100% 100%;
    height: 32px;
    width: 100%;
    margin: 5px 10px;
    padding: 5px 30px;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0px;
    color: aliceblue;
  }
  .measure-menu {
    // padding: 10px 10px;
    width: 200px;
    height: 150px;

    .itembuton {
      background-color: #3f5e6c;
      color: white;
    }
  }
}
</style>
