import request from '@/utils/request';

//获取用户id
export default  function getTaskInfo(taskId) {
	return request({
		url: `drill/task/${taskId}`,
		method: 'get',
		// params: params,
	});
}


// `/drill/forward`, messageData
//转发
export   function getForward(messageData) {
	return request({
		url: `/drill/forward`,
		method: 'post',
		data: messageData,
	});
}
 // 新增推演记录
 export   function gethistory(recordData) {
	return request({
		url: `/drill/history`,
		method: 'post',
		data: recordData,
	});
}