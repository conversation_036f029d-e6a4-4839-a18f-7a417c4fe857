<template>
  <div class="popup">
    <div class="popup-header">
      <div class="popup-header-title">{{ poptitle }}</div>
      <div class="popup-header-close" @click="closePopup"></div>
    </div>
    <div class="popup-content">
      <el-form
        ref="ruleFormRef"
        style="max-width: 98%"
        :model="popInfo"
        label-width="auto"
        :label-position="labelPosition"
        :size="formSize"
      >
        <el-row :gutter="20">
          <el-col v-for="(field, index) in fields" :key="index" :span="24">
            <el-form-item :label="field.label">
              <el-input
                v-if="poptitle != '应急专家'"
                v-model="popInfo[field.prop]._value"
              />
              <el-input v-else v-model="popInfo[field.prop]" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="popup-footer">
      <div
        v-for="item in popFooterList"
        class="popup-footer-img"
        :class="item.imgurl"
        @click="popClick(item.imgurl)"
      ></div>
    </div>
  </div>
</template>

<script setup>
// 从父组件接收属性
const props = defineProps({
  poptitle: {
    type: String,
    default: "应急信息",
  },
  popInfo: {
    type: Object,
    default: () => ({}),
  },
  fields: {
    type: Array,
    default: () => [],
  },
});
const popFooterList = ref([
  {
    id: 1,
    imgurl: "messageicon",
  },
  {
    id: 2,
    imgurl: "callicon",
  },
  {
    id: 3,
    imgurl: "videoicon",
  },
  {
    id: 4,
    imgurl: "okicon",
  },
]);
// 表单属性设置
const labelPosition = ref("left");
const formSize = ref("default");
const ruleFormRef = ref();
const popClick = (type) => {
  switch (type) {
    case "messageicon":
      break;
    case "callicon":
      break;
    case "videoicon":
      break;
    case "okicon":
      emitter.emit("popInsurece", props.popInfo);
      break;
    default:
      break;
  }
};
// 定义事件
const emits = defineEmits(["closePopup"]);
const closePopup = () => {
  emits("closePopup");
};
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  width: 410px;
  // height: 404px;
  min-height: 400px;
  height: max-content;
  padding: 10px;
  opacity: 1;
  background: rgba(8, 76, 124, 0.5);
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%) 2;
  backdrop-filter: blur(10px);
  right: 770px;
  top: 101px;
  .popup-header {
    display: flex;
    justify-content: space-between;
    .popup-header-title {
      font-size: medium;
      padding-right: 50px;
      color: #ffffff !important;
      width: 180px;
      height: 36px;
      background-size: contain;
      background-repeat: no-repeat;
      background-image: url("@/assets/xtui/command/onduty/dialogtitle.png");
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: nowrap;
      flex-direction: row;
    }
    .popup-header-close {
      color: #ffffff00;
      width: 25px;
      height: 25px;
      background-size: contain;
      background-repeat: no-repeat;
      cursor: pointer;
      background-image: url("@/assets/xtui/command/onduty/closeicon.png");
    }
  }
  .popup-content {
    padding: 20px 25px 20px 25px;
    // min-height: 280px;
    // max-height: 280px;
    height: 280px;
    overflow-y: auto;
  }
  .popup-footer {
    display: flex;
    justify-content: space-evenly;
    .popup-footer-img {
      width: 46px;
      height: 46px;
      background-size: contain;
      background-repeat: no-repeat;
      cursor: pointer;
      &.messageicon {
        background-image: url("@/assets/xtui/command/command/messageicon.png");
      }
      &.callicon {
        background-image: url("@/assets/xtui/command/command/callicon.png");
      }
      &.videoicon {
        background-image: url("@/assets/xtui/command/command/videoicon.png");
      }
      &.okicon {
        background-image: url("@/assets/xtui/command/command/okicon.png");
      }
    }
  }
}
</style>
