<template>
  <div class="analyse">
    <div class="analyse-sub">
      <div class="analyse-header">空间分析</div>
      <div class="dialogbutton">
        <div class="masbutton cancer" @click="Explosion()">
          <span>爆炸分析</span>
        </div>
        <div class="masbutton submit" @click="Dustexplosions()">
          <span>粉尘爆炸</span>
        </div>
      </div>
    </div>
    <explosion v-if="isShow" />
    <dustexplosions v-if="isShowdust" />
  </div>
</template>
<script setup>
import explosion from "@/earthCompStyle/spatialAnalysis/Explosion.vue";
import dustexplosions from "@/earthCompStyle/spatialAnalysis/Dustexplosions.vue";
const isShow = ref(false);
const isShowdust = ref(false);
const Explosion = () => {
  isShowdust.value = false;
  isShow.value = !isShow.value;
};
const Dustexplosions = () => {
  isShow.value = false;
  isShowdust.value = !isShowdust.value;
};
</script>
<style scoped lang="scss">
//将背景和高斯模糊全部设置在了伪元素内，并让伪元素的z-index为-1，避免遮盖其他元素
.analyse::before {
  content: "";
  position: absolute;
  width: 222px;
  min-height: 328px;
  height: max-content;
  z-index: -1;
  background: rgba(8, 76, 124, 0.5);
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%) 2;
  backdrop-filter: blur(10px);
  // top: -10px;
}

.analyse {
  width: 222px;
  min-height: 328px;
  height: max-content;
  box-sizing: border-box;

  .analyse-sub {
    padding: 10px;
    .analyse-header {
      background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center center;
      background-size: 100% 100%;
      width: 100%;
      height: 32px;
      margin: 5px 0px 15px 0px;
      padding: 5px 30px;
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: bold;
      letter-spacing: 0px;
      color: aliceblue;
    }
    .dialogbutton {
      width: 100%;
      height: 200px;
      display: flex;
      justify-content: space-around;
      flex-direction: column;
      align-items: center;
      .masbutton {
        width: 100px;
        height: 32px;
        color: #ffffff;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        text-align: center;
        display: flex;
        align-content: center;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
      .submit {
        background-image: url("@/assets/xtui/command/onduty/sunmitback.png");
      }
      .cancer {
        background-image: url("@/assets/xtui/command/onduty/cancleback.png");
      }
    }
  }
}
</style>
