<template>
  <div class="router">
    <div class="router-path">
      <div class="router-header">路径规划</div>
      <div class="router-menu">
        <div class="router-menu-point">
          <span>起点</span>
          <el-input
            v-model="startPoint.longitude"
            style="width: 120px; height: 32px"
            placeholder="请输入经度"
          />
          <el-input
            v-model="startPoint.latitude"
            style="width: 120px; height: 32px"
            placeholder="请输入纬度"
          />
          <div class="positionPick" @click="positionPick('start')"></div>
        </div>

        <div class="router-menu-point">
          <span>终点</span>
          <el-input
            v-model="endPoint.longitude"
            style="width: 120px; height: 32px"
            placeholder="请输入经度"
          />
          <el-input
            v-model="endPoint.latitude"
            style="width: 120px; height: 32px"
            placeholder="请输入纬度"
          />
          <div class="positionPick" @click="positionPick('end')"></div>
        </div>
      </div>
    </div>
    <div class="router-position">
      <div
        style="
          width: 95%;
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div class="router-header">必经点</div>
        <div class="positionPick-add" @click="positionPick('center')"></div>
      </div>

      <div class="router-menu">
        <el-table :data="tableData" style="width: 100%" max-height="150">
          <el-table-column prop="longitude" align="center" label="经度" />
          <el-table-column prop="latitude" align="center" label="纬度" />
          <el-table-column align="center" label="操作">
            <template #default="scope">
              <div class="deleteIcon" @click="handleDeleteClick(scope.$index)"></div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="router-region">
      <div
        style="
          width: 95%;
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div class="router-header">绕行区域</div>
        <div class="positionPick-add" @click="positionPickPolygon()"></div>
      </div>
      <div class="router-menu">
        <el-table :data="tableDataRegion" style="width: 100%" max-height="150">
          <el-table-column
            prop="tempPositions"
            show-overflow-tooltip
            align="center"
            label="绕行区域"
          />
          <el-table-column align="center" label="操作">
            <template #default="scope">
              <div
                class="deleteIcon"
                @click="handleDeleteRegionClick(scope.$index)"
              ></div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div class="dialogbutton">
      <div class="masbutton cancer" @click="resetForm()">
        <span>清空规划</span>
      </div>
      <div class="masbutton submit" @click="submitForm()">
        <span>开始规划</span>
      </div>
    </div>
  </div>
</template>
<script setup>
const tableData = ref([]);
const tableDataRegion = ref([]);
let entity = reactive({});
const initentity = () => {
  entity = window.viewer.entities.add({
    label: {
      show: true,
    },
    point: {
      pixelSize: 5,
      color: Cesium.Color.RED,
    },
  });
};
let pointpick_handler = reactive({});
const pathParams = reactive({});
const startPoint = reactive({
  longitude: "",
  latitude: "",
});
const endPoint = reactive({
  longitude: "",
  latitude: "",
});

onMounted(() => {
  emitter.on("viewerLoad", (data) => {
    initentity();
  });
  if (window.viewer) {
    initentity();
  }
});
const handleDeleteClick = (index) => {
  tableData.value.splice(index, 1);
};
const handleDeleteRegionClick = (index) => {
  tableDataRegion.value.splice(index, 1);
  viewer.entities.remove(polygonEntity.value);
};
const submitForm = () => {};
const resetForm = () => {};

const positionPick = (val) => {
  pointpick_handler = new Cesium.ScreenSpaceEventHandler(window.viewer.scene.canvas);
  pointpick_handler.setInputAction((click) => {
    const cartesian = window.viewer.camera.pickEllipsoid(
      click.position,
      viewer.scene.globe.ellipsoid
    );
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    if (val === "start") {
      startPoint.longitude = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
      startPoint.latitude = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);
    } else if (val === "end") {
      endPoint.longitude = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
      endPoint.latitude = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);
    } else if (val === "center") {
      tableData.value.push({
        longitude: Cesium.Math.toDegrees(cartographic.longitude).toFixed(6),
        latitude: Cesium.Math.toDegrees(cartographic.latitude).toFixed(6),
      });
    }
    entity.show = true;
    entity.position = cartesian;
    removehandler();
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
let polygonHandler = ref();
let positions = [];
let polygonEntity = ref();

const createPolygon = (positions) => {
  return new Cesium.PolygonHierarchy(Cesium.Cartesian3.fromDegreesArray(positions));
};

const positionPickPolygon = () => {
  // Reset positions and handler
  positions = [];
  if (polygonHandler.value) {
    polygonHandler.value.destroy();
  }
  if (polygonEntity.value) {
    viewer.entities.remove(polygonEntity.value);
    polygonEntity.value = null;
  }

  polygonHandler.value = new Cesium.ScreenSpaceEventHandler(window.viewer.scene.canvas);
  polygonHandler.value.setInputAction((click) => {
    const cartesian = window.viewer.camera.pickEllipsoid(
      click.position,
      viewer.scene.globe.ellipsoid
    );
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian);

    const longitude = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
    const latitude = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);

    positions.push(parseFloat(longitude), parseFloat(latitude));

    if (polygonEntity.value) {
      viewer.entities.remove(polygonEntity.value);
    }

    if (positions.length >= 6) {
      viewer.entities.remove(polygonEntity.value);
      // At least 3 points to form a polygon
      // Close the polygon by connecting the last point to the first point
      const tempPositions = positions.concat([positions[0], positions[1]]);
      polygonEntity.value = viewer.entities.add({
        polygon: {
          hierarchy: createPolygon(tempPositions),
          material: Cesium.Color.RED.withAlpha(0.5),
        },
      });
    } else if (positions.length >= 2) {
      viewer.entities.remove(polygonEntity.value);
      polygonEntity.value = viewer.entities.add({
        polygon: {
          hierarchy: createPolygon(positions),
          material: Cesium.Color.RED.withAlpha(0.5),
        },
      });
    }

    // Display the points in the console for verification
    // console.log({ longitude, latitude });
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  polygonHandler.value.setInputAction(() => {
    let tempPositions = [];
    if (positions.length >= 6) {
      viewer.entities.remove(polygonEntity.value);
      // At least 3 points to form a polygon
      // Close the polygon by connecting the last point to the first point
      tempPositions = positions.concat([positions[0], positions[1]]);
      const polygonHierarchy = createPolygon(tempPositions);

      // Add polygon entity to the scene
      polygonEntity.value = viewer.entities.add({
        polygon: {
          hierarchy: polygonHierarchy,
          material: Cesium.Color.RED.withAlpha(0.5),
        },
      });
    }
    // console.log(tempPositions);
    // Push coordinates of the polygon to tableDataRegion
    tableDataRegion.value.push({ tempPositions: tempPositions });
    // Display the stored polygon data in the console for verification
    // console.log(tableDataRegion.value);
    // polygonHandler.value.destroy();
  }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
};

const removehandler = () => {
  entity.show = false;
  pointpick_handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
</script>
<style scoped lang="scss">
//将背景和高斯模糊全部设置在了伪元素内，并让伪元素的z-index为-1，避免遮盖其他元素
.router::before {
  content: "";
  position: absolute;
  width: 358px;
  min-height: 628px;
  height: max-content;
  z-index: -1;
  background: rgba(8, 76, 124, 0.5);
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%) 2;
  backdrop-filter: blur(10px);
  top: -10px;
}

.router {
  // backdrop-filter: blur(10px);
  width: 358px;
  min-height: 628px;
  height: max-content;
  // background: rgba(8, 76, 124, 0.5);
  // background: rgba(8, 76, 124, 0.5);
  box-sizing: border-box;
  // border: 2px solid;
  // border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%) 2;
  // backdrop-filter: blur(10px);
  // padding: 8px;
  .router-header {
    background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center center;
    background-size: 100% 100%;
    width: 184.04px;
    height: 32px;
    padding: 5px 30px;
    margin: 5px 10px;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0px;
    color: aliceblue;
  }
  .router-menu {
    padding: 10px 10px;
    .deleteIcon {
      width: 100%;
      height: 12px;
      background: url("@/assets/xtui/tools/deleteicon.png") no-repeat center center;
    }
    .router-menu-point {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      width: 100%;
      align-items: center;
      margin: 5px 0px;
      span {
        font-family: 思源黑体;
        font-size: 12px;
        font-weight: normal;
        line-height: 20px;
        text-align: right;
        letter-spacing: 0px;
        font-feature-settings: "kern" on;
        color: #ffffff;
      }
    }
  }
}

.dialogbutton {
  width: 100%;
  display: flex;
  justify-content: space-evenly;
  .masbutton {
    width: 100px;
    height: 32px;
    color: #ffffff;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    text-align: center;
    display: flex;
    align-content: center;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  .submit {
    background-image: url("@/assets/xtui/command/onduty/sunmitback.png");
  }
  .cancer {
    background-image: url("@/assets/xtui/command/onduty/cancleback.png");
  }
}

.positionPick {
  cursor: pointer;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
  background-image: url("@/assets/xtui/tools/pickimg.png");
}
.positionPick-add {
  cursor: pointer;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
  background-image: url("@/assets/xtui/tools/addimg.png");
}
</style>
