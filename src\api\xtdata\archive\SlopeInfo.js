import request from '@/utils/request'

// 查询监测大类
export function getSlopeInfo(query) {
  return request({
    url: '/mine/slope',
    method: 'get',
    params: query
  })
}



export function addSlopeInfo(data) {
  return request({
    url: '/mine/slope',
    method: 'post',
    data: data
  })
}


export function updateSlopeInfo(data) {
  return request({
    url: '/mine/slope',
    method: 'post',
    data: data
  })
}


export function delSlopeInfo(ids) {
  return request({
    url: '/mine/slope',
    method: 'delete',
    data: ids
  })
}
