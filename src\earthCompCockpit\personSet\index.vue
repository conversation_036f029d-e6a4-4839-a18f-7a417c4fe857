<template>
  <div class="person" v-show="peoplePanelFlag">
    <!-- <div class="person-title">
      <span>人员与短信设置</span>
      <CircleClose
        @click="closePeoplePanel"
        style="width: 1.2em; height: 1.2em; margin-right: 8px; cursor: pointer"
      />
    </div> -->

    <div class="person-config">
      <div>置信度配置：</div>
      <el-checkbox-group
        v-model="checkList"
        @change="changeCheckValue"
        size="large"
        :min="1"
      >
        <el-checkbox label="低" value="低" />
        <el-checkbox label="中" value="中" />
        <el-checkbox label="高" value="高" />
      </el-checkbox-group>
    </div>

    <div class="person-set">
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        status-icon
        :rules="rules"
        label-width="85px"
        label-position="left"
      >
        <el-col :span="24">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="ruleForm.name" style="width: 298px" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="单位" prop="role">
            <el-input v-model="ruleForm.role" style="width: 298px" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="电话" prop="phone">
            <el-input v-model="ruleForm.phone" style="width: 298px" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div style="margin-left: 5px">
            <el-button type="primary" plain @click="submitForm(ruleFormRef)">
              新增
            </el-button>
            <el-button type="primary" plain @click="resetForm(ruleFormRef)"
              >重置</el-button
            >
          </div>
        </el-col>
      </el-form>
    </div>
    <div class="person-list">
      <el-table :data="peopleListData" style="width: 100%" max-height="200">
        <el-table-column prop="name" label="姓名" align="center"> </el-table-column>
        <el-table-column prop="role" label="单位" width="100" align="center">
        </el-table-column>
        <el-table-column prop="phone" label="电话" width="110" align="center">
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="danger" text @click="deleteItem(scope.row.id)" size="small"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="person-list-page">
        <el-pagination
          background
          :page-size="peoplePageParams.pageSize"
          :current-page="peoplePageParams.pageNum"
          layout="prev, pager, next"
          :total="totalPeople"
          @current-change="handleChangePeople"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { nextTick, ref, reactive, computed, onMounted, toRaw } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  addUser,
  getUserList,
  deleteUser,
  getMsgList,
  setConfig,
  getConfig,
} from "@/api/xtsatellite/index.js";

const { proxy } = getCurrentInstance();

const emits = defineEmits(["changeQuery", "closeA"]); //参数为数组
// emits("getNum", 10);
// ====================置信度配置====================================
const checkList = ref(["低", "中", "高"]);
getConfig().then((res) => {
  if (res.data) {
    checkList.value = res.data;
  }
});
function changeCheckValue(val) {
  ElMessageBox.confirm("确认更新配置吗", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      setConfig(val).then((res) => {
        if (res.data) {
          ElMessage({
            type: "success",
            message: "配置修改成功",
          });
        }
      });
    })
    .catch(() => {
      getConfig().then((res) => {
        if (res.data) {
          checkList.value = res.data;
          ElMessage({
            type: "info",
            message: "已取消",
          });
        }
      });
    });

  /* const d = {
    level: val,
  };
  setConfig(val).then((res) => {
    if (res.data) {
      ElMessage.success("配置成功");
    }
  }); */
}
// ====================人员配置======================================
const ruleForm = reactive({
  name: "",
  role: "",
  phone: "",
  t: "",
});
const ruleFormRef = ref();

//rule校验规则对象、value表单输入内容、callback符合条件callback放行(不符合callback注入错误信息)

const validatorPhone = (rule, value, callback) => {
  if (/^1\d{10}$/.test(value)) {
    // if (/^d{4,10}$/.test(value)) {
    callback();
  } else {
    callback(new Error("请输入正确的电话号码"));
  }
};

// 定义表单校验配置对象
const rules = {
  name: [
    {
      required: true,
      message: "请输入用户名",
      trigger: "change",
    },
  ],

  role: [{ required: true, message: "请输入工作单位", trigger: "change" }],
  phone: [{ required: true, trigger: "change", validator: validatorPhone }],
};

const submitForm = (formEl) => {
  // 发送请求前，保证全部表单校验通过后再发请求
  // await loginForms.value.validate();
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      console.log("submit!");
      const t = proxy.dayjs().format("YYYY-MM-DD HH:mm:ss");
      ruleForm.t = t;
      addUser(ruleForm).then((res) => {
        if (res.data) {
          ElMessage.success("添加成功");

          peoplePageParams.pageNum = Math.ceil(
            ++totalPeople.value / peoplePageParams.pageSize
          );
          getAllPeople(peoplePageParams);
        }
      });
    } else {
      console.log("error submit!");
    }
  });
};

const resetForm = (formEl) => {
  if (!formEl) return;
  formEl.resetFields();
};

const handleClick = () => {};
// =========================人员列表==================================
const peoplePanelFlag = ref(true);
const totalPeople = ref(0);
const peoplePageParams = reactive({
  pageNum: 1,
  pageSize: 5,
});
function openPeoplePanel() {
  peoplePanelFlag.value = true;
}
function closePeoplePanel() {
  // peoplePanelFlag.value = false;
  emits("closeA");
}

const peopleListData = ref([]);
function getAllPeople(p) {
  getUserList(p).then((res) => {
    peopleListData.value = res.rows;
    totalPeople.value = res.total;
  });
}
getAllPeople(peoplePageParams);

function handleChangePeople(num) {
  peoplePageParams.pageNum = num;
  getAllPeople(peoplePageParams);
}

function deleteItem(id) {
  ElMessageBox.confirm("确认删除吗", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      deleteUser([id]).then((res) => {
        if (res.data) {
          ElMessage.success("删除成功");
          peoplePageParams.pageNum = 1;
          getAllPeople(peoplePageParams);
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消",
      });
    });
}
</script>

<style scoped lang="scss">
.person {
  pointer-events: auto;
  height: 602px;
  width: 436px;
  padding: 10px;
  // position: fixed;
  // top: 53.8%;
  // left: 50%;
  // transform: translate(-50%, -50%); /* 使用css3的transform来实现 */

  /* background: rgba(8, 76, 124, 0.5);
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%)
    2;
  backdrop-filter: blur(10px); */

  .person-title {
    background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center center;
    background-size: 96% 100%;
    height: 32px;
    width: 100%;
    margin: 15px 0px 15px 0px;
    padding: 5px 30px;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0px;
    color: aliceblue;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .person-config {
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 20px;
    // height: 50px;
    padding: 10px 20px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: whitesmoke;
    :deep(.el-checkbox__label) {
      color: whitesmoke;
    }
  }

  .person-set {
    // height: 205px;
    padding: 0px 10px;

    :deep(.el-form-item--default) {
      margin-bottom: 20px;
    }
    :deep(.el-button) {
      background-color: rgba(21, 66, 139, 0.42);
      color: aliceblue;
    }
    :deep(.el-form-item__label) {
      color: white;
      font-size: 14px;
      font-weight: 400;
      letter-spacing: 0px;
      // line-height: 20px;
    }
    :deep(.el-input__wrapper) {
      background-color: transparent;
      width: 276px;
      height: 28px;
    }
    :deep(.el-input__inner) {
      color: white;
    }
  }
  .person-list {
    padding: 15px;
    /* 全局修改el-table 表头和内容颜色 header color content color */
    .person-list-page {
      // position: absolute;
      // bottom: 30px;
      margin-top: 15px;
      display: flex;
      justify-content: center;
    }
  }
}

:deep(.el-table th) {
  background: rgba(100, 113, 142, 1) !important;
  .cell {
    font-weight: 400;
    color: #ffffff !important;
  }
}
/*最外层透明*/
:deep(.el-table),
:deep(.el-table__expanded-cell) {
  background-color: transparent;

  cursor: pointer;
}
/* 表格内背景颜色 */
/* 表格内背景颜色  */
:deep(.el-table th),
:deep(.el-table tr),
:deep(.el-table td) {
  background-color: transparent;
  border: 0px;
  color: #ffffff;
  font-size: 12px;
  height: 5px;
  font-family: Source Han Sans CN Normal, Source Han Sans CN Normal-Normal;
  font-weight: Normal;
}
/* // 修改高亮当前行颜色 */
:deep(.el-table tbody tr:hover) > td {
  background: #84a7d271 !important;
}
:deep(.el-table .el-table__header-wrapper th) {
  background-color: rgba(#63abe8, 0.4) !important;
  color: rgba(#ffffff, 1) !important;
}
:deep(.el-table__inner-wrapper::before) {
  // height: 0.5px !important;
  // border: 0.1px solid rgba(209, 203, 203, 0.185);
  background-color: #e9ebef26;
}
:deep(.el-table__row > td) {
  border: none !important;
}
:deep(.el-table::before) {
  height: 0px !important;
}
:deep(.el-table) {
  --el-table-border: none !important;
}

:deep(.el-pagination.is-background .btn-prev) {
  width: 40px;
  height: 40px;
  background-color: rgba(41, 104, 221, 0.168);
}
:deep(.el-pagination.is-background .btn-next) {
  width: 40px;
  height: 40px;
  background-color: rgba(41, 104, 221, 0.168);
}
:deep(.el-pagination__total) {
  color: #ffffff !important;
}

:deep(.el-pagination.is-background .el-pager li.is-active) {
  width: 40px;
  height: 40px;
  background-color: rgba(41, 104, 221, 0.168);
}
</style>
