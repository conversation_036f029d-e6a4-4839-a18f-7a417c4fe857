import request from '@/utils/request'

// 查询承包商基本信息列表
export function listBasic(query) {
  return request({
    url: '/contractor/basic',
    method: 'get',
    params: query
  })
}

// 查询承包商基本信息详细
export function getBasic(id) {
  return request({
    url: '/contractor/basic/' + id,
    method: 'get'
  })
}

// 新增承包商基本信息
export function addBasic(data) {
  return request({
    url: '/contractor/basic',
    method: 'post',
    data: data
  })
}

// 修改承包商基本信息
export function updateBasic(data) {
  return request({
    url: '/contractor/basic',
    method: 'post',
    data: data
  })
}

// 删除承包商基本信息
export function delBasic(data) {
  return request({
    url: '/contractor/basic/',
    method: 'delete',
    data: data
  })
}
