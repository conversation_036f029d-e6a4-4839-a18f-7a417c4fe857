<template>
  <div class="content-box">
    <div class="content-box-title">
      <span>{{ title }}</span>

      <div class="content-box-button" v-if="buttonVisible">
        <div
          class="content-box-button-list"
          v-for="(item, index) in buttonInfo"
          :key="index"
        >
          <div
            class="content-box-button-list-li"
            :class="{ menubuttonActive: item.id === isActive }"
            @click="buttonClick(item)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
      <div
        class="content-box-icon"
        v-if="iconVisble"
        @click="parentClick"
      ></div>
    </div>
    <div class="content-box-main">
      <slot />
    </div>
  </div>
</template>

<script setup>
/* 
title:标题
iconVisble：是否显示更多按钮
buttonVisible:显示功能切换按钮
iconClick：向父组件传递点击方法事件
*/
const props = defineProps({
  title: {
    type: String,
    required: true,
    default: "标题",
  },
  iconVisble: {
    type: Boolean,
    required: false,
    default: false,
  },
  buttonVisible: {
    type: Boolean,
    required: false,
    default: false,
  },
  buttonInfo: {
    type: Array,
    required: false,
    default: [
      {
        id: 1,
        label: "指令",
        click: "zhiling",
      },
      {
        id: 2,
        label: "实况",
        click: "shikuang",
      },
    ],
  },
});
const isActive = ref(1);
const emit = defineEmits(["iconClick", "buttonClick"]);
const parentClick = () => {
  emit("iconClick");
};

const buttonClick = (item) => {
  emit("buttonClick", item);
  isActive.value = item.id;
};
</script>

<style scoped lang="scss">
.content-box {
  margin: 10px;
  width: 400px;
  // height: max-content;
  // min-height: 130px;
  .content-box-title {
    background: url("@/assets/xtui/panel/cardsTitleBack.png");
    background-size: 100% 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      font-family: ysbthzt;
      font-size: 24px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0em;
      background: linear-gradient(180deg, #85a9e7 0%, #ffffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-left: 26px;
    }
    .content-box-icon {
      background: url("@/assets/xtui/panel/title_btn.png");
      background-size: 100% 100%;
      margin-right: 25px;
      width: 25px;
      height: 25px;
      cursor: pointer;
    }
    .content-box-button {
      width: 200px;
      height: 35px;
      display: flex;
      justify-content: flex-end;
      cursor: pointer;
      .content-box-button-list {
        .content-box-button-list-li {
          width: 65px;
          height: 30px;
          color: #ffffff !important;
          padding: 5px 15px;
        }
        .menubuttonActive {
          width: 65px;
          color: #ffffff !important;
          height: 30px;
          background: url("@/assets/xtui/panel/titlebutonHover.png");
          background-size: 100% 100%;
          padding: 5px 15px;
        }
      }
    }
  }
  .content-box-main {
    height: max-content;
    // min-height: 130px;
  }
}
</style>
