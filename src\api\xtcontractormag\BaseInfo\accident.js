import request from '@/utils/request'

// 查询承包商事故列表
export function listAccident(query) {
  return request({
    url: '/contractor/accident',
    method: 'get',
    params: query
  })
}

// 查询承包商事故详细
export function getAccident(id) {
  return request({
    url: '/contractor/accident/' + id,
    method: 'get'
  })
}

// 新增承包商事故
export function addAccident(data) {
  return request({
    url: '/contractor/accident',
    method: 'post',
    data: data
  })
}

// 修改承包商事故
export function updateAccident(data) {
  return request({
    url: '/contractor/accident',
    method: 'post',
    data: data
  })
}

// 删除承包商事故
export function delAccident(data) {
  return request({
    url: '/contractor/accident/',
    method: 'delete',
    data: data
  })
}
