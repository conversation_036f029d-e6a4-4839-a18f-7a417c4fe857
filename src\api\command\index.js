import request from '@/utils/request';

// task id获取灾害详情（类型）
export function getEventInfoByTaskId(id) {
	return request({
		url: `/command/alarm/${id}`,
		method: 'get',
		// params: params,
	});
}
// 再通过灾害（类型）获取预案列表
export function getCaseListByEventType(params) {
	return request({
		url: `/plan/plan`,
		method: 'get',
		params: params,
	});
}
// 结束应急 修改中台事件状态
export function updateAlarmById(data) {
	return request({
		url: `/command/alarm`,
		method: 'post',
		data: data,
	});
}
// ---------------------------------------------------
// 天气查询
export function getAmapWeather(params) {
	return request({
		url: window.xtmapConfig.weather.url,
		method: 'get',
		params: params,
	});
}
// 应急资源树
export function getResourceTree() {
	return request({
		url: `/resources/tree`,
		method: 'get',
	});
}
// 应急资源统计
export function getResourceChart(params) {
	return request({
		url: `/resources/chart/allNum?store=true`,
		method: 'get',
		params: params,
	});
}

// 车辆状态表格
export function getCarsList(params) {
	return request({
		url: '/command/car',
		method: 'get',
		params: params,
	});
}
// 舆情表格
export function getNewsList() {
	return request({
		url: '/publicSentiment',
		method: 'get',
	});
}

// 值班人员详情
export function getDutyDetails(params) {
	return request({
		/* url:
			window.xtmapConfig.xtParkBaseUrl +
			'/api/secure-base/dutyPlan/month', */
		url: '/dutyPlan/month',
		method: 'get',
		params: params,
	});
}

// 报警监测详情
export function getWarningList(params) {
	return request({
		url: '/warning',
		method: 'get',
		params: params,
	});
}

// 新增事故事件
export function addEvents(params) {
	return request({
		url: '/command/alarm',
		method: 'post',
		data: params,
	});
}

// 预案列表下拉
export function getPlanList(params) {
	return request({
		url: '/plan/plan',
		method: 'get',
		params: params,
	});
}

// 根据xttid请求报警信息
export function getAlarmById(alarmId) {
	return request({
		url: `/command/alarm/${alarmId}`,
		method: 'get',
	});
}

// 预案id获取人员分组和指令信息
export function getEmergencePlanId(emergencePlanId) {
	return request({
		url: `/plan/planDetail/${emergencePlanId}`,
		method: 'get',
	});
}

// 初始化应急通讯录
export function getDutyAddress(params) {
	return request({
		url: `/command/dutyAddress`,
		// url: `/duty/dutyAddress`,
		method: 'get',
		params: params,
	});
}

// 路径规划
export function lineRouterService(data) {
	return request({
		url: window.xtmapConfig.routePlanUrl,
		method: 'post',
		data: data,
	});
}
