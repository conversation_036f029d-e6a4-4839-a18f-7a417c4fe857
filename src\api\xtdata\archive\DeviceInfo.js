import request from '@/utils/request'

// 查询监测大类
export function getDeviceInfo(query) {
  return request({
    url: '/mine/device',
    method: 'get',
    params: query
  })
}



export function addDeviceInfo(data) {
  return request({
    url: '/mine/device',
    method: 'post',
    data: data
  })
}


export function updateDeviceInfo(data) {
  return request({
    url: '/mine/device',
    method: 'post',
    data: data
  })
}


export function delDeviceInfo(ids) {
  return request({
    url: '/mine/device',
    method: 'delete',
    data: ids
  })
}
