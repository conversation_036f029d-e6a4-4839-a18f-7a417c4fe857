<template>
  <div id="cesiumContainer"></div>
  <div class="tool-position">
    <MousePosition></MousePosition>
  </div>
</template>

<script setup>
import axios from "axios";
import MousePosition from "@/earthCompStyle/MousePosition";
// 公共方法
import CommonMethods from "./common/Common.js";
// 影像裁剪
import MapClipImage from "./common/mapClipImage.js";
// 可移动label
import DragLable from "./common/DragLable.js";
// 静态数据--来源与现在的系统
import { mineData } from "./common/mineData.js";
// 在线天地图影像服务
let TDT_IMG_W =
  "http://{s}.tianditu.gov.cn/img_w/wmts?service=wmts&request=GetTile&version=1.0.0" +
  "&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}" +
  "&style=default&format=tiles&tk=" +
  window.xtmapConfig.map.TDU_Key;
//在线天地图影像中文标记服务(墨卡托投影)
let TDT_CIA_W =
  "http://{s}.tianditu.gov.cn/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0" +
  "&LAYER=cia&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}" +
  "&style=default.jpg&tk=" +
  window.xtmapConfig.map.TDU_Key;

onMounted(() => {
  window.viewer = new Cesium.Viewer("cesiumContainer", {
    /* imageryProvider: new Cesium.UrlTemplateImageryProvider({
      url: "http://gwxc.shipxy.com/tile.g?z={z}&x={x}&y={y}",
    }), */
    imageryProvider: new Cesium.UrlTemplateImageryProvider({
      url: `https://tiles1.geovisearth.com/base/v1/img/{z}/{x}/{y}?format=webp&tmsIds=w&token=${window.xtmapConfig.map.XTY_Key}`,
    }),
    /* imageryProvider: new Cesium.WebMapTileServiceImageryProvider({
      // 调用影像地图中文服务
      url: TDT_IMG_W, // url地址
      layer: "img_w", // WMTS请求的层名称
      style: "default", // WMTS请求的样式名称
      format: "tiles", // MIME类型，用于从服务器检索图像
      tileMatrixSetID: "GoogleMapsCompatible", //	用于WMTS请求的TileMatrixSet的标识符
      subdomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"], // 天地图8个服务器
      minimumLevel: 0, // 最小层级
      maximumLevel: 18, // 最大层级
    }), */
    animation: false,
    shouldAnimate: true,
    baseLayerPicker: false,
    fullscreenButton: false,
    geocoder: false,
    homeButton: false,
    sceneModePicker: false,
    selectionIndicator: false,
    timeline: false,
    navigationHelpButton: false,
    infoBox: false,
    mapMode2D: Cesium.MapMode2D.ROTATE,
    navigationInstructionsInitiallyVisible: false,
    // terrainProvider: new Cesium.EllipsoidTerrainProvider(),
    contextOptions: {
      //cesium状态下允许canvas转图片convertToImage
      webgl: {
        alpha: true,
        depth: false,
        stencil: true,
        antialias: true,
        premultipliedAlpha: true,
        preserveDrawingBuffer: true,
        failIfMajorPerformanceCaveat: true,
      },
      allowTextureFilterAnisotropic: true,
    },
  });

  viewer.scene.debugShowFramesPerSecond = false;
  viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
    Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
  );

  viewer._cesiumWidget._creditContainer.style.display = "none";
  /* viewer.imageryLayers.addImageryProvider(
    new Cesium.WebMapTileServiceImageryProvider({
      // 影像注记
      url: TDT_CIA_W,
      subdomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
      layer: "cia_w",
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "GoogleMapsCompatible",
    })
  ); */
  viewer.imageryLayers.addImageryProvider(
    new Cesium.UrlTemplateImageryProvider({
      url: `https://tiles1.geovisearth.com/base/v1/cia/{z}/{x}/{y}?format=png&tmsIds=w&token=${window.xtmapConfig.map.XTY_Key}`,
    })
  );
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 20000000;
  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 600;
  viewer._cesiumWidget._creditContainer.style.display = "none";

  emitter.emit("viewerLoad", { name: "h" });
  // 初始化地球位置
  // initMapPosition();
  // // 添加地图遮罩+父区域边界
  // addClipImage();
  // // 添加市区 区域范围
  // addZZ();
  // // 添加四个矿区图标
  // addMinePosition();
});
// 初始化地球位置方法
const commonMethods = ref();
const initMapPosition = () => {
  commonMethods.value = new CommonMethods(viewer);
  commonMethods.value.initMap();
};
// 添加地图遮罩+父区域边界
const MapClipImageObj = ref();
const addClipImage = () => {
  MapClipImageObj.value = new MapClipImage(viewer);
  axios
    .get(`${window.xtmapConfig.publicPath}/geojson/xinjiangprovice.json`)
    .then((res) => {
      MapClipImageObj.value.clipImage(res.data);
    });
};
// 添加市区 区域范围
const addZZ = () => {
  let zzbjx = Cesium.GeoJsonDataSource.load(
    `${window.xtmapConfig.publicPath}/geojson/新疆维吾尔自治区.json`,
    {
      stroke: Cesium.Color.BLUE.withAlpha(0), // 边框颜色
      fill: Cesium.Color.RED.withAlpha(0), // 填充颜色
      strokeWidth: 0, // 边框宽度
      material: new Cesium.PolylineGlowMaterialProperty({
        color: Cesium.Color.BLUE,
      }),
      // zIndex: 1,
    }
  );
  zzbjx.then((dataSource) => {
    viewer.dataSources.add(zzbjx);
    dataSource.name = "geo-zzbjx";
    dataSource.entities.values.forEach((entity) => {
      let positions = entity.polygon.hierarchy._value.positions;
      entity.polyline = {
        positions: positions,
        width: 1,
        material: new Cesium.PolylineGlowMaterialProperty({
          glowPower: 0.2,
          color: Cesium.Color.fromCssColorString("#00ffff"),
        }),
      };
    });
    // viewer.flyTo(zzbjx);
  });
};
</script>

<style scoped>
#cesiumContainer {
  position: absolute;
  width: 100%;
  height: 100vh;
  top: 0px;
  left: 0px;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
.tool-position {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 32px;
  z-index: 100;
  background: #1a4599;
  opacity: 0.6;
  /* background-color: rgba(0, 0, 255, 0.589); */
}
</style>
