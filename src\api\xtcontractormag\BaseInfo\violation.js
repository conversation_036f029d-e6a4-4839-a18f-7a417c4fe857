import request from '@/utils/request'

// 查询承包商违章列表
export function listViolation(query) {
  return request({
    url: '/contractor/violation',
    method: 'get',
    params: query
  })
}

// 查询承包商违章详细
export function getViolation(id) {
  return request({
    url: '/contractor/violation/' + id,
    method: 'get'
  })
}

// 新增承包商违章
export function addViolation(data) {
  return request({
    url: '/contractor/violation',
    method: 'post',
    data: data
  })
}

// 修改承包商违章
export function updateViolation(data) {
  return request({
    url: '/contractor/violation',
    method: 'post',
    data: data
  })
}

// 删除承包商违章
export function delViolation(data) {
  return request({
    url: '/contractor/violation/',
    method: 'delete',
    data: data
  })
}
