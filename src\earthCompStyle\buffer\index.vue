<script setup>
import { B<PERSON>erPolygon, BufferLine, BufferPoint } from "./bf";

import { nextTick, ref, reactive, computed, onMounted } from "vue";

function initMeasureTool() {
  window.pb = new BufferPoint(window.viewer);
  window.lb = new BufferLine(window.viewer);
  window.pob = new BufferPolygon(window.viewer);
}
const pointInput = ref(1000);
const lineInput = ref(1000);
const polygonInput = ref(1000);

const pointOptions = reactive({
  radius: pointInput,
  color: Cesium.Color.RED.withAlpha(0.6),
  outlineColor: "",
});

function drawPoint() {
  pb.reset();
  pb.createEvent(pointOptions).then((res) => {
    // console.log(res, "res");  //返回点击位置的坐标 用于后续传递给后台进行查询
  });
}
const lineOptions = reactive({
  radius: lineInput,
  color: Cesium.Color.RED.withAlpha(0.6),
  outlineColor: "",
});

function drawLine() {
  lb.reset();
  lb.createEvent(lineOptions).then((res) => {
    // console.log(res, "res");  //返回点击位置的坐标 用于后续传递给后台进行查询
  });
}
const polygonOptions = reactive({
  radius: polygonInput,
  color: Cesium.Color.RED.withAlpha(0.6),
  outlineColor: "",
});

function drawPolygon() {
  pob.reset();
  pob.createEvent(polygonOptions).then((res) => {
    // console.log(res, "res");  //返回点击位置的坐标 用于后续传递给后台进行查询
  });
}

function clearAll() {
  lb.reset();
  pb.reset();
  pob.reset();
}
onMounted(() => {
  setTimeout(() => {
    initMeasureTool();
  }, 500);
});
</script>

<template>
  <div class="buffer">
    <div class="buffer-header">
      <span>缓冲区</span>
    </div>
    <div class="buffer-menu">
      <span>请先输入缓冲半径，再左键点击绘制</span>
      <div class="class-buffer point">
        <el-input
          v-model.number="pointInput"
          style="width: 160px"
          placeholder="Please input"
          ><template #append>米</template></el-input
        >
        <el-button @click="drawPoint">绘制点</el-button>
      </div>
      <el-divider />
      <span>请先输入缓冲半径，再左键点击绘制</span>
      <div class="class-buffer line">
        <el-input
          v-model.number="lineInput"
          style="width: 160px"
          placeholder="Please input"
          ><template #append>米</template></el-input
        >
        <el-button @click="drawLine">绘制线</el-button>
      </div>
      <el-divider />
      <span>请先输入缓冲半径，再左键点击绘制</span>
      <div class="class-buffer polygon">
        <el-input
          v-model.number="polygonInput"
          style="width: 160px"
          placeholder="Please input"
          ><template #append>米</template></el-input
        >
        <el-button @click="drawPolygon">绘制面</el-button>
      </div>
      <el-divider />
      <div class="class-clear">
        <el-button @click="clearAll"> 清除所有 </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.buffer {
  width: 300px;
  height: max-content;
  // background: url("@/assets/xtui/tools/dialogback.png");
  // background-size: 100% 100%;
  // display: flex;
  // flex-direction: column;
  // color: antiquewhite;
  // font-family: ysbthzt;
  background: rgba(8, 76, 124, 0.5);
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%) 2;
  backdrop-filter: blur(10px);
  padding: 8px;
  .buffer-header {
    background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center center;
    background-size: 100% 100%;
    height: 32px;
    width: 100%;
    margin: 5px 0px 15px 0px;
    padding: 5px 30px;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0px;
    color: aliceblue;
  }
  .buffer-menu {
    padding: 10px 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    height: 329px;
    .class-buffer {
      display: flex;
      justify-content: space-around;
    }
    .class-clear {
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
