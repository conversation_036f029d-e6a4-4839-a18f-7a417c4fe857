import request from '@/utils/request';
//查询基础信息
export function getBaseInfo(id) {
	return request({
		url: `/drill/task/${id}`,
		method: 'get'
	});
}
export function getBasePlan() {
	return request({
		url: `/plan/plan`,
		method: 'get'
	});
}

export function getPlanDetail(id) {
	return request({
		url: `/plan/planDetail/${id}`,
		method: 'get'
	});
}
export function getDrillRecord() {
	return request({
		url: `/drill/record`,
		method: 'get'
	});
}

export function sendCommand(task_id, msg, groupName = "ALL") {
	const reqData = {
		exchange: "XTEGleaderFanout",
		routingKey: "",
		data: JSON.stringify({
			from: task_id,
			to: groupName,
			msg,
		}),
	};
	return request({
		url: `/drill/forward`,
		method: 'post',
		data: reqData
	});
}
//获取推演历史
export function getDrillHistory(taskId) {
	return request({
		url: `/drill/history`,
		method: 'get',
		params: {taskId}
	});
}