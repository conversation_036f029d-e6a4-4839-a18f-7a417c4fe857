class newTreeAction {
  constructor(){
    // ---------------定义全局变量
    this.resource_url = "https://park.geovisearth.com";
    // 园区基本信息接口
    this.securebase_url = "https://park.geovisearth.com";
    this.em_resource_icons = {};
    this.imgurlforIREAL = "http://182.92.158.14:4321";
    this.expertImg = imgurlforIREAL + "/geovis/projects/project_3301/toolsforTree/expert.png";
    this.teamImg = imgurlforIREAL + "/geovis/projects/project_3301/toolsforTree/team.png";
    this.goodsImg = imgurlforIREAL + "/geovis/projects/project_3301/toolsforTree/goods.png";
    this.refugeImg = imgurlforIREAL + "/geovis/projects/project_3301/toolsforTree/refuge.png";
    this.medicalImg = imgurlforIREAL + "/geovis/projects/project_3301/toolsforTree/medical.png";
    this.storeImg = imgurlforIREAL + "/geovis/projects/project_3301/toolsforTree/store.png";
    this.cameraImg = imgurlforIREAL + "/geovis/projects/project_3301/toolsforTree/camera.png";

    //vdrug元素控制
    this.domMap = new Map();
  }

  //vdrug元素初始化方法
  initVdragDom(arr){
    arr.map((v)=>{
      this.domMap.set(v.key,v.val);
    })
  }
  // ---------------定义树资源数据请求相关接口-start------------------
  // 1.获取资源树接口
  getResourceTree() {
    const config = {
      method: "get",
      url:  "/resources/tree",
    
    };
    xtRequest.emergency(config)
      .then(function (response) {
        dataHub.emergency_resources_treeInfo.tree_data = response.data.data;
      })
      .catch(function (error) {
        console.log("/resources/tree + error");
      });
  }
  getResourceTreeByType_New(id, type) {
    let image = "";
    switch (type) {
      case "expert":
        image = this.expertImg;
        this.getexpertById(id, image, type);
        break;
      case "team":
        image = this.teamImg;
        this.getteamById(id, image, type);
        break;
      case "goods":
        image = this.goodsImg;
        this.getgoodsById(id, image, type);
        break;
      case "refuge":
        image = this.refugeImg;
        this.getrefugeById(id, image, type);
        break;
      case "medical":
        image = this.medicalImg;
        this.getmedicalById(id, image, type);
        break;
      case "store":
        image = this.storeImg;
        this.getstoreById(id, image, type);
        break;
      default:
        break;
    }
    // if(){

    // }else if(){

    // }
  }
  //选择枚举值
  format(val1,val2) {
      let label = '';
    val2.forEach((item) => {
      if (item.value == val1) {
      label = item.label;
      }
      });
    return label;
  }
  // /resources/expert/{id} 专家资源请求
  getexpertById(id, image) {
    const config = {
      method: "get",
      url:  `/resources/expert/${id}`,
    
    };
    xtRequest.emergency(config)
      .then((response)=>{
        response.data.data.businessArea=this.format( response.data.data.businessArea,dataHub.xt_resource_businessarea)
        response.data.data.title=this.format( response.data.data.title,dataHub.xt_resource_title)
        dataHub.emergency_resources_treeInfo.expertInfo = response.data.data;
        dataHub.emergency_resources_treeInfo.dialogTitleName = "专家详情";
        // 最外层ID
        // getComponentObject("baseRectanglezh5f80").visible = true;
        window.newTreeAction.domMap.get("动态显示内容矩形")("visible",true);
        // // 动态显示列表
        // dataHub.emergency_resources_treeInfo.showDynamicId =
        //   "baseRectangley4hf80";
        setTimeout(()=>{
          window.newTreeAction.domMap.get("弹窗动态显示")('专家详情');
        },300)
      })
      .catch(function (error) {});
  }
  // /resources/team/{id} 队伍资源请求
  getteamById(id, image, type) {
    const config = {
      method: "get",
      url:  `/resources/team/${id}`,
    
    };
    xtRequest.emergency(config)
      .then((response)=>{
        response.data.data.businessArea=this.format( response.data.data.businessArea,dataHub.xt_resource_businessarea)
        response.data.data.teamLevel=this.format( response.data.data.teamLevel,dataHub.xt_resource_teamlevel)
        dataHub.emergency_resources_treeInfo.teamInfo = response.data.data;
        if (image != null) {
          this.addMarker_IE(image, response.data.data, id, type,response.data.data.unitName);
        } else {
          // // 最外层ID
          // getComponentObject("baseRectanglezh5f80").visible = true;
          window.newTreeAction.domMap.get("动态显示内容矩形")("visible",true);
          // // 动态显示列表
          // dataHub.emergency_resources_treeInfo.showDynamicId =
          //   "baseRectangley47f80";
          // dataHub.emergency_resources_treeInfo.dialogTitleName = "应急队伍";
          setTimeout(()=>{
            window.newTreeAction.domMap.get("弹窗动态显示")('应急队伍');
          },300)
          
        }
      })
      .catch(function (error) {});
  }
  // /resources/goods/{id} 物资资源请求
  getgoodsById(id, image, type) {
    const config = {
      method: "get",
      url:  `/resources/goods/${id}`,
    
    };
    xtRequest.emergency(config)
      .then(function (response) {
        dataHub.emergency_resources_treeInfo.goodsInfo = response.data.data;
        if (image != null) {
          this.addMarker_IE(image, response.data.data, id, type,response.data.data.materialName);
        } else {
          // 最外层ID
          // getComponentObject("baseRectanglezh5f80").visible = true;
          window.newTreeAction.domMap.get("动态显示内容矩形")("visible",true);
          // // 动态显示列表
          // dataHub.emergency_resources_treeInfo.showDynamicId =
          //   "baseRectangley3xf80";
          // dataHub.emergency_resources_treeInfo.dialogTitleName = "应急物资";
          setTimeout(()=>{
            window.newTreeAction.domMap.get("弹窗动态显示")('应急物资');
          },300)
        }
      })
      .catch(function (error) {});
  }
  // /resources/refuge/{id} 避难场所请求
  getrefugeById(id, image, type) {
    const config = {
      method: "get",
      url:  `/resources/refuge/${id}`,
    
    };
    xtRequest.emergency(config)
      .then(function (response) {
        dataHub.emergency_resources_treeInfo.refugeInfo = response.data.data;
        if (image != null) {
          this.addMarker_IE(image, response.data.data, id, type,response.data.data.shelterName);
        } else {
          // 最外层ID
          // getComponentObject("baseRectanglezh5f80").visible = true;
          window.newTreeAction.domMap.get("动态显示内容矩形")("visible",true);
          // // 动态显示列表
          // dataHub.emergency_resources_treeInfo.showDynamicId =
          //   "baseRectangley3mf80";
          // dataHub.emergency_resources_treeInfo.dialogTitleName = "避难场所";
          setTimeout(()=>{
            window.newTreeAction.domMap.get("弹窗动态显示")('避难场所');
          },300)
        }
      })
      .catch(function (error) {});
  }
  // /resources/medical/{id} 医疗机构请求
  getmedicalById(id, image, type) {
    const config = {
      method: "get",
      url:  `/resources/medical/${id}`,
    
    };
    xtRequest.emergency(config)
      .then((response)=>{
        response.data.data.institutionLevel=this.format( response.data.data.institutionLevel,dataHub.xt_resource_institutionlevel)
        dataHub.emergency_resources_treeInfo.medicalInfo = response.data.data;
        if (image != null) {
          this.addMarker_IE(image, response.data.data, id, type,response.data.data.institutionName);
        } else {
          // 最外层ID
          // getComponentObject("baseRectanglezh5f80").visible = true;
          window.newTreeAction.domMap.get("动态显示内容矩形")("visible",true);
          // // 动态显示列表
          // dataHub.emergency_resources_treeInfo.showDynamicId =
          //   "baseRectangley3cf80";
          // // getComponentObject("dynamicShowxkwc54").id = "baseRectanglehj9c54";
          // dataHub.emergency_resources_treeInfo.dialogTitleName = "医疗机构";
          setTimeout(()=>{
            window.newTreeAction.domMap.get("弹窗动态显示")('医疗机构');
          },300)
        }
      })
      .catch(function (error) {});
  }
  // /resources/store/{id} 仓库
  getstoreById(id, image, type) {
    const config = {
      method: "get",
      url:  `/resources/store/${id}`,
    
    };
    xtRequest.emergency(config)
      .then(function (response) {
        dataHub.emergency_resources_treeInfo.storeInfo = response.data.data;
        if (image != null) {
          this.addMarker_IE(image, response.data.data, id, type,response.data.data.name);
        } else {
          // // 最外层ID
          // getComponentObject("baseRectanglezh5f80").visible = true;
          window.newTreeAction.domMap.get("动态显示内容矩形")("visible",true);
          // // 动态显示列表
          // dataHub.emergency_resources_treeInfo.showDynamicId =
          //   "baseRectangley32f80";
          // dataHub.emergency_resources_treeInfo.dialogTitleName = "应急仓库";
          setTimeout(()=>{
            window.newTreeAction.domMap.get("弹窗动态显示")('应急仓库');
          },300)
        }
      })
      .catch(function (error) {});
  }

  // 节点取消选中事件处理程序
  onNodeUnselected_New(node) {
    window.earthMain.clearGroup(node.id);
    // 最外层ID
    // getComponentObject("baseRectanglezh5f80").visible = false;
    // getComponentObject("baseRectangley2if80").visible = false;
    // // getComponentObject("baseRectanglejj0a5F").visible = false;
    window.newTreeAction.domMap.get("动态显示内容矩形")('visible',false);
    window.newTreeAction.domMap.get("监控视频弹出框")('visible',false);
  }

  // 点击地图图标展示弹窗
  openDialogByType(type, id) {
    switch (type) {
      case "team":
        this.getteamById(id, null, type);
        break;
      case "goods":
        this.getgoodsById(id, null, type);
        break;
      case "refuge":
        this.getrefugeById(id, null, type);
        break;
      case "medical":
        this.getmedicalById(id, null, type);
        break;
      case "store":
        this.getstoreById(id, null, type);
        break;
      default:
        break;
    }
  }
  // 2.获取企业树
  // 获取企业树接口
  getCompanyTree() {
    const config = {
      method: "get",
      url: "/resources/secure-base/enterprise?userId=1&name=",
    
    };
    xtRequest.emergency(config)
      .then(function (response) {
        response.data.rows.forEach((item) => {
          item.value = item.companyName;
          item.label = item.companyName;
        });
        dataHub.emergency_company_treeInfo.tree_data = [
          {
            value: "拉依苏化工区",
            label: "拉依苏化工区",
            children: response.data.rows,
          },
        ];
      })
      .catch(function (error) {
        console.log(error);
      });
  }

  // 3.获取监控树接口
  // 暂无

  //
  getvideoTreeByType(id, type) {
   let image = this.cameraImg;
    this.addMarker_IE(image, dataHub.emergency_monitor_treeInfo.videoInfo, id, type);
  }

  // ---------------定义树资源数据请求相关接口-end------------------
  // 添加图标公共方法
  addMarker_IE(image, info, id, type,name) {
    // console.log(image, info, id, type);
    let marker = earthMain.createMarker({
      position: [info.longitude ? info.longitude : 83.62, info.latitude ? info.latitude : 41.3, 1],
      imageUrl: image,
      label: name,
    });
    // marker.id = id;
    marker.infoId = id;
    marker.Kindtype = type;
    earthMain.addGraphic(marker, id);
    const group = earthMain.getGroup(id);
    // gvEarth.camera.flyTo(marker)
    console.log(info.longitude,info.latitude);
    // earthMain.earthUtil.flyToGraph(marker);
    earthMain.earthUtil.flyTo(
          {
            "lon": info.longitude-0.03,
            "lat": info.latitude-0.08,
            "alt": 3267.678667,
            "heading": 23.374675000000025,
            "pitch": -13.983344999999986,
            "roll": 90
          },
          // {"lon":121.42039,"lat":30.738973,"alt":1830.760768,"heading":23.431047999999976,"pitch":-13.926638000000025,"roll":0},
          2
        )
  }
  addCompanyBounds(e) {
    // window.earthMain.clearGroup("companybounds");
    let boundarys = e.boundary
      .map((point) => [point.lon, point.lat, 10])
      .flat(Infinity);
    // 创建datasource
    this.CompanyGroup = window.earthMain.getGroup(e.id);
    // 创建围栏
    this.CompanyFence = window.earthMain.createFence(boundarys, "#ffffff");
    // 添加围栏到datasource
    // window.earthMain.addGraphic(this.CompanyFence, this.CompanyGroup);
    window.earthMain.addGraphic(this.CompanyFence, e.id);
    gvEarth.flyTo(this.CompanyFence, 2000);
  }

}

// -------------------IE methods start ---------------

