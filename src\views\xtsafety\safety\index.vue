<template>
  <div class="safety">
    <div class="left-panel">
      <LeftBase></LeftBase>
    </div>

    <div class="right-panel">
      <RightBase></RightBase>
    </div>
    <div class="bottom-panel">
      <BottomBase></BottomBase>
    </div>
    <div class="tools-panel">
      <Tools></Tools>
    </div>
  </div>
</template>
<script setup>
import LeftBase from "@/views/xtsafety/safety/left/LeftBase";
import RightBase from "@/views/xtsafety/safety/right/RightBase";
import BottomBase from "@/views/xtsafety/safety/bottom/BottomBase";
import Tools from "@/views/xtsafety/common/tools/index";

import { now } from "@vueuse/core";
import { ref } from "vue";
import { set } from "nprogress";

const currentTab = ref("RightBase");
const tabs = {
  RightBase,
};
const ht = ref("应急指挥");
onMounted(() => {
  emitter.on("viewerLoad", (data) => {
    window.viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        112.886802,
        36.118856,
        /*  112.873532,
        36.117711, */
        5807.45585153255
      ),
    });

    addHome();
  });
});
function openToMid() {
  window.open(window.xtmapConfig.xtMidUrl, "_blank");
}
function addHome() {
  const entity_p = new Cesium.Entity({
    position: Cesium.Cartesian3.fromDegrees(112.886802, 36.118856),
    id: "unid_home",
    billboard: {
      image: `${window.xtmapConfig.billboard.path}homeb1.png`,

      height: 130,
      width: 180,
      // height: window.xtmapConfig.billboard.h,
      // width: window.xtmapConfig.billboard.w,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    },
    label: {
      // text: "应急指挥中心",
      text: "",
      font: " 17px MicroSoft YaHei", //bold
      fillColor: Cesium.Color.WHITE,
      // outlineWidth: 2,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      // verticalOrigin: Cesium.VerticalOrigin.TOP,
      pixelOffset: new Cesium.Cartesian2(0, -113), //偏移量
      showBackground: false,
      // backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
      // backgroundPadding: new Cesium.Cartesian2(6, 3),
      // scaleByDistance: new Cesium.NearFarScalar(2000, 1, 12000, 0),
    },
    properties: {
      type: "uuidhome", // 这个是关键
    },
  });
  window.viewer.entities.add(entity_p);
}

const nowTime = ref(new Date().toLocaleTimeString());
const nowDay = ref(new Date().toLocaleDateString());
setInterval(() => {
  nowTime.value = new Date().toLocaleTimeString();
  nowDay.value = new Date().toLocaleDateString();
}, 1000);
</script>

<style scoped lang="scss">
.safety {
  background: url("@/assets/xtui/layout/sidebg.png");
  background-size: 100% 100%;
  height: 100vh;
  width: 100%;
  position: relative;
  pointer-events: none;
  .safety-header {
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 100;
    pointer-events: auto;

    .right-info {
      position: absolute;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      right: 10px;
      top: 10px;
      z-index: 100;
      width: 390px;
      height: 50px;
      background-color: rgba(210, 105, 30, 0);
      .left-time {
        font-family: Source Han Sans;
        font-size: 15px;
        font-weight: bold;

        color: #3cd5fff6;
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .mid-btn {
        width: 100px;
        height: 33px;
        border-radius: 2px;
        // margin-top: 5px;
        margin-left: 30px;
        background-image: url("@/assets/xtui/cockpit/btn3.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #f5f1f1;
        &:hover {
          cursor: pointer;
        }
      }
      .right-btn {
        width: 32px;
        height: 32px;
        background-image: url("@/assets/xtui/cockpit/user2.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        &:hover {
          cursor: pointer;
        }
      }
    }
  }
  .tools-panel {
    position: absolute;
    right: 440px;
    top: 260px;
    z-index: 99;
    pointer-events: auto;
  }
  .left-panel {
    position: absolute;
    left: 10px;
    top: 90px;
    z-index: 99;
    pointer-events: auto;
  }
  .right-panel {
    position: absolute;
    right: 10px;
    top: 90px;
    z-index: 99;
    pointer-events: auto;
  }
  .bottom-panel {
    position: absolute;
    left: 410px;
    bottom: 35px;
    width: calc(100% - 840px);
    height: 220px;
    z-index: 99;
    pointer-events: auto;
    // background-color: brown;
  }
}
</style>
