

<script setup>
import { nextTick, ref, reactive, computed, onMounted } from "vue";
const props = defineProps({
  xttid: {
    type: String,
    required: true,
  },
});
// 预案电子化
// 接收地址栏的xttid
onMounted(() => {});
</script>

<template>
  <div class="case">
    case22222222222

    <div>从地址栏获取到的xttid = {{ xttid }}</div>
  </div>
</template>

<style scoped lang="scss">
.case {
  width: 450px;
  height: 300px;
  background-color: #b4d497;
}
</style>

