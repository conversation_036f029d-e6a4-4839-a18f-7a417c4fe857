export default class DragLable {
  constructor(viewer, options) {
    this._viewer = viewer;
    //屏蔽cesium的默认双击追踪选中entity行为
    this._viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
      Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
    );
    this._options = {
      labelPixelOffset: new Cesium.Cartesian2(100, -100),
      labelBackgroundColor: Cesium.Color.MIDNIGHTBLUE.withAlpha(0.8),
      dragLineColor: Cesium.Color.GOLD,
      dragLineLength: 100 * 1.414,
      dragLinePixelOffset: new Cesium.Cartesian2(50 - (100 * 1.414) / 2, -50),
      ...options,
    };
    this._selectedEntity = undefined;
    this._selectedEntityLine = [];
    this._handler = new Cesium.ScreenSpaceEventHandler(
      this._viewer.scene.canvas
    );
    // 监听拖动
    this._handler.setInputAction(
      (e) => this.handleLeftDown(e),
      Cesium.ScreenSpaceEventType.LEFT_DOWN
    );
    this._handler.setInputAction(
      (e) => this.handleLeftUp(e),
      Cesium.ScreenSpaceEventType.LEFT_UP
    );
    this._handler.setInputAction(
      (e) => this.handleMouseMove(e),
      Cesium.ScreenSpaceEventType.MOUSE_MOVE
    );
  }
   // 合并后的创建实体方法
   createDragLabelEntity(lon, lat, name, id, color, mineOffsets, widthRotation) {
    const entity = new Cesium.Entity({
      id: `entity-id_${id}`,
      position: Cesium.Cartesian3.fromDegrees(lon, lat, 100),
      point: {
        color: color || Cesium.Color.AQUA,  // 使用传入的颜色参数
        pixelSize: 2,
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(20000, 20000000),
      },
    });
    // 添加实体到视图
    this._viewer.entities.add(entity);
    // 初始化标牌和广告牌
    this._initEntityBillboard(entity, mineOffsets, widthRotation);
    this._selectedEntityLine.push(entity);
    return entity;
  }
  // 私有方法：初始化连接线
  _initEntityBillboard(targetEntity, mineOffsets, widthRotation) {
    const canvas = document.createElement("canvas");
    canvas.width = 200;
    canvas.height = 5;
    const cxt = canvas.getContext("2d");
    // 绘制线条
    cxt.lineWidth = 5;
    cxt.strokeStyle = "#FFFFFF";
    cxt.beginPath();
    cxt.moveTo(0, 0);
    cxt.lineTo(200, 0);
    cxt.stroke();
    if (targetEntity) {
      targetEntity.billboard = {
        image: canvas,
        color: this._options.dragLineColor,
        height: 2,
        scale: 1,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(20000, 20000000),
        width: widthRotation.width,
        rotation: Cesium.Math.toRadians(widthRotation.rotation)
      };

      // 统一处理定位逻辑
      const isXPositive = mineOffsets.x >= 0;
      const isYPositive = mineOffsets.y >= 0;
      
      targetEntity.billboard.horizontalOrigin = isXPositive ? 
        Cesium.HorizontalOrigin.LEFT : 
        Cesium.HorizontalOrigin.RIGHT;
      
      targetEntity.billboard.verticalOrigin = isYPositive ? 
        Cesium.VerticalOrigin.BASELINE : 
        Cesium.VerticalOrigin.BOTTOM;

      // 统一计算偏移量
      const xOffset = (Math.abs(mineOffsets.x) - widthRotation.width) / 2;
      const yOffset = mineOffsets.y / 2;
      
      targetEntity.billboard.pixelOffset = new Cesium.Cartesian2(
        isXPositive ? xOffset : -xOffset,
        yOffset
      );
    }
  }
  // 初始化广告牌
  addOtherbillboardEntity(lon, lat, name, id, mineOffsets, url) {
    console.log(lon, lat, name, id, mineOffsets, url);
    // 添加第二个标牌
    const entityBillord = new Cesium.Entity({
      id: id,
      position: Cesium.Cartesian3.fromDegrees(lon, lat, 100),
      billboard: {
        image: `${window.xtmapConfig.plot.path}data/billboard/${url.img}.png`, // default: undefined
        // image: canvasElement,
        width: url.width,
        height: 66,
        scale: 1,
        pixelOffset: mineOffsets,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(20000, 20000000),
        // horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        // verticalOrigin: Cesium.VerticalOrigin.BASELINE,
      },
    });
    this._viewer.entities.add(entityBillord);
    if (mineOffsets.x >= 0 && mineOffsets.y >= 0) {
      entityBillord.billboard.horizontalOrigin = Cesium.HorizontalOrigin.LEFT;
      entityBillord.billboard.verticalOrigin = Cesium.VerticalOrigin.TOP;
    } else if (mineOffsets.x > 0 && mineOffsets.y < 0) {
      entityBillord.billboard.horizontalOrigin = Cesium.HorizontalOrigin.LEFT;
      entityBillord.billboard.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;
    } else if (mineOffsets.x < 0 && mineOffsets.y < 0) {
      entityBillord.billboard.horizontalOrigin = Cesium.HorizontalOrigin.RIGHT;
      entityBillord.billboard.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;
    } else if (mineOffsets.x < 0 && mineOffsets.y > 0) {
      entityBillord.billboard.horizontalOrigin = Cesium.HorizontalOrigin.RIGHT;
      entityBillord.billboard.verticalOrigin = Cesium.VerticalOrigin.TOP;
    }
  }

  updateLabelBillboard(clickEntity, delta) {
    let billboardLineEntity = this._selectedEntityLine.find((item) =>
      item.id.includes(clickEntity.id)
    );
    let billboardLine = billboardLineEntity.billboard;
    let billboard = clickEntity.billboard;
    billboard.pixelOffset = delta;
    billboardLine.width = Math.sqrt(
      Math.pow(delta.x, 2) + Math.pow(delta.y, 2)
    );
    billboardLine.rotation = Cesium.Math.toRadians(
      -Math.atan(delta.y / delta.x) * (180 / Math.PI)
    );
    if (delta.x >= 0 && delta.y >= 0) {
      // 右下
      billboard.horizontalOrigin = Cesium.HorizontalOrigin.LEFT;
      billboard.verticalOrigin = Cesium.VerticalOrigin.TOP;
      billboardLine.horizontalOrigin = Cesium.HorizontalOrigin.LEFT;
      billboardLine.verticalOrigin = Cesium.VerticalOrigin.BASELINE;
      billboardLine.pixelOffset = new Cesium.Cartesian2(
        delta.x / 2 - billboardLine.width / 2,
        delta.y / 2
      );
    } else if (delta.x > 0 && delta.y < 0) {
      //右上
      billboard.horizontalOrigin = Cesium.HorizontalOrigin.LEFT;
      billboard.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;
      billboardLine.horizontalOrigin = Cesium.HorizontalOrigin.LEFT;
      billboardLine.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;
      billboardLine.pixelOffset = new Cesium.Cartesian2(
        delta.x / 2 - billboardLine.width / 2,
        delta.y / 2
      );
    } else if (delta.x < 0 && delta.y < 0) {
      //左上
      billboard.horizontalOrigin = Cesium.HorizontalOrigin.RIGHT;
      billboard.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;
      billboardLine.horizontalOrigin = Cesium.HorizontalOrigin.RIGHT;
      billboardLine.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;
      billboardLine.pixelOffset = new Cesium.Cartesian2(
        delta.x / 2 + billboardLine.width / 2,
        delta.y / 2
      );
    } else if (delta.x < 0 && delta.y > 0) {
      //左下
      billboard.horizontalOrigin = Cesium.HorizontalOrigin.RIGHT;
      billboard.verticalOrigin = Cesium.VerticalOrigin.TOP;
      billboardLine.horizontalOrigin = Cesium.HorizontalOrigin.RIGHT;
      billboardLine.verticalOrigin = Cesium.VerticalOrigin.BASELINE;
      billboardLine.pixelOffset = new Cesium.Cartesian2(
        delta.x / 2 + billboardLine.width / 2,
        delta.y / 2
      );
    }
  }
  
  handleLeftDown(e) {
    const obj = this._viewer.scene.pick(e.position);
    if (obj && obj.collection) {
      // 锁定相机
      console.log(obj, "obj");
      // this._viewer.container.style.cursor = "pointer";
      // this('canvas').css('cursor', 'pointer');//鼠标箭头换成小手
      this.cameraControl(false);
      this._selectedEntity = obj.id;
    }
  }
  handleLeftUp() {
    this.cameraControl(true);
    this._selectedEntity = undefined;
    this._viewer.container.style.cursor = "default";
  }
  handleMouseMove(e) {
    // this._viewer.container.style.cursor = "pointer";
    if (this._selectedEntity) {
      let cartesian3Pos = this._selectedEntity.position.x
        ? this._selectedEntity.position
        : this._selectedEntity.position._value;
      //将笛卡尔坐标转为屏幕坐标
      let windowPos =
        this._viewer.scene.cartesianToCanvasCoordinates(cartesian3Pos);
      if (e.endPosition && e.endPosition.x) {
        //计算移动差值
        const delta = new Cesium.Cartesian2(
          e.endPosition.x - windowPos.x,
          e.endPosition.y - windowPos.y
        );
        this.updateLabelBillboard(this._selectedEntity, delta);
      }
    }
  }
  cameraControl(enable) {
    this._viewer.scene.screenSpaceCameraController.enableTranslate = enable;
    this._viewer.scene.screenSpaceCameraController.enableInputs = enable;
  }
  //销毁资源
  destroy() {
    this._handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOWN);
    this._handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP);
    this._handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    this._handler = undefined;
    this._selectedEntity = undefined;
  }
}
