import request from '@/utils/request'

// 查询承包商人员列表
export function listPerson(query) {
  return request({
    url: '/contractor/person',
    method: 'get',
    params: query
  })
}

// 查询承包商人员详细
export function getPerson(id) {
  return request({
    url: '/contractor/person/' + id,
    method: 'get'
  })
}

// 新增承包商人员
export function addPerson(data) {
  return request({
    url: '/contractor/person',
    method: 'post',
    data: data
  })
}

// 修改承包商人员
export function updatePerson(data) {
  return request({
    url: '/contractor/person',
    method: 'post',
    data: data
  })
}

// 删除承包商人员
export function delPerson(data) {
  return request({
    url: '/contractor/person/',
    method: 'delete',
    data: data
  })
}
