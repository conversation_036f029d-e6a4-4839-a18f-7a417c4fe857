<template>
  <div class="xtree-resource">
    <el-input
      v-model="filterText"
      style="width: 240px"
      placeholder="请输入车牌号"
      class="tree-input"
    />

    <el-tree
      class="xtree-list"
      ref="resourceTreeRef"
      style="max-width: 600px"
      :data="resourceTreeData"
      :props="defaultProps"
      @node-click="handleNodeClick"
      @check-change="handleCheckChange"
      show-checkbox
      node-key="id"
      default-expand-all
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
    >
      <template #default="{ node, data }">
        <span class="custom-tree-node">
          <span>{{ node.label }}</span>
          <!-- <span class="custom-tree-node-span" v-if="!data.children">
            <a style="color: greenyellow" v-if="data.login"> 在线 </a>
            <a style="color: red" v-else> 离线 </a>
          </span> -->
        </span>
      </template>
    </el-tree>
  </div>
  <PopupSlot
    v-if="isPopShow"
    ref="popupRef"
    id="popup-info-xtree-r"
    @closePopup="closePopup"
    popupTitle="资源详情"
  >
    <!-- 如果多个不同面板 就在这里加v-if -->
    <template #content>
      <div class="inner-content">
        <ul>
          <li>类型：{{ popupItem.type }}</li>
          <li>
            名称：{{
              popupItem.name ||
              popupItem.materialName ||
              popupItem.unitName ||
              popupItem.expertName
            }}
          </li>
          <li>位置：{{ popupItem.address || popupItem.affiliation }}</li>
          <li>联系人：{{ popupItem.director || popupItem.expertName }}</li>
          <li>联系方式：{{ popupItem.telephone }}</li>

          <!-- <li>纬度：{{ popupItem.lat.toFixed(6) }}</li> -->
        </ul>
      </div>
    </template>
  </PopupSlot>
</template>

<script setup>
import { nextTick, ref, reactive, computed, onMounted } from "vue";

import { ElLoading, ElMessage } from "element-plus";
import PopupSlot from "./PopupSlot";
import PoiUtils from "./PoiUtilsTree.js";
import {
  getResourceTree,
  getResourcesExpert,
  getResourcesTeam,
  getResourcesGoods,
  getResourcesMedical,
  getResourcesRefuge,
  getResourcesStore,
} from "@/api/tree/index";

// ---------------------- 筛选框 ------------------------------
const { proxy } = getCurrentInstance();
const filterText = ref("");
const resourceTreeRef = ref();
watch(filterText, (val) => {
  resourceTreeRef.value.filter(val);
});
const filterNode = (value, data) => {
  if (!value) return true;
  return data.number_plate.includes(value);
};

// ---------------------- 数据列表 ------------------------------

const resourceTreeData = ref([]);
const defaultProps = {
  children: "children",
  label: "label",
};
getResourceTree().then((res) => {
  resourceTreeData.value = res.data;
});

// ---------------------- 点击事件 --------------------

const handleNodeClick = (data, node, event) => {
  // console.log(data, node, event);
  // currentCarInfo.driver = data.driver;
  // currentCarInfo.type = data.type;
};

// 0. 背景：每个叶子节点（最小单位）点击时只加载一个图标点（前提），不是按照一类加载的
// 1. 判断是不是叶子节点
// 2. 需要一个中间变量来存储选中的节点，便于删除图标，反向取消选中

const handleCheckChange = (data, checked, indeterminate) => {
  // console.log("Node:", data);
  // console.log("Checked:", checked);
  if (checked && !data.children.length && data.id) {
    // 当节点被选中且没有子节点时，创建一个新的数据源并添加到poiCollectionArr中

    getResourceDataByType(data.id, data.type).then((r) => {
      console.log(r, "rrr");

      if (r !== "expert") {
        console.log("11111");

        r.type = data.type;
        const e = PoiUtils.createPoi(r, "xtree-r-type", data.type);
        xtreeResourceCustomDS.entities.add(e);
        viewer.flyTo(e);
      }
    });
  } else if (!checked && !data.children.length && data.id) {
    // 当节点取消选中且没有子节点时，移除相关数据源
    xtreeResourceCustomDS.entities.removeById(data.id);

    isPopShow.value = false;
    postRenderingFn?.();
  }
};

// ---------------------- popslot --------------------
const isPopShow = ref(false);
const popupItem = ref({});
let postRenderingFn = reactive();

function eventCallbackXtree(viewer, pick) {
  if (
    !pick.id.properties ||
    !pick.id.properties.type._value ||
    pick.id.properties.type._value !== "xtree-r-type"
  ) {
    return;
  }

  // console.log(pick.id, "pick.id");
  getResourceDataByType(pick.id.id, pick.id.properties.query._value).then((r) => {
    // console.log(r, "rrrrrrrrrr");
    popupItem.value = {};
    popupItem.value = r;
    popupItem.value.type = convertTypeToTxt[pick.id.properties.query._value];
    isPopShow.value = true;
    nextTick(() => {
      const popDom = window.document.querySelector("#popup-info-xtree-r");
      const height = popDom.offsetHeight;
      const width = popDom.offsetWidth;
      postRenderingFn?.();
      postRenderingFn = viewer.scene.postRender.addEventListener(() => {
        const screenC = viewer.scene.cartesianToCanvasCoordinates(
          pick.id.position._value
        );
        if (screenC) {
          popDom.style.left = screenC.x - 1 + "px";
          popDom.style.top = screenC.y - height - 45 + "px";
        }
      });
    });
  });
}

function closePopup() {
  postRenderingFn?.();
  isPopShow.value = false;
}
// const resultInfo = ref();

// 根据类别获取资源数据
const getResourceDataByType = async (id, type) => {
  let r;
  switch (type) {
    case "expert":
      r = await getResourcesExpert(id);
      popupItem.value = r.data;
      popupItem.value.type = "应急专家";
      r.data = "expert";

      nextTick(() => {
        isPopShow.value = true;
      });

      break;
    case "team":
      r = await getResourcesTeam(id);

      break;
    case "goods":
      r = await getResourcesGoods(id);

      break;
    case "refuge":
      r = await getResourcesMedical(id);

      break;
    case "medical":
      r = await getResourcesMedical(id);

      break;
    case "store":
      r = await getResourcesStore(id);
      // r = res.data;
      break;
    default:
      break;
  }

  return r.data;
  // return resultInfo.value;
};
const convertTypeToTxt = {
  expert: "应急专家",
  team: "救援队伍",
  goods: "应急物资",
  refuge: "避难场所",
  medical: "医疗机构",
  store: "应急仓库",
};

// ===============================vue hook================================

let xtreeResourceCustomDS = null;

function czInitFn() {
  xtreeResourceCustomDS = new Cesium.CustomDataSource("xtree-r-data");
  window.viewer.dataSources.add(xtreeResourceCustomDS);
  // xtreeResourceCustomDS.entities.add(tempPoint);
  // xtreeResourceCustomDS.entities.removeAll();
}

onMounted(() => {
  /* emitter.on("viewerLoad", () => {
    console.log("oooo--111111---");
  });
  console.log("oooo-222222----"); */
  czInitFn();
  // PoiUtils.addListener(window.viewer, eventCallbackXtree);
});

onActivated(() => {
  // if (PoiUtils.getListener()) return;
  PoiUtils.addListener(window.viewer, eventCallbackXtree);
  // console.log(PoiUtils.getListener(), "onActivated");
});

onDeactivated(() => {
  if (isPopShow.value) isPopShow.value = false;

  PoiUtils.removeListener();
  // console.log(PoiUtils.getListener(), "onDeactivated");

  // if (typeof postRenderingFn === "function" && postRenderingFn())
  postRenderingFn?.();

  // 关闭面板要不要清空地图点？如果要清空，那就在mounted里创建事件
  // xtreeResourceCustomDS.entities.removeAll();

  // 关闭面板如何清空树节点？
});

function MSG(item) {
  const loading = ElLoading.service({
    lock: true,
    text: "获取GPS信息中...请稍等",
    background: "rgba(0, 0, 0, 0.7)",
  });

  const p = {
    start: proxy
      .dayjs(new Date().getTime() - 24 * 60 * 60 * 1000)
      .format("YYYY-MM-DD HH:mm:ss"),
    end: proxy.dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
  };
  getCarGPS(p).then((res) => {
    // console.log(res);
    loading.close();
    if (res.records.length === 0) {
      // alert("暂无数据");
      ElMessage({
        message: `暂无GPS位置轨迹信息`,
        type: "warning",
        plain: true,
      });
      return;
    }
  });
}

const append = (data) => {
  /* const newChild = { id: id++, label: "testtest", children: [] };
  if (!data.children) {
    data.children = [];
  }
  data.children.push(newChild);
  dataSource.value = [...dataSource.value]; */
};

const remove = (node, data) => {
  /* const parent = node.parent;
  const children = parent.data.children || parent.data;
  const index = children.findIndex((d) => d.id === data.id);
  children.splice(index, 1);
  dataSource.value = [...dataSource.value]; */
};
</script>

<style scoped lang="scss">
.xtree-resource {
  padding: 0 10px 10px 10px;
  width: 250px;
  // height: 280px;
  // background-color: rgba(100, 148, 237, 0.534);

  .tree-input {
    margin-bottom: 10px;
    :deep(.el-input__wrapper) {
      background-color: transparent;
      border: 1px solid rgb(90, 174, 226);
      box-shadow: none;
      border-radius: 0;
    }
  }
  .xtree-list {
    height: 300px;
    overflow: scroll;
  }
  :deep(.el-tree) {
    background-color: rgba(0, 51, 255, 0);

    padding: 13px;

    overflow: auto;
    :deep(.el-tree-node__label) {
      font-size: 16px;
      color: rgb(255, 255, 255);
    }
  }

  :deep(.el-tree-node__content) {
    background-color: rgba(137, 43, 226, 0);
  }
  :deep(.el-tree-node.is-current > .el-tree-node__content) {
    /* 当前选中后的颜色 */
    background-color: rgba(48, 138, 234, 0.356);
  }
  :deep(.el-tree-node .el-tree-node__content .el-tree-node__label) {
    color: rgb(255, 255, 255);
  }
  :deep(.el-tree-node .el-tree-node__content:hover) {
    /* 鼠标浮动的颜色 */
    background-color: rgba(37, 107, 183, 0.2);
    /* background-color: rgba(37, 107, 183, 0.469); */
  }
  :deep(.el-tree-node.is-current.is-focusable:hover) {
    /* 鼠标浮动的颜色 */
    background-color: rgba(135, 183, 234, 0);
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // font-size: 16px;
    padding-right: 8px;
    color: white;
    .custom-tree-node-span {
      // display: flex;
      // align-items: center;
      color: white;
    }
  }

  .car-info {
    display: flex;
    flex-direction: column;
  }
}
.inner-content {
  width: 230px;
  // height: 185px;
  li {
    min-height: 30px;
    margin-bottom: 5px;
  }
}
</style>
