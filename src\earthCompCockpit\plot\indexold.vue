 
<template>
  <div class="draw-icon">
    <div class="draw-tree-menu">
      <!--  	@check-change="checkTreeNode"-->
      <el-tree
        :data="treeData"
        show-checkbox
        default-expand-all
        ref="resTree"
        highlight-current
        :props="defaultProps"
        @node-click="openInfo"
      >
      </el-tree>
    </div>
    <div class="draw-tree-content">
      <ul>
        <li v-for="item in imgList" @click="drawIconX(item.imgName)">
          <img :src="`data/image/emergIcon/${iconType}/${item.imgName}.png`" />
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
let popDom;
let popDomR;

let poiCollectionArr = [];

import axios from "axios";
import { getCoor, drawCenter, removeCenter } from "./drawIconX";
import t_data from "./treeData";
export default {
  name: "DrawIcon",
  components: {},

  data() {
    return {
      treeData: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      imgList: [],
      iconType: "",
      type_icon: [],
    };
  },
  created() {
    this.treeData = t_data;
  },
  mounted() {
    window.localStorage.setItem("reshow_type_icon", []);
  },
  watch: {
    /* 推荐写法 */
    outTreeData(val) {
      this.treeData = val;
    },
  },
  methods: {
    openInfo(node) {
      if (!node.children) {
        // console.log(node.type, '----------ytyep');
        axios(`data/geojson/drawIcon/${node.type}.json`).then((res) => {
          console.log(res, "-----------");
          console.log(res.data.content, "------zzz");
          this.iconType = node.type;
          this.imgList = res.data.content;
        });
      }
    },
    /* openInfo(node) {
			console.log(node);
			axios(`data/geojson/drawIcon/${node.type}.json`).then((res) => {
				console.log(res, '-----------');
				console.log(res.data.content, '------zzz');
				this.imgList = res.data.content;
			});
		}, */
    drawIconX(i) {
      let parentUrl = this.iconType;
      // console.log(i, parentUrl, `data/image/emergIcon/${parentUrl}/${i}`);
      let allUrl = `data/image/emergIcon/${parentUrl}/${i}`;
      getCoor(window.viewer, i, parentUrl, 64).then((res) => {
        let item = {
          coor: res,
          imgUrl: allUrl,
        };
        this.type_icon.push(item);

        window.localStorage.setItem(
          "reshow_type_icon",
          JSON.stringify(this.type_icon)
        );
      });
    },
  },

  destroyed() {},
};
</script>

<style scoped>
.draw-tree-menu {
  padding: 10px;
}
.draw-tree-menu >>> .el-tree {
  background-color: rgba(0, 51, 255, 0);
  padding: 13px;
  height: 140px;
  overflow: auto;
}
.draw-tree-menu >>> .el-tree-node__label {
  font-size: 16px;
}
.draw-tree-menu >>> .el-tree-node__content {
  background-color: rgba(137, 43, 226, 0);
}
.draw-tree-menu >>> .el-tree-node.is-current > .el-tree-node__content {
  /* 当前选中后的颜色 */
  background-color: rgba(48, 138, 234, 0.356);
}
.draw-tree-menu >>> .el-tree-node .el-tree-node__content .el-tree-node__label {
  color: rgb(255, 254, 254);
}
.draw-tree-menu >>> .el-tree-node .el-tree-node__content:hover {
  /* 鼠标浮动的颜色 */
  background-color: rgba(37, 107, 183, 0.2);
  /* background-color: rgba(37, 107, 183, 0.469); */
}
.draw-tree-menu >>> .el-tree-node.is-current.is-focusable:hover {
  /* 鼠标浮动的颜色 */
  background-color: rgba(135, 183, 234, 0);
  /* background-color: rgba(135, 183, 234, 0.469); */
}

::-webkit-scrollbar {
  width: 5px;
}
::-webkit-scrollbar-track {
  background-color: #9cbef2dd;
}
::-webkit-scrollbar-thumb {
  background-color: #468af0ee;
}
::-webkit-scrollbar-thumb:hover {
  background-color: #468af0ee;
}
::-webkit-scrollbar-thumb:active {
  background-color: #f06c00;
}

#tree-poi-pop {
  display: none;
  width: 230px;
  height: 180px;
  /* background-color: burlywood; */
  position: absolute;
  left: 0;
  top: 0;
  background-image: url("../../../../public/data/image/emerUI/popup.png");
  background-size: 100% 100%;
  padding: 5px 10px;
}
.info-left-title {
  /* width: 60%; */
  width: fit-content;
  padding: 5px;
  margin: 5px;
  border-bottom: solid 1px rgb(42, 233, 211);
  color: white;
}
.info-left-content {
  padding: 5px;
  margin: 5px;
  color: white;
  text-indent: 28px;
  height: 101px;
  overflow: auto;
  font-size: 14px;
}
.closepop {
  position: absolute;
  right: 20px;
  top: 20px;
}
#tree-right-pop {
  display: none;
  width: 100px;
  height: 30px;
  /* background-color: rgb(88, 151, 141); */
  position: absolute;
  left: 0;
  top: 0;
  padding: 3px;
  /* display: flex;
	align-items: center; */
}
#tree-right-pop >>> .el-button {
  background-color: #0e2954a1;
  border: 1px solid rgb(9, 83, 117);
  color: white;
}
/* -----------------------content---------------------- */
.draw-tree-content {
  height: 150px;
  /* background-color: #f06c0074; */
  padding: 10px;
}
.draw-tree-content ul {
  margin: 0;
  padding: 10px 20px;
  border: 1px solid rgba(24, 76, 159, 0.817);
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  height: 140px;
  overflow: auto;
}
.draw-tree-content ul li {
  width: 64px;
  height: 64px;
  list-style: none;
  margin: 0 10px 8px 0;
}
.draw-tree-content ul li img {
  width: 64px;
  height: 64px;
}
</style>
