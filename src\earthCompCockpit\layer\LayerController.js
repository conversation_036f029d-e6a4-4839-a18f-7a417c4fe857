/*
 * @Descripttion:
 * @LastEditTime: 2022-01-12 18:32:50
 */
function czAddImageryLayer(options) {
	switch (options.serviceType) {
		case 'WMTS':
			options.layerName = new Cesium.ImageryLayer(
				new Cesium.WebMapTileServiceImageryProvider({
					url: options.url,
					layer: options.layer,
					format: 'image/png',
					style: options.style || '',
					maximumLevel: options.maximumLevel || 5,
					// tilingScheme:
					// 	options.tilingScheme ||
					// 	new Cesium.GeographicTilingScheme(),
					/* tilingScheme:
						options.tilingScheme ||
						new Cesium.GeographicTilingScheme(), */
					// 默认加载databox,对应的就是地图预览请求参数里的 tilematrixset
					tileMatrixSetID: options.tileMatrixSetID,
					// tileMatrixSetID: options.tileMatrixSetID || 'EPSG:4326',
					// tileMatrixLabels: options.tileMatrixLabels || '',
					subdomains: options.subdomains,
				})
			);
			window.viewer.imageryLayers.add(options.layerName);
			break;
		case 'WMS':
			break;
		case 'TMS':
			break;
		case 'XYZ':
			options.layerName = new Cesium.ImageryLayer(
				new Cesium.UrlTemplateImageryProvider({
					// 此处要注意url中的xyz顺序，以及与ts的配合情况
					url: options.url,
					tilingScheme:
						options.tilingScheme ||
						new Cesium.GeographicTilingScheme(),
				})
			);
			window.viewer.imageryLayers.add(options.layerName);
			break;
		case 'CZDEM':
			window.viewer.terrainProvider = new Cesium.CesiumTerrainProvider({
				url: options.url,
			});
			break;
		case 'JSON':
			window.alert('json');
			break;
		case 'AMAP':
			break;
		case 'BMAP':
			break;
		case 'GMAP':
			break;

		default:
			break;
	}
}

function czRemoveImageryLayer(name) {
	// 如果是imageryLayers.addImageryProvider添加的图层，就要先用get(index)选出来再删除
	/* window.viewer.imageryLayers.addImageryProvider(window.wtms_image);
		this.zlayer = window.viewer.imageryLayers.get(1); */
	if (name === 'cz_dem') {
		window.viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider({});
	} else {
		window.viewer.imageryLayers.remove(name);
	}
}
export { czAddImageryLayer, czRemoveImageryLayer };
// export default czAddImageryLayer;

/* window.gd_imglayer = viewer.imageryLayers.addImageryProvider(
				new Cesium.UrlTemplateImageryProvider({
					url: 'http://wprd03.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scl=1&style=6',
				})
			);
			window.gd_annolayer = viewer.imageryLayers.addImageryProvider(
				new Cesium.UrlTemplateImageryProvider({
					url: 'http://wprd03.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scl=1&style=8',
				})
			); */

/* var anno = new Cesium.UrlTemplateImageryProvider({
                // url: "static/tiles/{z}/{x}/{y}.png"
                url:
                "http://127.0.0.1:8300/sdc/tiles/annotation/{z}/{x}/{reverseY}.png",
                tilingScheme: new Cesium.GeographicTilingScheme(),
            });
            viewer.imageryLayers.addImageryProvider(anno); */
