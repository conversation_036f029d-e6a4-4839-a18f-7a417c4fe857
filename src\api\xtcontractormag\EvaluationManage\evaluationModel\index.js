import request from '@/utils/request'

// 查询评价模型指标配置列表
export function listEvaluationModel(query) {
  return request({
    url: '/contractor/evaluationModel',
    method: 'get',
    params: query
  })
}

// 查询评价模型指标配置详细
export function getEvaluationModel(id) {
  return request({
    url: '/contractor/evaluationModel/' + id,
    method: 'get'
  })
}

// 新增评价模型指标配置
export function addEvaluationModel(data) {
  return request({
    url: '/contractor/evaluationModel',
    method: 'post',
    data: data
  })
}

// 修改评价模型指标配置
export function updateEvaluationModel(data) {
  return request({
    url: '/contractor/evaluationModel',
    method: 'post',
    data: data
  })
}

// 删除评价模型指标配置
export function delEvaluationModel(data) {
  return request({
    url: '/contractor/evaluationModel/',
    method: 'delete',
    data: data
  })
}
