class spatialQuery {
	constructor() {
		this.evtTypes = [
			// {
			//   name:"监控树",  //组件标识
			//   key:"videoSearch",  //上球图标组标识
			//   backfun:null,  //执行查询后的回调函数
			//   clearGraphicFun:null,  //清除上球图标函数
			// }
		];
		this.drawAction = [];
		this.queryStore = new Map();
	}

	/***
	 * 添加查询对象
	 * { obj.name }:组件标识
	 * { obj.key }:上球图标组标识
	 * { obj.backfun }:执行查询后的回调函数
	 * { obj.clearGraphicFun }:清除上球图标函数
	 */
	addQueryFunc(obj) {
		let items = this.evtTypes.filter((v) => {
			return v.key == obj.key;
		});
		if (items.length == 0) {
			this.evtTypes.push(obj);
		}
	}

	//清除查询对象
	deleteQueryFunc(key) {
		let idx = this.evtTypes.findIndex((v) => {
			return v.key == key;
		});
		if (idx > -1) {
			this.evtTypes.splice(idx, 1);
		}
	}

	//框选查询 执行回调
	queryRectangle() {
		let func = this.evtTypes.map((v) => {
			return v.backfun;
		});
		if (func.length == 0) {
			func = null;
		}
		let obj_f = window.earthMain.drawRectangle(func);
		this.drawAction.push(obj_f);
	}

	//多边形选查询 执行回调
	queryPolygon() {
		let func = this.evtTypes.map((v) => {
			return v.backfun;
		});
		if (func.length == 0) {
			func = null;
		}
		let obj_f = window.earthMain.drawPolygon(func);
		this.drawAction.push(obj_f);
	}

	//缓冲区查询-绘制点 执行回调
	drawPoint(func = null) {
		let obj_f = window.earthMain.drawPoint(func);
		this.drawAction.push(obj_f);
	}

	//缓冲区查询-添加圆 执行回调
	queryCircle() {
		let point = this.queryStore.get('circleCenter');
		let radius = this.queryStore.get('circleRadius');
		let param = {
			lon: point.lon,
			lat: point.lat,
			radius: radius,
		};
		// window.spatialQuery.queryStore.set("circleCenter",data.position);
		// window.spatialQuery.queryStore.set("circleRadius",e.target.value);
		let circle = window.earthMain.addCircle(param);
		window.earthMain.viewer.graphicLayer.add(circle);
		// window.spatialQuery.drawAction.push(circularGraphic);
		this.drawAction.push(circle);
		//执行回调
		// this.evtTypes.map((v)=>{
		//   return v.backfun;
		// })
		this.evtTypes.forEach((vf, i) => {
			return vf.backfun({
				type: 'Circle',
				pos: param,
			});
		});
	}

	//清除绘制和图标
	clearAction() {
		if (this.drawAction.length > 0) {
			this.evtTypes.forEach((vf, i) => {
				//清除图标
				vf.clearGraphicFun();
			});
			//清除绘制图形
			this.drawAction.forEach((vp, i) => {
				earthMain.viewer.graphicLayer.remove(vp);
			});
			this.drawAction = [];
			//清除绘制缓存
			if (this.queryStore.size > 0) {
				this.queryStore.clear();
			}
		}
	}
}

const qqq = [
	{
		uid: '12010400001320000372',
		device_path: '/dist_4/link_30',
		online_ts: 1730626418,
		name: '慈林大街-18-同旺小学路口南人行道西',
		device_type: 'device/IPC',
		statistics: null,
		longitude: 112.892566,
		latitude: 36.119216,
		status: 'online',
		manufacturer: 'uniview',
		model: 'uniview',
		disabled: false,
		leftSlot: true,
		rightSlot: true,
		children: [],
	},
	{
		uid: '12010400001320000201',
		device_path: '/dist_4/link_30',
		online_ts: 0,
		name: '北外环-北刘村口东北11号杆机动车道东',
		device_type: 'device/IPC',
		statistics: null,
		longitude: 112.912785,
		latitude: 36.138194,
		status: 'offline',
		manufacturer: 'uniview',
		model: 'uniview',
		disabled: false,
		leftSlot: true,
		rightSlot: true,
		children: [],
	},
	{
		uid: '12010400001320000415',
		device_path: '/dist_4/link_30',
		online_ts: 1730626417,
		name: '东西大街--152-神农路丁字口',
		device_type: 'device/IPC/IPDome',
		statistics: null,
		longitude: 112.893333,
		latitude: 36.119823,
		status: 'online',
		manufacturer: 'uniview',
		model: 'uniview',
		disabled: false,
		leftSlot: true,
		rightSlot: true,
		children: [],
	},
	{
		uid: '12010400001320000405',
		device_path: '/dist_4/link_30',
		online_ts: 1730626418,
		name: '慈林大街ZZXL-4-慈林大街新建巷北人行道西',
		device_type: 'device/IPC',
		statistics: null,
		longitude: 112.892784,
		latitude: 36.11784,
		status: 'online',
		manufacturer: 'uniview',
		model: 'uniview',
		disabled: false,
		leftSlot: true,
		rightSlot: true,
		children: [],
	},
	{
		uid: '12010400001320000399',
		device_path: '/dist_4/link_30',
		online_ts: 0,
		name: '慈林大街16-慈林大街精卫路口人行道东',
		device_type: 'device/IPC',
		statistics: null,
		longitude: 112.891604,
		latitude: 36.117915,
		status: 'offline',
		manufacturer: 'uniview',
		model: 'uniview',
		disabled: false,
		leftSlot: true,
		rightSlot: true,
		children: [],
	},
	{
		uid: '12010400001320000400',
		device_path: '/dist_4/link_30',
		online_ts: 0,
		name: '慈林大街16-慈林大街精卫路口人行道西',
		device_type: 'device/IPC',
		statistics: null,
		longitude: 112.891604,
		latitude: 36.117911,
		status: 'offline',
		manufacturer: 'uniview',
		model: 'uniview',
		disabled: false,
		leftSlot: true,
		rightSlot: true,
		children: [],
	},
	{
		uid: '12010400001320000401',
		device_path: '/dist_4/link_30',
		online_ts: 1730626418,
		name: '慈林大街17-巴黎春天机动车道东',
		device_type: 'device/IPC',
		statistics: null,
		longitude: 112.893253,
		latitude: 36.117889,
		status: 'online',
		manufacturer: 'uniview',
		model: 'uniview',
		disabled: false,
		leftSlot: true,
		rightSlot: true,
		children: [],
	},
	{
		uid: '12010400001320000402',
		device_path: '/dist_4/link_30',
		online_ts: 1730626418,
		name: '慈林大街17-巴黎春天机动车道西',
		device_type: 'device/IPC',
		statistics: null,
		longitude: 112.893239,
		latitude: 36.117875,
		status: 'online',
		manufacturer: 'uniview',
		model: 'uniview',
		disabled: false,
		leftSlot: true,
		rightSlot: true,
		children: [],
	},
];
