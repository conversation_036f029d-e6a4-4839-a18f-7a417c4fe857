<template>
  <div class="xtplatform">
    <router-view class="router-view"></router-view>
    <XtMap></XtMap>
  </div>
</template>

<script setup>
import XtMap from "@/views/xtmap/index";
</script>

<style scoped lang="scss">
.xtplatform {
  width: 100%;
  height: 100vh;
  // position: relative;
  // background: url("@/assets/xtui/layout/leftbg.png");
  // background-size: 100% 100%;

  .router-view {
    width: 100%;
    height: 100vh;

    pointer-events: none;
    background-image: url("@/assets/xtui/layout/sidebg.png");
    background-size: 100% 100%;
    z-index: 90;
  }
}
</style>

