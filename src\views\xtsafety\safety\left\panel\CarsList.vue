<template>
  <div class="sjyj">
    <el-table
      ref="tableRef"
      :data="carList"
      max-height="250"
      style="width: 95%; margin-top: 5px"
      @row-click="rowclick"
    >
      <el-table-column
        prop="numberplate"
        label="车牌号"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column prop="manager" label="司机姓名" align="center" />
      <el-table-column prop="type" label="车辆类型" align="center">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.type == '0'" size="small"
            >医疗救护车
          </el-tag>
          <el-tag type="info" v-else-if="scope.row.type == '1'" size="small"
            >道路清障车
          </el-tag>
          <el-tag type="warning" v-else-if="scope.row.type == '2'" size="small"
            >防汛抢险车
          </el-tag>
          <el-tag type="warning" v-else size="small">应急救援指挥车 </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" align="center">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.status == '0'" size="small"
            >维修
          </el-tag>
          <el-tag type="info" v-else-if="scope.row.status == '1'" size="small"
            >正常</el-tag
          >
          <el-tag type="warning" v-else-if="scope.row.status == '2'" size="small"
            >外出</el-tag
          >
          <el-tag type="warning" v-else size="small">故障</el-tag>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script setup>
const props = defineProps({
  carList: {
    type: Object,
    required: true,
    default: [],
  },
});
const tableRef = ref(null); //表格实例
onMounted(() => {
  scroll(tableRef.value.$refs.bodyWrapper); //设置滚动
});

const scroll = (tableBody) => {
  let isScroll = true; //滚动
  const tableDom = tableBody.getElementsByClassName("el-scrollbar__wrap")[0];
  //鼠标放上去，停止滚动；移开，继续滚动
  tableDom.addEventListener("mouseover", () => {
    isScroll = false;
  });
  tableDom.addEventListener("mouseout", () => {
    isScroll = true;
  });
  setInterval(() => {
    if (isScroll) {
      tableDom.scrollTop += 3; //设置滚动速度
      if (tableDom.clientHeight + tableDom.scrollTop == tableDom.scrollHeight) {
        tableDom.scrollTop = 0;
      }
    }
  }, 100);
};
const rowclick = (row, column, event) => {};
</script>
<style lang="scss" scoped>
// // 表格定制
// .sjyj {
//   // 表格部分样式
//   // 最外层透明
//   :deep(.el-table),
//   :deep(.el-table__expanded-cell) {
//     background-color: transparent;
//     color: #93dcfe;
//     font-size: 24px;
//     cursor: pointer;
//   }
//   /* 表格内背景颜色  */
//   :deep(.el-table th),
//   :deep(.el-table tr),
//   :deep(.el-table td) {
//     background-color: transparent;
//     border: 0px;
//     color: #93dcfe;
//     // font-size: 24px;
//     // font-family: Source Han Sans CN Normal, Source Han Sans CN Normal-Normal;
//     // font-weight: Normal;
//   }
//   :deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
//     background: #00ffff1f;
//   }
//   /* // 去掉最下面的那一条线  */
//   .el-table::before {
//     height: 0px;
//   }

//   /* // 设置表格行高度 */
//   :deep(.el-table__body tr),
//   :deep(.el-table__body td) {
//     padding: 0;
//     height: 40px;
//   }

//   /* // 修改高亮当前行颜色 */
//   :deep(.el-table tbody tr:hover) > td {
//     background: #84a7d271 !important;
//   }
//   :deep(.el-table .el-table__header-wrapper th) {
//     background-color: rgba(#63abe8, 0.4) !important;
//     // color: rgba(#ffffff, 1) !important;
//     color: rgb(186, 212, 245) !important;
//     font-size: 24px !important;
//     font-family: Source Han Sans CN Normal, Source Han Sans CN Normal-Normal;
//     font-weight: blod;
//   }
//   /* // 取消当前行高亮 */
//   // :deep(.el-table tbody tr {
//   //   pointer-events: none;
//   // }

//   /* 修改表头样式-加边框 */
//   // :deep(.el-table__header-wrapper {
//   //   border: solid 1px #04c2ed;
//   // }

//   /* // 表格斑马自定义颜色 */
//   :deep(.el-table__row.warning-row) {
//     background: #01205a;
//   }

//   /* 去掉表格里的padding */
//   :deep(.el-table .cell, .el-table th div) {
//     padding-left: 0px;
//     padding-right: 0px;
//     padding-top: 0px;
//     padding-bottom: 0px;
//     font-size: 12px;
//     overflow: hidden;
//     text-overflow: ellipsis;
//   }

//   :deep(.el-table__inner-wrapper::before) {
//     height: 0px !important;
//     background-color: #ebeef500 !important;
//   }
//   :deep(.el-tag) {
//     background-color: transparent;
//     border-width: 0px;
//   }
// }
</style>
