<!--
 * @Descripttion: 
 * @LastEditTime: 2021-12-17 18:22:36
-->
<template>
  <div class="cz_layers">
    <div class="czlc_header">图层管理</div>
    <div class="czlc_tree">
      <el-tree
        :data="layerTreeData"
        show-checkbox
        default-expand-all
        node-key="id"
        ref="tree"
        highlight-current
        :props="defaultProps"
        @check-change="handleCheckChange"
      >
      </el-tree>
    </div>
  </div>
</template>

<script>
import { czAddImageryLayer, czRemoveImageryLayer } from "./LayerController.js";
// import dragElement from '@/js/tools/dragElement.js';
import "./layerConfig.js";
export default {
  name: "Layer",
  data() {
    return {
      layerTreeData: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
    };
  },
  created() {
    this.getLayerTree();
  },
  mounted() {
    // $ref
    // dragElement('.czlc_header', '.plg-content');
  },
  methods: {
    getLayerTree() {
      for (const key in window.czLayerConfig) {
        //获取索引赋给tree的id
        let treeId = Object.keys(window.czLayerConfig).indexOf(key);
        let treeElement = window.czLayerConfig[key];
        let tempArr = [];
        for (const _key in treeElement) {
          if (_key != "name") {
            tempArr.push({
              label: treeElement[_key].treeName,
              layerValue: _key,
            });
          }
        }
        this.layerTreeData.push({
          id: treeId,
          label: treeElement.name,
          labelValue: key,
          children: [...tempArr],
        });
        tempArr = [];
        // console.log(this.layerTreeData, 'layerGroup');
      }
    },
    handleCheckChange(data, checked) {
      let optionName = data.layerValue;
      let layerGroup = this.getNodeParent(optionName);
      if (layerGroup !== undefined) {
        if (checked) {
          czAddImageryLayer(window.czLayerConfig[layerGroup][optionName]);
        } else {
          czRemoveImageryLayer(window.czLayerConfig[layerGroup][optionName].layerName);
        }
      }
    },

    //获取父节点
    getNodeParent(nodeValue) {
      for (const item of this.layerTreeData) {
        for (const i of item.children) {
          if (i.layerValue == nodeValue) {
            return item.labelValue;
          }
        }
      }
    },
    handleNodeClick(node, val) {
      console.log(node, val);
      console.log(val.parent.data.label);
    },
    handleCurrentChange(node, val) {
      // @current-change="handleCurrentChange"
      console.log(node, "node");
      console.log(val, "val");
    },
  },
  watch: {},
  computed: {},
  destroyed() {},
};
</script>

<style scoped lang="scss">
.cz_layers {
  width: 230px;
  height: max-content;
  // background: url("@/assets/xtui/tools/dialogback.png");
  // background-size: 100% 100%;
  // display: flex;
  // flex-direction: column;
  // color: antiquewhite;
  // font-family: ysbthzt;
  background: rgba(8, 76, 124, 0.5);
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%) 2;
  backdrop-filter: blur(10px);
  padding: 10px;
}
.czlc_header {
  background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center center;
  background-size: 100% 100%;
  height: 32px;
  width: 100%;
  margin: 5px 0px 15px 0px;
  padding: 5px 30px;
  font-family: Source Han Sans;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 0px;
  color: aliceblue;
}
.czlc_tree {
  .el-tree {
    padding: 10px;
    width: 210px;
    min-height: 250px;
    color: #cce3ff;
    background: rgba(26, 69, 86, 0);
    max-height: 18rem;
    overflow: auto;
    border: 0px solid rgb(53, 183, 234);
    font-size: 12px;
    font-weight: bold;
  }

  :deep(.el-tree-node__content:hover) {
    background: transparent !important;
    color: rgb(96, 167, 230);
  }

  :deep(.el-tree > .el-tree-node > .el-tree-node__content) {
    background: transparent !important;
    height: 30px;
  }

  :deep(.el-tree > .el-tree-node:focus > .el-tree-node__content) {
    background: transparent !important;
  }

  :deep(.el-tree
      > .el-tree-node
      > .el-tree-node__children
      > .el-tree-node
      > .el-tree-node__content) {
    background: transparent !important;
    height: 30px;
  }

  :deep(.el-tree
      > .el-tree-node
      > .el-tree-node__children
      > .el-tree-node
      > .el-tree-node__children
      > .el-tree-node
      > .el-tree-node__content) {
    background: transparent !important;
    padding: 15px;
    height: 30px;
    :hover {
      background: transparent !important;
      color: rgb(96, 167, 230);
    }
  }
}
</style>
