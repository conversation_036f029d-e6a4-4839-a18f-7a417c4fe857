<template>
  <div class="watchtower-list">
    <PopupSlot
      v-if="isPopShow"
      ref="popupRef"
      id="popup-info-tower"
      @closePopup="closePopup"
      popupTitle="值班详情"
    >
      <template #content>
        <div class="inner-content">
          <div>瞭望塔：{{ popupItem.lookout }}</div>
          <ul>
            <li>经度：{{ popupItem._X._value.toFixed(6) }}</li>
            <li>纬度：{{ popupItem._Y._value.toFixed(6) }}</li>
            <li>高程：{{ popupItem._Z._value.toFixed(6) }}</li>
          </ul>

          <ul>
            <li v-for="p in popupItem.persons">
              护林员：{{ p.name }}
              <br />
              电&nbsp;&nbsp;&nbsp;话： {{ p.phone }}
            </li>
          </ul>
        </div>
      </template>
    </PopupSlot>
  </div>
</template>
<script setup>
import PoiUtils from "@/views/xtsatellite/base/basejs/PoiUtilsWithRemove.js";
// import PoiUtils from "@/views/xtsatellite/base/basejs/PoiUtils";
import PopupSlot from "@/views/xtsatellite/base/basejs/PopupSlot";
import { el } from "element-plus/es/locales.mjs";
const isPopShow = ref(false);
let postRenderingFn = reactive(null);

const towerHandler = ref(null);
let popupItem = ref({});
// 初始化树形数据
onMounted(() => {
  // initGeojson();
});
onUnmounted(() => {
  // console.log(456);
  remove();
});
onActivated(() => {
  // 更新内容
  initGeojson();
  towerHandler.value = PoiUtils.addListenerNew(
    window.viewer,
    eventCallbackTower
  );
});
function eventCallbackTower(viewer, pick) {
  /* if (currentItem.value[0].id === pick.id.id) {
      popupItem.value = currentItem.value[0];
    } */
  // console.log(pick, "pick0000000000000---");

  if (
    !pick.id._properties ||
    !pick.id._properties._lookout ||
    !pick.id._properties._lookout._value
  ) {
    return;
  }
  popupItem.value = pick.id._properties;
  console.log(popupItem.value);
  //数组格式重组
  let personTemp = [];
  if (popupItem.value._name._value) {
    personTemp = popupItem.value._name._value.split("&");
  }
  let phoneTemp = [];
  if (popupItem.value._phone._value) {
    phoneTemp = popupItem.value._phone._value.split("&");
  }
  let t = {};
  popupItem.value.persons = [];
  for (let i = 0; i < personTemp.length; i++) {
    t = {};
    t.name = personTemp[i];
    if (phoneTemp.length) {
      t.phone = phoneTemp[i];
    } else {
      t.phone = "";
    }
    console.log(t, "ttt");

    popupItem.value.persons.push(t);
  }

  /* if (popupItem.value.people.includes("&")) {
  } */

  isPopShow.value = true;

  /* nextTick(() => {
    const popDom = window.document.querySelector("#popup-info-tower");
    popDom.style.position = "fixed";
    popDom.style.left = "-368px";
    popDom.style.top = "200px";
  }); */

  nextTick(() => {
    const popDom = window.document.querySelector("#popup-info-tower");
    const height = popDom.offsetHeight;
    postRenderingFn = viewer.scene.postRender.addEventListener(() => {
      const screenC = viewer.scene.cartesianToCanvasCoordinates(
        pick.primitive._position
      );
      if (screenC) {
        popDom.style.left = screenC.x - 1 + "px";
        popDom.style.top = screenC.y - height - 25 + "px";
      }
    });
  });
}
function closePopup() {
  // postRenderingFn();
  isPopShow.value = false;
  // uavDataSource.entities.removeAll();
}

onDeactivated(() => {
  // 更新内容
  // console.log(789);
  remove();
  towerHandler.value?.destroy();
});
const dataSource = ref(null);
const viewer = window.viewer;
const initGeojson = () => {
  const geoJsonUrl = `${window.xtmapConfig.publicPath}/geojson/watchtowers.geojson`; // 指向 public/data 下的文件路径
  dataSource.value = new Cesium.GeoJsonDataSource();
  dataSource.value
    .load(geoJsonUrl, {
      clampToGround: true,
    })
    .then((res) => {
      viewer.dataSources.add(dataSource.value);
      // 遍历所有的实体，为每个点设置属性描述信息
      dataSource.value.entities.values.forEach((entity) => {
        if (entity.properties) {
          // console.log(entity.properties);

          entity.billboard = new Cesium.BillboardGraphics({
            image: `${window.xtmapConfig.billboard.path}lwt.png`, // 使用项目中的图标或标记图片
            width: 32,
            height: 32,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
              0,
              150000
            ),
          });
          entity.label = new Cesium.LabelGraphics({
            // text: '',
            text: entity.properties._lookout._value,
            font: "bold 14px MicroSoft YaHei",
            outlineWidth: 2,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            // horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            pixelOffset: new Cesium.Cartesian2(0, -45), //偏移量
            showBackground: true,
            backgroundColor: new Cesium.Color(0, 0, 0, 0.5),
            backgroundPadding: new Cesium.Cartesian2(6, 3),
            // scaleByDistance: new Cesium.NearFarScalar(3000, 1, 12000, 0),
          });
        }
      });

      // 调整视角到数据范围
      viewer.flyTo(dataSource.value);
    });
};
const remove = () => {
  if (dataSource.value) {
    viewer.dataSources.remove(dataSource.value);
    dataSource.value = null; // 重置引用
  } else {
    console.warn("No GeoJSON to remove");
  }
};
</script>
<style scoped lang="scss">
.watchtower-list {
  height: 1px;
  width: 200px;
}
.inner-content {
  padding: 10px;
  width: 250px;
  // height: 160px;
  // display: flex;
  // justify-content: space-evenly;
  li {
    margin-bottom: 10px;
  }
}
</style>

