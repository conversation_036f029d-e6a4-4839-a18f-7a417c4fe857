<template>
  <div class="right-base">
    <cards title="今日值班" @iconClick="open"> sssssssss </cards>
    <cards title="弹窗" @iconClick="open">
      <el-button @click="open()">click </el-button>
    </cards>
  </div>
  <el-dialog
    title="应急通讯录"
    v-model="phoneVisible"
    width="810"
    height="500"
    :top="'9vh'"
  >
    <div>2222</div>
    <Phone :phoneList="phoneList"></Phone>
  </el-dialog>
</template>

<script setup>
import { parseTime } from "@/utils/ruoyi";
import {
  getDutyDetails,
  getWarningList,
  getDutyAddress,
} from "@/api/command/index";
import cards from "@/views/xtsafety/common/cards/index";
import Phone from "./panel/PhoneList.vue";

const dialogTableVisible = ref(false);
const phoneVisible = ref(false);

const phoneList = reactive([
  {
    name: "qwwq",
    sex: 1,
    post: "qwwq",
    company: "qwwq",
    telephone: "qqww",
  },
]);
const open = () => {
  phoneVisible.value = !phoneVisible.value;
};

// 初始化应急通讯录
const initdutyAddress = () => {
  /* getDutyAddress(formInline).then((res) => {
    if (res.code === 200) {
      dutyAddressList.value = res.rows;
      total.value = res.total;
    }
  }); */
};
onMounted(() => {
  // initDutyInfo();
  // initWarningInfo();
  // initdutyAddress();
});
</script>

<style scoped lang="scss">
.right-base {
  // height: 90vh;
  width: 420px;

  .searchButton {
    color: white !important;
    width: 90px;
    background: rgba(77, 225, 255, 0.14);
    box-sizing: border-box;
    border: 1px solid;
    border-image: linear-gradient(
        180deg,
        rgba(77, 225, 255, 0.9412) 0%,
        rgba(77, 225, 255, 0) 97%
      )
      1;

    :deep(.el-button:active) {
      background-color: rgba(77, 225, 255, 0.541);
      font-weight: bold;
    }
    /*按钮悬浮*/
    :deep(.el-button:hover) {
      background-color: rgba(77, 225, 255, 0.315);
      color: white !important;
      font-weight: bold;
      border-color: #01a8f9 !important;
    }
    /*按钮点击*/
    :deep(.el-button:focus) {
      background: rgba(77, 225, 255, 0.418);
      color: white !important;
      font-weight: bold;
      border-color: #01a8f9 !important;
    }
  }

  .searchButton-reset {
    color: white !important;
    width: 90px;
    background: rgba(65, 129, 206, 0.47);

    box-sizing: border-box;
    border: 1px solid;
    border-image: linear-gradient(
        180deg,
        #73bbff 0%,
        rgba(115, 187, 255, 0) 99%
      )
      1;

    :deep(.el-button:active) {
      background-color: rgba(77, 225, 255, 0.541);
      font-weight: bold;
    }
    /*按钮悬浮*/
    :deep(.el-button:hover) {
      background-color: rgba(77, 225, 255, 0.315);
      color: white !important;
      font-weight: bold;
      border-color: #01a8f9 !important;
    }
    /*按钮点击*/
    :deep(.el-button:focus) {
      background: rgba(77, 225, 255, 0.418);
      color: white !important;
      font-weight: bold;
      border-color: #01a8f9 !important;
    }
  }
}
</style>
