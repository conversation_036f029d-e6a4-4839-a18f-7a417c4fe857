import Cookies from 'js-cookie';

const TokenKey = 'Admin-Token' //长子
// const TokenKey = 'wsetoken'; //温宿

const ExpiresInKey = 'Admin-Expires-In';

const TenantKey = 'X-Tenant'

export function getToken() {
	return Cookies.get(TokenKey);
}

export function setToken(token) {
	return Cookies.set(TokenKey, token);
}

export function removeToken() {
	return Cookies.remove(TokenKey);
}

export function getExpiresIn() {
	return Cookies.get(ExpiresInKey) || -1;
}

export function setExpiresIn(time) {
	return Cookies.set(ExpiresInKey, time);
}

export function removeExpiresIn() {
	return Cookies.remove(ExpiresInKey);
}

export function getTenantId() {
	return Cookies.get(TenantKey);
}

export function setTenantId(tenantId) {
	return Cookies.set(TenantKey, tenantId);
}

export function removeTenantId() {
	return Cookies.remove(Tenant<PERSON><PERSON>);
}
