

<script setup>
import Header from "@/views/xtdrill/common/header/index";
import Tools from "@/views/xtdrill/common/tools/index";

import LeftBase from "@/views/xtdrill/review/leftpanel/LeftBase";
import RightBase from "@/views/xtdrill/review/rightpanel/RightBase";
import RightInstruct from "@/views/xtdrill/review/rightpanel/RightInstruct";
import RabbitmqClient from "@/utils/rmq.js";
import { nextTick, ref, reactive, computed, onMounted } from "vue";

const currentTab = ref("RightBase");
const tabs = {
  RightBase,
  RightInstruct,
};
function czFn() {
  window.viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(
      105.01145621478398,
      18.58828659369774,
      195000.0
    ),
  });
}

// -------------------------连接mq----------------------------
const options = {
  rmqUrl: window.xtmapConfig.rmq.urlA,
  // subUrl: window.xtmapConfig.rmq.subA,
};
const rmqObj = new RabbitmqClient(options);
// callback
function getData(d) {
  console.log(d + "-------ffff");
}

onMounted(() => {
  emitter.on("viewerLoad", (data) => {
    czFn();
  });

  rmqObj.initMQ().then(() => {
    rmqObj.subMsg(window.xtmapConfig.rmq.subA, getData);
    // rmqObj.subMsg(window.xtmapConfig.rmq.subB, getData); //无法连多个交换机
    rmqObj.reconnectMQ(3);
  });
});
</script>

<template>
  <div class="review">
    <div class="review-header">
      <Header name="复盘页面">
        <template #userInfo>
          <div class="userinfo">
            <!-- <el-button>指挥席位</el-button> -->
          </div>
        </template>
      </Header>
    </div>
    <div class="review-tools">
      <Tools></Tools>
    </div>
    <div class="left-panel">
      <LeftBase></LeftBase>
    </div>
    <div class="right-panel">
      <!-- <component :is="componentName"></component> -->
      <keep-alive>
        <component :is="tabs[currentTab]"></component>
      </keep-alive>
    </div>
  </div>
</template>

<style scoped lang="scss">
.review {
  // background-color: rgba(113, 197, 10, 0.917);
  // background: url("@/assets/xtui/layout/leftbg.png");
  // background-size: 100% 100%;
  width: 100%;
  height: 100vh;

  position: relative;
  top: 0;
  left: 0;
  .review-header {
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 100;
    pointer-events: auto;
  }
  .review-tools {
    position: absolute;
    right: 500px;
    top: 100px;
    z-index: 99;
    pointer-events: auto;
  }
  .left-panel {
    position: absolute;
    left: 40px;
    top: 0;
    z-index: 99;
    pointer-events: auto;
  }
  .right-panel {
    position: absolute;
    right: 30px;
    top: 0;
    z-index: 99;
    pointer-events: auto;
  }
}
</style>

