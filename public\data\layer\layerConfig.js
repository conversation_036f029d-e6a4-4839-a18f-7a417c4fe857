/*
 * @Descripttion: santana
 * @LastEditTime: 2022-01-12 18:32:55
 */

window.czLayerConfig = {
	czBaseMap: {
		// 图层组名称
		name: '地图影像',

		DB_LABEL_XYZ: {
			serviceType: 'XYZ',
			treeName: '星图云影像',
			layerName: 'xyz_img',
			url: 'https://tiles1.geovisearth.com/base/v1/img/{z}/{x}/{y}?format=webp&tmsIds=w&token=d8f0b7024dbc0c43c7e7f080a8edc43043bb97df0d3b890480e400c2b69f69f7',
			tilingScheme: new Cesium.WebMercatorTilingScheme(),
		},
		// databox
		DB_IMG_XYZ: {
			serviceType: 'XYZ',
			treeName: '星图云影像注记',
			layerName: 'xyz_label',
			url: 'https://tiles1.geovisearth.com/base/v1/cia/{z}/{x}/{y}?format=png&tmsIds=w&token=d8f0b7024dbc0c43c7e7f080a8edc43043bb97df0d3b890480e400c2b69f69f7',
			tilingScheme: new Cesium.WebMercatorTilingScheme(),
		},
		DB_VEC_XYZ: {
			serviceType: 'XYZ',
			treeName: '星图云矢量',
			layerName: 'xyz_vec',
			url: 'https://tiles1.geovisearth.com/base/v1/vec/{z}/{x}/{y}?format=png&tmsIds=w&token=d8f0b7024dbc0c43c7e7f080a8edc43043bb97df0d3b890480e400c2b69f69f7',
			tilingScheme: new Cesium.WebMercatorTilingScheme(),
		},
		/* DB_DEM: {
			serviceType: 'CZDEM',
			treeName: '盒子terrain地形',
			layerName: 'cz_dem',
			url: 'http://223.84.66.67:8092/terrain/DEM',
		}, */
		TDT_IMAGE: {
			// 天地图 示例
			serviceType: 'WMTS',
			treeName: '天地图影像',
			layerName: 'wtms_image',

			url: `http://{s}.tianditu.gov.cn/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=0cc22d450bf67b7abb13e0e90ea233ae`,
			layer: 'img_w',
			style: 'default',
			format: 'tiles',
			subdomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
			tileMatrixSetID: 'w',
			// tilingScheme: new Cesium.WebMercatorTilingScheme(),
			minimumLevel: 0, // 最小层级
			maximumLevel: 18, // 最大

			// tileMatrixSetID: 'GoogleMapsCompatible',
		},
		TDT_CIA: {
			serviceType: 'WMTS',
			treeName: '天地图影像注记',
			layerName: 'wtms_tdtcia',

			url: `http://{s}.tianditu.gov.cn/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default.jpg&format=tiles&tk=0cc22d450bf67b7abb13e0e90ea233ae`,
			layer: 'cia_w',
			style: 'default',
			format: 'image/jpeg',
			subdomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
			tileMatrixSetID: 'w',
			// tilingScheme: new Cesium.WebMercatorTilingScheme(),
			minimumLevel: 0, // 最小层级
			maximumLevel: 18, // 最大

			// tileMatrixSetID: 'GoogleMapsCompatible',
		},
		TDT_VEC: {
			// 天地图 示例
			serviceType: 'WMTS',
			treeName: '天地图矢量',
			layerName: 'wtms_tdtvec',

			url: `http://{s}.tianditu.gov.cn/vec_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=0cc22d450bf67b7abb13e0e90ea233ae`,
			layer: 'vec_w',
			style: 'default',
			format: 'tiles',
			subdomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
			tileMatrixSetID: 'w',
			// tilingScheme: new Cesium.WebMercatorTilingScheme(),
			minimumLevel: 0, // 最小层级
			maximumLevel: 18, // 最大

			// tileMatrixSetID: 'GoogleMapsCompatible',
		},
		TDT_CVA: {
			// 天地图 示例
			serviceType: 'WMTS',
			treeName: '天地图矢量注记',
			layerName: 'wtms_tdtcva',

			url: `http://{s}.tianditu.gov.cn/cva_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cva&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default.jpg&format=tiles&tk=068486abb23e217da3bc0e653ade3994`,
			layer: 'cva_w',
			style: 'default',
			format: 'image/jpeg',
			subdomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
			tileMatrixSetID: 'w',
			// tileMatrixSetID: 'GoogleMapsCompatible',
			// tilingScheme: new Cesium.WebMercatorTilingScheme(),
			minimumLevel: 0, // 最小层级
			maximumLevel: 18, // 最大

			// tileMatrixSetID: 'GoogleMapsCompatible',
		},
	},
	/* czThematicMap: {
		name: '专题地图',
		DB_LABzEL_XYZ: {
			serviceType: 'WMTS',
			treeName: '天地图矢量图层',
			layerName: 'tdt_wtms_vector',
			url: 'http://t0.tianditu.com/vec_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=068486abb23e217da3bc0e653ade3994',

			layer: 'tdtBasicLayer',
			style: 'defatlt',
			format: 'image/png',
			maximumLevel: 15,
			tilingScheme: new Cesium.WebMercatorTilingScheme(),
			tileMatrixSetID: 'EPSG:3857',
		},
		DB_IMAGE_sea: {
			serviceType: 'WMTS',
			treeName: 'seaicenter',
			layerName: 'wtms_image_sea',
			url: 'http://icenter.geovis.online/tilecache/service/wmts?layer=Global_SeaMap-PNG-3857',
			layer: 'Global_SeaMap-PNG-3857',
			style: 'defatlt',
			format: 'image/png',
			maximumLevel: 15,
			tilingScheme: new Cesium.WebMercatorTilingScheme(),
			// tilingScheme: new Cesium.GeographicTilingScheme(),
			tileMatrixSetID: 'EPSG:3857',
		},
		DB_LABzEL_XYZz: {
			serviceType: 'XYZ',
			treeName: '盒子注记xyz版',
			layerName: 'xyz_label',
			url: 'http://223.84.66.67:8092/xyz/ImageLabel/{z}/{x}/{y}.jpg',
		},
		DB_LABzEL_XYyZ: {
			serviceType: 'XYZ',
			treeName: '盒子注记xyz版',
			layerName: 'xyz_label',
			url: 'http://223.84.66.67:8092/xyz/VectorMap/{z}/{x}/{y}.png',
		},
	}, */
};

/* trdata: [
	
	{
		id: 2,
		label: '一级 2',
		children: [
			{
				id: 5,
				label: '二级 2-1',
			},
			{
				id: 6,
				label: '二级 2-2',
			},
		],
	} 
], */
