
import request from '@/utils/request'

// 查询监测大类
export function getSpaceBased(query) {
  return request({
    url: '/mine/aerialBase',
    method: 'get',
    params: query
  })
}



export function addSpaceBased(data) {
  return request({
    url: '/mine/aerialBase',
    method: 'post',
    data: data
  })
}


export function updateSpaceBased(data) {
  return request({
    url: '/mine/aerialBase',
    method: 'post',
    data: data
  })
}


export function delSpaceBased(ids) {
  return request({
    url: '/mine/aerialBase',
    method: 'delete',
    data: ids
  })
}
