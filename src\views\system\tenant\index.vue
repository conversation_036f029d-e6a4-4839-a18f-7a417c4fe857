<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      v-show="showSearch"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="租户名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入租户名"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="租户状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="租户状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in [
              {
                label: '启用',
                value: '1',
              },
              {
                label: '禁用',
                value: '0',
              },
            ]"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:tenant:edit']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:tenant:remove']"
          >删除</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 表格数据 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="用户编号" prop="contactUserId" /> -->
      <el-table-column label="租户名" prop="name" />
      <el-table-column label="联系人" prop="contactName" />
      <el-table-column label="联系手机" prop="contactMobile" />
      <el-table-column label="租户状态">
        <template #default="scope">
          {{ scope.row.status === "1" ? "启用" : "禁用" }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="显示顺序" prop="roleSort" width="100" /> -->
      <!-- <el-table-column label="创建时间" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-tooltip content="修改" placement="top">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:tenant:edit']"
            >修改</el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button
              link
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:tenant:remove']"
            >删除</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改租户配置对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="tenantRef" :model="form" :rules="rules" label-width="auto">
        <!-- <el-form-item label="用户编号" prop="contactUserId">
          <el-input v-model="form.contactUserId" placeholder="请输入用户编号" />
        </el-form-item> -->
        <el-form-item label="租户名" prop="name">
          <el-input v-model="form.name" placeholder="请输入租户名" />
        </el-form-item>
        <el-form-item label="联系人" prop="contactName">
          <el-input v-model="form.contactName" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系手机" prop="contactMobile">
          <el-input v-model="form.contactMobile" placeholder="请输入联系手机" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="form.sort"
            controls-position="right"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="租户状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in [
                {
                  label: '启用',
                  value: '1',
                },
                {
                  label: '禁用',
                  value: '0',
                },
              ]"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="租户关联用户" prop="tenantUserList">
          <el-button type="primary" sizi="mini" @click="handleSelectUser"
            >点此关联</el-button
          >
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入描述"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <select-user
      ref="selectRef"
      :selectUserIds="selectUserIds"
      @ok="dealUserData"
    />
  </div>
</template>

<script setup name="Tenant">
import {
  listTenant,
  getTenant,
  addTenant,
  updateTenant,
  delTenant,
} from "@/api/system/tenant";
import selectUser from "./selectUser";
import { formatDate } from "@/utils";
import useUserStore from "@/store/modules/user";

const userStore = useUserStore();

const { proxy } = getCurrentInstance();

const tableData = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const selectUserIds = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    // contactUserId: undefined,
    name: undefined,
    status: undefined,
    pageNum: 1,
    pageSize: 10,
  },
  rules: {
    // contactUserId: [
    //   { required: true, message: "用户编号不能为空", trigger: "blur" },
    // ],
    name: [{ required: true, message: "租户名不能为空", trigger: "blur" }],
    // contactName: [
    //   { required: true, message: "联系人不能为空", trigger: "blur" },
    // ],
    // contactMobile: [
    //   { required: true, message: "联系手机不能为空", trigger: "blur" },
    // ],
    sort: [{ required: true, message: "排序不能为空", trigger: "blur" }],
    status: [{ required: true, message: "租户状态不能为空", trigger: "blur" }],
    tenantUserList: [
      { required: true, message: "租户关联用户不能为空", trigger: "change" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.roleId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 重置新增的表单以及其他数据  */
function reset() {
  form.value = {
    id: undefined,
    contactUserId: undefined,
    name: undefined,
    contactName: undefined,
    contactMobile: undefined,
    sort: 0,
    status: "1",
    tenantUserList: [],
    description: undefined,
  };
  proxy.resetForm("tenantRef");
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

// 查询租户列表
const getList = () => {
  listTenant(queryParams.value).then((res) => {
    tableData.value = res.rows;
    total.value = res.total;
    loading.value = false;
  });
};

// 新增租户
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加租户";
}

// 编辑租户
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  selectUserIds.value = row.tenantUserList.map((item) => {
    return {
      userId: item.userId,
    };
  });
  getTenant(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改租户";
  });
}

// 删除租户
function handleDelete(row) {
  const _ids = row.id ? [row.id] : ids.value;
  proxy.$modal
    .confirm('是否确认删除租户编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delTenant(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

// 打开用户列表
const handleSelectUser = () => {
  proxy.$refs["selectRef"].show();
};

// 处理关联用户列表数据
const dealUserData = (tenantUserList) => {
  form.value.tenantUserList = [];
  if (form.value.id != undefined) {
    form.value.tenantUserList = tenantUserList.map((item) => {
      return {
        tenantId: form.value.id,
        userId: item,
      };
    });
  } else {
    form.value.tenantUserList = tenantUserList.map((item) => {
      return {
        userId: item,
      };
    });
  }
};

// 提交
function submitForm() {
  proxy.$refs["tenantRef"].validate((valid) => {
    if (valid) {
      selectUserIds.value = [];
      if (form.value.id != undefined) {
        const data = {
          ...form.value,
          updateBy: userStore.name,
          updateTime: formatDate(new Date()),
        };
        updateTenant(data).then((res) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        const data = {
          ...form.value,
          createBy: userStore.name,
          createTime: formatDate(new Date()),
          deleted: "0",
        };
        addTenant(data).then((res) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

getList();
</script>
