<template>
  <div class="tools">
    <div class="tools-list">
      <ul>
        <li
          v-for="(item, index) in toolData"
          :class="{ isLiActive: item.value === currentTab }"
          @click="changeCurrentTool(item.value, index)"
        >
          {{ item.name }}
        </li>
      </ul>
    </div>
    <div class="tools-panel">
      <keep-alive>
        <component :is="tabs[currentTab]" v-show="isPanelShow"></component>
      </keep-alive>
    </div>
  </div>
</template>


<script setup>
import { nextTick, ref, reactive, computed, onMounted } from "vue";
import Measure from "@/earthCompStyle/measure/index";
import Plot from "@/earthCompStyle/plot/index";
import Resource from "@/earthCompStyle/resource/index";
import Print from "@/earthCompStyle/print/index";
import Thematic from "@/earthCompStyle/thematic/index";
import Buffer from "@/earthCompStyle/buffer/index";
import Layer from "@/earthCompStyle/layer/index";
import Camera from "@/earthCompStyle/camera/index";
// import DrawIcon from "@/earthCompStyle/DrawIcon/index";

const currentTab = ref("");
const tabs = {
  Measure,
  Plot,
  Resource,
  Print,
  // Thematic,
  Buffer,
  Layer,
  Camera,
  // DrawIcon,
};
const isPanelShow = ref(false);
const toolData = reactive([
  {
    name: "测量",
    value: "Measure",
  },
  {
    name: "绘制",
    value: "Plot",
  },
  {
    name: "截图",
    value: "Print",
  },
  {
    name: "资源",
    value: "Resource",
  },
  /* {
    name: "专题",
    value: "Thematic",
  }, */
  {
    name: "缓冲区",
    value: "Buffer",
  },
  {
    name: "图层",
    value: "Layer",
  },
  {
    name: "相机",
    value: "Camera",
  },
  {
    name: "标绘Ns",
    value: "Plot",
  },
]);

function changeCurrentTool(val, index) {
  if (currentTab.value === val) {
    isPanelShow.value = false;
    currentTab.value = null;
  } else {
    isPanelShow.value = true;
    currentTab.value = val;
  }
}

onMounted(() => {
  // const toolbar = window.document.getElementById("#tools-list");
  const toolbar = window.document.querySelector(".tools-list");
  const toolbarWidth = toolbar.offsetWidth;

  const toolPanel = window.document.querySelector(".tools-panel");
  toolPanel.style.position = "absolute";
  toolPanel.style.right = toolbarWidth + 20 + "px";
  toolPanel.style.top = 0 + "px";
});
</script>

<style scoped lang="scss">
.tools {
  background-color: rgba(205, 163, 127, 0.315);
  position: relative;
  padding: 10px;
  .tools-list {
    ul {
      margin: 0;
      padding: 0;
      display: grid;
      grid-template-columns: repeat(1fr);

      li {
        list-style-type: none;
        width: 64px;
        height: 64px;
        background-color: rgba(43, 141, 239, 0.239);
        display: flex;
        align-items: center;
        justify-content: center;
        &.isLiActive {
          background-color: rgba(17, 100, 182, 0.639);
        }
      }
    }
  }
  .tools-panel {
    // position: absolute;
    // top: 0;
    // right: 90px;
  }
}
</style>

