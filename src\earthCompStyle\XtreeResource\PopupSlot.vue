<!--
 * @Descripttion: santana
 * @LastEditTime: 2022-04-10 20:40:45
-->

<template>
  <div class="popup">
    <!-- <div class="popup" v-if="isPopShow"> -->
    <div class="popup-header">
      <div class="popup-header-title">{{ popupTitle }}</div>
      <CircleClose
        @click="closePopup"
        style="width: 1.5em; height: 1.5em; margin-right: 8px"
      />
    </div>
    <div class="popup-content">
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script setup>
import { reactive } from "vue";

const props = defineProps({
  popupTitle: {
    type: String,
    default: "",
  },
  item: {
    type: Object,
  },
});

// console.log(props.item, "item---");
// const imgSrc = new URL("@/assets/icons/billboard/bb.png", import.meta.url).href;
// console.log(imgSrc, "imgSrc");
const emits = defineEmits(["closePopup"]); //addEvent是父元素里面的一个函数方法，通过这个方法传参
const closePopup = () => {
  emits("closePopup");
};
</script>

<style scoped lang="scss">
.popup {
  // width: 290px;
  // height: 285px;
  // background-color: rgba(33, 105, 213, 0.435);
  // background-image: url("@/assets/xtui/panel/弹框通用背景@1x.png");
  // background-repeat: no-repeat;
  // background-size: 100% 100%;
  background: rgba(8, 76, 124, 0.5);
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%) 2;
  backdrop-filter: blur(10px);
  position: fixed;
  top: 100px;
  left: 850px;
  padding: 15px 15px 15px 15px;
  // border-radius: 5px;
  color: white;
  /* display: none; */
  /* .popup-header {
    display: flex;
    justify-content: space-between;
    border: 1px solid rgba(150, 178, 223, 0.619);
    align-items: center;
    padding: 5px;
  } */
  .popup-header {
    cursor: pointer;
    background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center center;
    background-size: 100% 100%;
    height: 32px;
    width: 100%;
    margin: 5px 0px 15px 0px;
    padding: 5px 10px 5px 30px;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0px;
    color: aliceblue;
    display: flex;
    justify-content: space-between;
    .popup-header-title {
      margin-left: 10px;
    }
  }
  .popup-content {
    li {
      height: 30px;
      margin-bottom: 5px;
    }
  }
}
</style>
