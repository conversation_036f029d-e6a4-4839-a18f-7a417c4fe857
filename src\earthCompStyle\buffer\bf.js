import * as turf from '@turf/turf';

function _arrayLikeToArray(r, a) {
  (null == a || a > r.length) && (a = r.length);
  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];
  return n;
}
function _classCallCheck(a, n) {
  if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function");
}
function _defineProperties(e, r) {
  for (var t = 0; t < r.length; t++) {
    var o = r[t];
    o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o);
  }
}
function _createClass(e, r, t) {
  return r && _defineProperties(e.prototype, r), Object.defineProperty(e, "prototype", {
    writable: !1
  }), e;
}
function _createForOfIteratorHelper(r, e) {
  var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"];
  if (!t) {
    if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e  ) {
      t && (r = t);
      var n = 0,
        F = function () {};
      return {
        s: F,
        n: function () {
          return n >= r.length ? {
            done: !0
          } : {
            done: !1,
            value: r[n++]
          };
        },
        e: function (r) {
          throw r;
        },
        f: F
      };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var o,
    a = !0,
    u = !1;
  return {
    s: function () {
      t = t.call(r);
    },
    n: function () {
      var r = t.next();
      return a = r.done, r;
    },
    e: function (r) {
      u = !0, o = r;
    },
    f: function () {
      try {
        a || null == t.return || t.return();
      } finally {
        if (u) throw o;
      }
    }
  };
}
function _defineProperty(e, r, t) {
  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
    value: t,
    enumerable: !0,
    configurable: !0,
    writable: !0
  }) : e[r] = t, e;
}
function _toPrimitive(t, r) {
  if ("object" != typeof t || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r );
    if ("object" != typeof i) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (String )(t);
}
function _toPropertyKey(t) {
  var i = _toPrimitive(t, "string");
  return "symbol" == typeof i ? i : i + "";
}
function _unsupportedIterableToArray(r, a) {
  if (r) {
    if ("string" == typeof r) return _arrayLikeToArray(r, a);
    var t = {}.toString.call(r).slice(8, -1);
    return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;
  }
}

/**
 * @description: 获取地球坐标
 * @param {obj} viewer viewer对象
 * @param {e} pxCoor e.position
 * @param {string} resultType 设置返回值的显示格式是经纬度还是xyz(cartesian)
 * @return { obj } 返回的默认格式：{ lon: lon, lat: lat, height: height }
 */

// 外层必须传 viewer和e ,所以没有设置fn默认值
function getCoorFromPx(_ref) {
  var _ref$resultType = _ref.resultType,
    resultType = _ref$resultType === void 0 ? 'jwd' : _ref$resultType,
    viewer = _ref.viewer,
    pxCoor = _ref.pxCoor;
  //判定标识
  var isOnOsgb;
  var isTerrainOpen;
  var cartesian = null;
  var jwdCoor = null;
  var xyzCoor = null;
  var pick = viewer.scene.pick(pxCoor);

  // 应该用drill pick,获取所有通过点击捕获到的实体,如果有模型,就获取模型，
  // 否则，当模型如果处在地面以下一部分时，pick是会直接判定为点击到地面

  // 有无模型
  if (pick && pick.primitive instanceof Cesium.Cesium3DTileFeature || pick && pick.primitive instanceof Cesium.Cesium3DTileset || pick && pick.primitive instanceof Cesium.Model) {
    isOnOsgb = true;
  }
  // 有无地形,Cesium.EllipsoidTerrainProvider是用户不加载地形时，cz默认的空地形,所以t时无 , f时有
  if (viewer.terrainProvider instanceof Cesium.EllipsoidTerrainProvider) {
    isTerrainOpen = false;
  } else {
    isTerrainOpen = true;
  }
  if (isOnOsgb) {
    cartesian = viewer.scene.pickPosition(pxCoor);
  } else {
    if (isTerrainOpen) {
      var ray = viewer.scene.camera.getPickRay(pxCoor);
      if (!ray) return;
      cartesian = viewer.scene.globe.pick(ray, viewer.scene);
    } else {
      cartesian = viewer.scene.camera.pickEllipsoid(pxCoor, viewer.scene.globe.ellipsoid);
    }
  }
  if (cartesian) {
    var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    var lon = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
    var lat = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);
    var height = cartographic.height > 0 ? cartographic.height : 0.1; // 模型高度

    jwdCoor = {
      lon: lon,
      lat: lat,
      height: height
    };
    xyzCoor = cartesian;
    return resultType === 'xyz' ? xyzCoor : jwdCoor;

    /* let position = transformCartesianToWGS84(viewer, cartesian);
    if (position.alt < 0) {
    	coor = transformWGS84ToCartesian(viewer, position, 0.1);
    } */
  }
  return null;
}
function cart3ToDegree(dataArr) {
  var data = [];
  var jwdCoor = [];
  var _iterator = _createForOfIteratorHelper(dataArr),
    _step;
  try {
    for (_iterator.s(); !(_step = _iterator.n()).done;) {
      var i = _step.value;
      var cartographic = Cesium.Cartographic.fromCartesian(i);
      var lon = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
      var lat = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);
      jwdCoor = [parseFloat(lon), parseFloat(lat)];
      data.push(jwdCoor);
    }
  } catch (err) {
    _iterator.e(err);
  } finally {
    _iterator.f();
  }
  return data;
}
function pointsToDegreesArray(points) {
  var degreesArray = [];
  points.map(function (item) {
    degreesArray.push(item[0]);
    degreesArray.push(item[1]);
  });
  return degreesArray;
}

/* 
const handler = new Cesium.ScreenSpaceEventHandler(viewer.canvas);
return new Promise((resolve, reject) => {
	handler.setInputAction((e) => {
		let posi = e.position;
		let pickedObj = scene.pick(e.position);

		let coor = getCatesian3FromPX(viewer, endPos);

		resolve(coor);
		reject('--err--');
	}, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}); 
 
handler.setInputAction(function (movement) {
    let endPos = movement.endPosition;
    CreateRemindertip(toolTip, endPos, true);
    if (Cesium.defined(polyline)) {
    anchorpoints.pop();
    let cartesian = getCatesian3FromPX(viewer, endPos);
    anchorpoints.push(cartesian);
    }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE); 
*/

// 绘制 清除 两个按钮

/* const options = {
	color: Cesium.Color.RED.withAlpha(0.6),
	outlineColor: '',
	radius: 10,
}; */
var BufferPoint = /*#__PURE__*/function () {
  function BufferPoint(viewer) {
    _classCallCheck(this, BufferPoint);
    this.viewer = viewer;
    this.eventHandler = undefined;
    // this.poiArr = [];
    this.pointBufferDataSource = new Cesium.CustomDataSource('bufferpoidata');
    this.viewer.dataSources.add(this.pointBufferDataSource);
    this.pointEntity = undefined;
    this.bufferEntity = undefined;
    this.activeShape = undefined;
    this.options = null;
    this.coor = {
      lon: '',
      lat: ''
    };
  }
  return _createClass(BufferPoint, [{
    key: "createEvent",
    value: function createEvent(options) {
      var _this = this;
      return new Promise(function (resolve, reject) {
        _this.options = options;
        _this.eventHandler = new Cesium.ScreenSpaceEventHandler(_this.viewer.scene.canvas);
        _this.eventHandler.setInputAction(_this.leftEvent(resolve, reject), Cesium.ScreenSpaceEventType.LEFT_CLICK);
      });
    }
  }, {
    key: "leftEvent",
    value: function leftEvent(resolve, reject) {
      var _this2 = this;
      var event = function event(e) {
        var earthPosition = getCoorFromPx({
          viewer: _this2.viewer,
          pxCoor: e.position,
          resultType: 'xyz'
        });
        var earthPositionJWD = getCoorFromPx({
          viewer: _this2.viewer,
          pxCoor: e.position,
          resultType: 'jwd'
        });
        if (Cesium.defined(earthPosition)) {
          _this2.pointEntity = _this2.createPoint(earthPosition, '');
          _this2.bufferEntity = _this2.createBuffer(earthPosition, _this2.options);
          _this2.pointBufferDataSource.entities.add(_this2.pointEntity);
          _this2.pointBufferDataSource.entities.add(_this2.bufferEntity);
          // this.poiArr.push(earthPosition);
          if (_this2.eventHandler) {
            _this2.eventHandler.destroy();
          }
          _this2.eventHandler = undefined;
          resolve(earthPositionJWD);
          reject('err----');
        }
      };
      return event;
    }
  }, {
    key: "createPoint",
    value: function createPoint(poi, txt) {
      var point = new Cesium.Entity({
        position: poi,
        point: {
          color: Cesium.Color.YELLOW.withAlpha(0.6),
          pixelSize: 6
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        label: {
          text: txt || '',
          font: 'bold 12px MicroSoft YaHei',
          outlineWidth: 2,
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          verticalOrigin: Cesium.VerticalOrigin.TOP,
          pixelOffset: new Cesium.Cartesian2(15, 0),
          // pixelOffset: new Cesium.Cartesian2(-15, 16), //偏移量
          showBackground: true,
          backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
          backgroundPadding: new Cesium.Cartesian2(6, 3),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      });
      return point;
    }
  }, {
    key: "createBuffer",
    value: function createBuffer(poi, options) {
      var buffer = new Cesium.Entity({
        position: poi,
        ellipse: {
          semiMinorAxis: options.radius,
          semiMajorAxis: options.radius,
          fill: true,
          material: options.color
          // material: Cesium.Color.RED.withAlpha(0.6),
          // outline: true,
          // outlineColor: Cesium.Color.YELLOW,
          // outlineWidth: 1,
        }
      });
      return buffer;
    }
  }, {
    key: "reset",
    value: function reset() {
      this.pointBufferDataSource.entities.removeAll();
      this.pointEntity = undefined;
      this.bufferEntity = undefined;
      this.r = null;
      if (this.eventHandler) {
        this.eventHandler.destroy();
      }
      this.eventHandler = undefined;
    }
  }, {
    key: "resetHandler",
    value: function resetHandler() {
      if (this.eventHandler) {
        this.eventHandler.destroy();
      }
      this.eventHandler = undefined;
    }
  }, {
    key: "removePoi",
    value: function removePoi() {
      /* this.viewer.entities.remove(this.activeShape);
      this.tempPointArr.entities.removeAll(); */
      this.pointBufferDataSource.entities.removeAll();
    }
  }]);
}();

var BufferLine = /*#__PURE__*/function () {
  function BufferLine(viewer) {
    var _this = this;
    _classCallCheck(this, BufferLine);
    _defineProperty(this, "leftEvent", function (e) {
      var earthPosition = getCoorFromPx({
        viewer: _this.viewer,
        pxCoor: e.position,
        resultType: 'xyz'
      });
      if (Cesium.defined(earthPosition)) {
        var tempPoint;
        if (_this.poiArr.length === 0) {
          _this.floatingPoint = _this.createFloatPoint(earthPosition, '左键绘制，右键结束');
          _this.viewer.entities.add(_this.floatingPoint);
          tempPoint = _this.createPoint(earthPosition);
          _this.poiArr.push(earthPosition);
          var dynamicPositions = new Cesium.CallbackProperty(function () {
            return _this.poiArr;
          }, false);
          _this.activeShapeLine = _this.createPolyline(dynamicPositions);
        } else {
          tempPoint = _this.createPoint(earthPosition);
        }
        _this.poiArr.push(earthPosition);
        _this.lineBufferDataSource.entities.add(tempPoint);
        // this.tipInfo.innerHTML = ' 右键单击结束测量 ';
      }
    });
    _defineProperty(this, "moveEvent", function (e) {
      var newPosition = getCoorFromPx({
        viewer: _this.viewer,
        pxCoor: e.endPosition,
        resultType: 'xyz'
      });
      if (Cesium.defined(_this.floatingPoint) && Cesium.defined(newPosition)) {
        _this.floatingPoint.position.setValue(newPosition);
        _this.poiArr.pop();
        _this.poiArr.push(newPosition);
      }
    });
    this.viewer = viewer;
    this.eventHandler = undefined;
    this.poiArr = [];
    // 点集和线是分开绘制的，所以为左键点集合声明一个CustomDataSource
    this.lineBufferDataSource = new Cesium.CustomDataSource('bufferlinedata');
    this.viewer.dataSources.add(this.lineBufferDataSource);
    this.floatingPoint = undefined;
    this.activeShapeLine = undefined;
    this.activeShapePolygon = undefined;
    this.options = null;
  }
  return _createClass(BufferLine, [{
    key: "createEvent",
    value: function createEvent(options) {
      var _this2 = this;
      return new Promise(function (resolve, reject) {
        _this2.options = options;
        _this2.eventHandler = new Cesium.ScreenSpaceEventHandler(_this2.viewer.scene.canvas);
        _this2.eventHandler.setInputAction(_this2.leftEvent, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        _this2.eventHandler.setInputAction(_this2.moveEvent, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        _this2.eventHandler.setInputAction(_this2.rightEvent(resolve, reject), Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      });
    }
  }, {
    key: "rightEvent",
    value: function rightEvent(resolve, reject) {
      var _this3 = this;
      var r = function r(e) {
        if (_this3.poiArr.length == 0) {
          return;
        }
        var Position = getCoorFromPx({
          viewer: _this3.viewer,
          pxCoor: e.position,
          resultType: 'xyz'
        });
        if (Cesium.defined(Position)) {
          _this3.poiArr.pop();
          _this3.poiArr.push(Position);
          var endPoint = _this3.createPoint(Position, '');
          _this3.lineBufferDataSource.entities.add(endPoint);
          _this3.viewer.entities.remove(_this3.floatingPoint);
          _this3.eventHandler.destroy();
          _this3.eventHandler = undefined;
          var turfLineArr = cart3ToDegree(_this3.poiArr);
          var turfLineData = turf.lineString(turfLineArr);
          var bufferData = turf.buffer(turfLineData, _this3.options.radius, {
            units: 'meters'
          });
          var polygonArr = pointsToDegreesArray(bufferData.geometry.coordinates[0]);
          _this3.activeShapePolygon = _this3.createPolygon(polygonArr);
          resolve();
          reject('err----');
        }
      };
      return r;
    }
  }, {
    key: "createPoint",
    value: function createPoint(poi, txt) {
      var point = new Cesium.Entity({
        position: poi,
        point: {
          color: Cesium.Color.YELLOW.withAlpha(0.6),
          pixelSize: 6
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        label: {
          text: txt || '',
          font: '18px sans-serif',
          fillColor: Cesium.Color.GOLD,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlineWidth: 2,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -20)
        }
      });
      return point;
    }
  }, {
    key: "createFloatPoint",
    value: function createFloatPoint(poi, txt) {
      var point = new Cesium.Entity({
        position: poi,
        point: {
          color: Cesium.Color.YELLOW.withAlpha(0.6),
          pixelSize: 6
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        label: {
          text: txt || '',
          font: 'bold 12px MicroSoft YaHei',
          outlineWidth: 2,
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          verticalOrigin: Cesium.VerticalOrigin.TOP,
          pixelOffset: new Cesium.Cartesian2(15, 0),
          // pixelOffset: new Cesium.Cartesian2(-15, 16), //偏移量
          showBackground: true,
          backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
          backgroundPadding: new Cesium.Cartesian2(6, 3),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      });
      return point;
    }
  }, {
    key: "createPolyline",
    value: function createPolyline(poiArr, material) {
      var shape = this.viewer.entities.add({
        polyline: {
          positions: poiArr,
          clampToGround: true,
          width: 3,
          // material: Cesium.Color.RED,
          material: new Cesium.PolylineDashMaterialProperty({
            color: Cesium.Color.fromCssColorString('rgb(20, 120, 255)').withAlpha(0.83)
          })
        }
      });
      return shape;
    }
  }, {
    key: "createPolygon",
    value: function createPolygon(poiArr, material) {
      var shape = this.viewer.entities.add({
        polygon: {
          hierarchy: new Cesium.PolygonHierarchy(Cesium.Cartesian3.fromDegreesArray(poiArr)),
          material: Cesium.Color.RED.withAlpha(0.6),
          classificationType: Cesium.ClassificationType.BOTH
        }
      });
      return shape;
    }
  }, {
    key: "reset",
    value: function reset() {
      this.removeGeometry();
      this.poiArr = [];
      this.floatingPoint = undefined;
      this.activeShapeLine = undefined;
      this.activeShapePolygon = undefined;
      if (this.eventHandler) {
        this.eventHandler.destroy();
      }
      this.eventHandler = undefined;
    }
  }, {
    key: "resetHandler",
    value: function resetHandler() {
      if (this.eventHandler) {
        this.eventHandler.destroy();
      }
      this.eventHandler = undefined;
    }
  }, {
    key: "removeGeometry",
    value: function removeGeometry() {
      this.viewer.entities.remove(this.activeShapeLine);
      this.viewer.entities.remove(this.activeShapePolygon);
      this.lineBufferDataSource.entities.removeAll();
    }
  }]);
}();

var BufferPolygon = /*#__PURE__*/function () {
  function BufferPolygon(viewer) {
    var _this = this;
    _classCallCheck(this, BufferPolygon);
    _defineProperty(this, "leftEvent", function (e) {
      var earthPosition = getCoorFromPx({
        viewer: _this.viewer,
        pxCoor: e.position,
        resultType: 'xyz'
      });
      if (Cesium.defined(earthPosition)) {
        var tempPoint;
        if (_this.poiArr.length === 0) {
          _this.floatingPoint = _this.createPoint(earthPosition, '左键绘制，右键结束');
          _this.viewer.entities.add(_this.floatingPoint);
          _this.poiArr.push(earthPosition);
          var dynamicPositions = new Cesium.CallbackProperty(function () {
            return new Cesium.PolygonHierarchy(_this.poiArr);
          }, false);
          _this.activeShapeArea = _this.createArea(dynamicPositions);
        }
        _this.poiArr.push(earthPosition);
        tempPoint = _this.createPoint(earthPosition);
        _this.polygonBufferDataSource.entities.add(tempPoint);
      }
    });
    _defineProperty(this, "moveEvent", function (e) {
      if (Cesium.defined(_this.floatingPoint)) {
        var newPosition = getCoorFromPx({
          viewer: _this.viewer,
          pxCoor: e.endPosition,
          resultType: 'xyz'
        });
        if (Cesium.defined(newPosition)) {
          _this.floatingPoint.position.setValue(newPosition);
          // if (this.poiArr.length > 1) {
          _this.poiArr.pop();
          _this.poiArr.push(newPosition);
          // }
        }
      }
    });
    this.viewer = viewer;
    this.eventHandler = undefined;
    this.poiArr = [];
    this.polygonBufferDataSource = new Cesium.CustomDataSource('bufferAreadata');
    this.viewer.dataSources.add(this.polygonBufferDataSource);
    this.floatingPoint = undefined;
    this.activeShapeArea = undefined;
    this.activeShapeBuffer = undefined;
    this.area = undefined;
    this.labelTxt = undefined;
    this.options = '';
  }
  return _createClass(BufferPolygon, [{
    key: "createEvent",
    value: function createEvent(options) {
      var _this2 = this;
      this.options = options;
      return new Promise(function (resolve, reject) {
        _this2.eventHandler = new Cesium.ScreenSpaceEventHandler(_this2.viewer.scene.canvas);
        _this2.eventHandler.setInputAction(_this2.leftEvent, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        _this2.eventHandler.setInputAction(_this2.moveEvent, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        _this2.eventHandler.setInputAction(_this2.rightEvent(resolve, reject), Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      });
    }
  }, {
    key: "rightEvent",
    value: function rightEvent(resolve, reject) {
      var _this3 = this;
      var r = function r(e) {
        if (_this3.poiArr.length <= 2) {
          return;
        }
        var Position = getCoorFromPx({
          viewer: _this3.viewer,
          pxCoor: e.position,
          resultType: 'xyz'
        });
        if (Cesium.defined(Position)) {
          _this3.poiArr.pop();
          _this3.poiArr.push(Position);
          var endP = _this3.createPoint(Position);
          _this3.polygonBufferDataSource.entities.add(endP);
          _this3.poiArr.push(_this3.poiArr[0]);
          var turfPloygonArr = cart3ToDegree(_this3.poiArr);
          var turfPloygonData = turf.polygon([turfPloygonArr]);
          var bufferData = turf.buffer(turfPloygonData, _this3.options.radius, {
            units: 'meters'
          });
          var polygonArr = pointsToDegreesArray(bufferData.geometry.coordinates[0]);
          _this3.activeShapeBuffer = _this3.createAreaBuffer(polygonArr);
          _this3.viewer.entities.remove(_this3.floatingPoint);
          _this3.eventHandler.destroy();
          _this3.eventHandler = undefined;
          resolve(_this3.area);
          reject('err----');
        }
      };
      return r;
    }
  }, {
    key: "createPoint",
    value: function createPoint(poi, txt) {
      var point = new Cesium.Entity({
        position: poi,
        point: {
          color: Cesium.Color.YELLOW.withAlpha(0.6),
          pixelSize: 6
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        label: {
          text: txt || '',
          font: 'bold 12px MicroSoft YaHei',
          outlineWidth: 2,
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          verticalOrigin: Cesium.VerticalOrigin.TOP,
          pixelOffset: new Cesium.Cartesian2(15, 0),
          // pixelOffset: new Cesium.Cartesian2(-15, 16), //偏移量
          showBackground: true,
          backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
          backgroundPadding: new Cesium.Cartesian2(6, 3),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      });
      return point;
    }
  }, {
    key: "createArea",
    value: function createArea(poiArr, material) {
      var shape = this.viewer.entities.add({
        polygon: {
          hierarchy: poiArr,
          //positions : Array.<Cartesian3>
          // height: 20, // 多边形相对于椭球面的高度
          material: Cesium.Color.fromCssColorString('rgb(20, 120, 255)').withAlpha(0.33),
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH
          /* material: new Cesium.PolylineDashMaterialProperty({
          	color: Cesium.Color.fromCssColorString('rgb(20, 120, 255)').withAlpha(0.83),
          }), */
          // outline: true,
          // outlineColor: Cesium.Color.YELLOW,
          // outlineWidth: 2.0,
        }
      });
      return shape;
    }
  }, {
    key: "createAreaBuffer",
    value: function createAreaBuffer(poiArr, material) {
      var shape = this.viewer.entities.add({
        polygon: {
          hierarchy: new Cesium.PolygonHierarchy(Cesium.Cartesian3.fromDegreesArray(poiArr)),
          //positions : Array.<Cartesian3>
          // height: 20, // 多边形相对于椭球面的高度
          material: Cesium.Color.RED.withAlpha(0.6),
          /* material:
          	Cesium.Color.fromCssColorString(
          		'rgb(20, 120, 255)'
          	).withAlpha(0.33), */
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH
          /* material: new Cesium.PolylineDashMaterialProperty({
          	color: Cesium.Color.fromCssColorString('rgb(20, 120, 255)').withAlpha(0.83),
          }), */
          // outline: true,
          // outlineColor: Cesium.Color.YELLOW,
          // outlineWidth: 2.0,
        }
      });
      return shape;
    }
  }, {
    key: "reset",
    value: function reset() {
      this.removePolygon();
      this.poiArr = [];
      this.floatingPoint = undefined;
      this.activeShapeArea = undefined;
      this.activeShapeBuffer = undefined;
      if (this.eventHandler) {
        this.eventHandler.destroy();
      }
      this.eventHandler = undefined;
    }
  }, {
    key: "resetHandler",
    value: function resetHandler() {
      if (this.eventHandler) {
        this.eventHandler.destroy();
      }
      this.eventHandler = undefined;
    }
  }, {
    key: "removePolygon",
    value: function removePolygon() {
      this.viewer.entities.remove(this.activeShapeArea);
      this.viewer.entities.remove(this.activeShapeBuffer);
      this.polygonBufferDataSource.entities.removeAll();
    }
  }]);
}();

export { BufferLine, BufferPoint, BufferPolygon };
