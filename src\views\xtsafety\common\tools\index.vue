<template>
  <div class="tools">
    <!-- <div class="ttt-aaa">
      <div class="ttt-zzzz">ssss</div>
    </div> -->
    <div class="tools-list">
      <ul>
        <li
          v-for="(item, index) in toolData"
          :title="item.name"
          :class="{ isLiActive: item.value === currentTab }"
          @click="changeCurrentTool(item.value, index, item.name)"
        >
          <div>
            <el-image :src="item.src" class="ulimage">
              <div slot="placeholder" class="image-slot">
                加载中<span class="dot">...</span>
              </div>
            </el-image>
            <el-divider />
          </div>
        </li>
      </ul>
    </div>
    <div class="tools-panel" v-show="isPanelShow">
      <div class="tool-panel-header">
        <span>{{ currentName }}</span>
        <CircleClose
          @click="close"
          style="width: 1.5em; height: 1.5em; cursor: pointer"
        />
      </div>
      <keep-alive>
        <component :is="tabs[currentTab]"></component>
      </keep-alive>
    </div>
  </div>
</template>

<script setup>
import Measure from "@/earthCompCockpit/measure/index";
import Plot from "@/earthCompCockpit/plot/index";

import videoList from "@/earthCompCockpit/videoList/index";
import Buffer from "@/earthCompCockpit/buffer/index";
import ZzLayer from "@/earthCompCockpit/zzLayer/index";
import Layer from "@/earthCompCockpit/layer/index";
import company from "@/earthCompCockpit/company/index";
import MineCompany from "@/earthCompCockpit/MineCompany/index";
import RoutePlan from "@/earthCompCockpit/RoutePlan/index";
import CarTrack from "@/earthCompCockpit/CarTrack/index";
import Pipline from "@/earthCompCockpit/Pipline/index";
import Resource from "@/earthCompCockpit/resource/index";
import XtreeResource from "@/earthCompCockpit/XtreeResource/index";
import XtreeCamera from "@/earthCompCockpit/XtreeCamera/index";

// -----------------------------icon---------------------------
import resorceIcon from "@/assets/xtui/tools/resorce.png";
import newRsIcon from "@/assets/xtui/tools/newrs.png";
import bufferIcon from "@/assets/xtui/tools/buffer.png";
import personIcon from "@/assets/xtui/tools/personSet.png";
import personDuty from "@/assets/xtui/tools/personDuty.png";
import videoIcon from "@/assets/xtui/tools/video.png";
import watchtowersIcon from "@/assets/xtui/tools/watchtower.png";
import removeIcon from "@/assets/xtui/tools/remove.png";
import measureIcon from "@/assets/xtui/tools/measure.png";
import drawIcon from "@/assets/xtui/tools/draw.png";
import mapIcon from "@/assets/xtui/tools/map.png";
import companyIcon from "@/assets/xtui/tools/company.png";
import pathIcon from "@/assets/xtui/tools/path.png";
import carIcon from "@/assets/xtui/tools/car.png";
import pipIcon from "@/assets/xtui/tools/pip.png";
import analysisIcon from "@/assets/xtui/tools/analysis.png";
import { command } from "@/store/modules/command";
const store = command();
const currentTab = ref("");
const currentName = ref("边界图层");
const tabs = {
  Measure,
  Plot,

  videoList,
  Buffer,
  ZzLayer,
  Layer,
  company,
  RoutePlan,

  CarTrack,
  Pipline,
  Resource,
  XtreeResource,
  XtreeCamera,
  MineCompany,
};

function close() {
  isPanelShow.value = false;
  currentTab.value = "";
}

const isPanelShow = ref(false);
const toolData = reactive([
  // {
  //   id: 1,
  //   name: "测量",
  //   value: "Measure",
  //   src: measureIcon,
  // },
  // {
  //   id: 2,
  //   name: "绘制",
  //   value: "Plot",
  //   src: drawIcon,
  // },
  /* {
    id: 1,
    name: "行政图层",
    value: "ZzLayer",
    src: resorceIcon,
  },
  {
    id: 2,
    name: "管网数据",
    value: "Pipline",
    src: pipIcon,
  },
  {
    id: 21,
    name: "应急资源",
    value: "XtreeResource",
    src: newRsIcon,
  }, */
  {
    id: 3,
    name: "视频监控",
    value: "XtreeCamera",
    src: videoIcon,
  },
  {
    id: 4,
    name: "企业信息",
    value: "MineCompany",
    src: companyIcon,
  },

  {
    id: 5,
    name: "地图量测",
    value: "Measure",
    src: measureIcon,
  },
  {
    id: 6,
    name: "地图标绘",
    value: "Plot",
    src: drawIcon,
  },

  /* {
    id: 8,
    name: "路径规划",
    value: "RoutePlan",
    src: pathIcon,
  }, */
  /* {
    id: 9,
    name: "车辆轨迹",
    value: "CarTrack",
    src: carIcon,
  }, */

  /* {
    id: 21,
    name: "资源信息",
    value: "Resource",
    src: companyIcon,
  }, */
]);

const toolfuction = reactive([
  {
    id: 7,
    name: "复原",
    value: "remove",
    src: removeIcon,
  },
]);

function changeCurrentTool(val, index, name) {
  currentName.value = name;
  if (currentTab.value === val) {
    isPanelShow.value = false;

    currentTab.value = null;
  } else {
    isPanelShow.value = true;

    currentTab.value = val;
  }
}

onMounted(() => {
  // const toolbar = window.document.getElementById("#tools-list");
  const toolbar = window.document.querySelector(".tools-list");
  const toolbarWidth = toolbar.offsetWidth;

  // 先算后显示 有时间差 所以直接把移动量写死
  const toolPanel = window.document.querySelector(".tools-panel");
  toolPanel.style.position = "absolute";
  toolPanel.style.right = 55 + "px";
  // toolPanel.style.right = toolbarWidth + 20 + "px";
  toolPanel.style.top = 0 + "px";
});

const clickButton = (val, index) => {
  window.viewer.dataSources.removeAll();
  window.viewer.entities.removeAll();
  viewer.scene.primitives.removeAll();
};
</script>

<style scoped lang="scss">
.tools {
  width: 47px;
  background-color: rgb(58 81 105 / 85%);
  position: relative;
  padding: 5px;
  border: solid 1px #85a9e7;
  .ttt-aaa {
    width: 300px;
    height: 300px;
    position: fixed;
    top: 300px;
    left: 500px;
    background-color: #85e78f;
    // display: none;
    opacity: 0;

    .ttt-zzzz {
      // display: block;
      opacity: 0.9;

      width: 100px;
      height: 100px;
      background-color: #85a9e7;
      // position: fixed;
      // top: 300px;
      // left: 500px;
    }
  }
  .tools-list {
    width: 35px;
    ul {
      margin: 0;
      padding: 0;
      display: grid;
      grid-template-columns: repeat(1fr);

      li {
        list-style-type: none;
        width: 35px;
        height: 40px;
        // background-color: rgba(43, 141, 239, 0.239);
        display: flex;
        align-items: center;
        justify-content: center;
        &.isLiActive {
          // background-color: rgba(17, 100, 182, 0.639);
          .ulimage {
            color: #85a9e7;
            width: 25px;
            height: 22.5px;
            cursor: pointer;
          }
        }
        .ulimage {
          width: 20px;
          height: 17.5px;
          cursor: pointer;
        }
      }
    }
  }
  .tools-panel {
    // width: 260px;
    height: max-content;
    position: absolute;
    right: 55px;
    top: 0px;
    padding: 15px;

    // background: rgba(8, 76, 124, 0.5);
    // box-sizing: border-box;
    // border: 2px solid;
    // border-image: linear-gradient(
    //     180deg,
    //     #3cd5ff 0%,
    //     rgba(60, 213, 255, 0) 100%
    //   )
    //   2;
    // filter: blur(2px);
    // backdrop-filter: blur(5px);
    .tool-panel-header {
      background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center
        center;
      background-size: 96% 100%;
      height: 32px;
      width: 100%;
      margin: 15px 0px 15px 0px;
      padding: 5px 30px;
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: bold;
      letter-spacing: 0px;
      color: aliceblue;
      display: flex;
      justify-content: space-between;
    }
  }
  .tools-panel:before {
    //将背景和高斯模糊全部设置在了伪元素内，并让伪元素的z-index为-1，避免遮盖其他元素
    width: 100%;
    height: 100%;
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    background: rgba(8, 76, 124, 0.5);
    box-sizing: border-box;
    border: 2px solid;
    border-image: linear-gradient(
        180deg,
        #3cd5ff 0%,
        rgba(60, 213, 255, 0) 100%
      )
      2;
    backdrop-filter: blur(10px);
    padding: 8px;
    z-index: -1;
  }
  .tools-panel-person {
    // position: absolute;
    // margin: auto;
    // left: 0;
    // top: 0;
    // right: 0;
    // bottom: 0;
  }
}

:deep(.el-divider--horizontal) {
  // margin: 0 !important;
  margin: 5px 0 0 0 !important;
  // border-top: 1px #9ca5ba !important;
  border-top: 1px #9ca5ba var(--el-border-style) !important;
}
</style>
