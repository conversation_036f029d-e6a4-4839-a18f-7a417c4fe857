<template>
  <div class="router">
    <div class="router-path">
      <div class="router-header">路径规划</div>
      <div class="router-menu">
        <LocationInput label="起点" :point="startPoint" @pick="positionPick('start')" />
        <LocationInput label="终点" :point="endPoint" @pick="positionPick('end')" />
      </div>
    </div>
    <div class="router-position">
      <div class="header-with-add">
        <div class="router-header">必经点</div>
        <div class="positionPick-add" @click="positionPick('center')"></div>
      </div>
      <div class="router-menu">
        <el-table :data="tableData" style="width: 100%" max-height="150">
          <el-table-column prop="longitude" align="center" label="经度" />
          <el-table-column prop="latitude" align="center" label="纬度" />
          <el-table-column align="center" label="操作">
            <template #default="scope">
              <div class="deleteIcon" @click="handleDeleteClick(scope.$index)"></div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="router-region">
      <div class="header-with-add">
        <div class="router-header">绕行区域</div>
        <div class="positionPick-add" @click="positionPickPolygon()"></div>
      </div>
      <div class="router-menu">
        <el-table :data="tableDataRegion" style="width: 100%" max-height="150">
          <el-table-column
            prop="name"
            label="绕行区域"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column align="center" label="操作">
            <template #default="scope">
              <div
                class="deleteIcon"
                @click="handleDeleteRegionClick(scope.$index)"
              ></div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="dialogbutton">
      <div class="masbutton cancer" @click="resetForm()">
        <span>清空规划</span>
      </div>
      <div class="masbutton submit" @click="submitForm()">
        <span>开始规划</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import LocationInput from "@/earthCompStyle/router/LocationInput.vue"; // 假设我们创建了这个组件

const tableData = ref([]);
const tableDataRegion = ref([]);
let entity = reactive({});
let pointpick_handler = reactive({});
let polygonHandler = ref(null);
let positions = [];
let polygonEntity = ref(null);

const initEntity = () => {
  entity = window.viewer.entities.add({
    label: { show: true },
    point: { pixelSize: 5, color: Cesium.Color.RED },
  });
};

const startPoint = reactive({ longitude: "", latitude: "" });
const endPoint = reactive({ longitude: "", latitude: "" });

onMounted(() => {
  emitter.on("viewerLoad", () => {
    initEntity();
  });
  if (window.viewer) {
    initEntity();
  }
});

const handleDeleteClick = (index) => {
  tableData.value.splice(index, 1);
};

const handleDeleteRegionClick = (index) => {
  viewer.entities.remove(tableDataRegion.value[index].entity);
  tableDataRegion.value.splice(index, 1);
};

const submitForm = () => {};

const resetForm = () => {
  tableData.value = [];
  tableDataRegion.value.forEach((region) => {
    viewer.entities.remove(region.entity);
  });
  tableDataRegion.value = [];
};

const positionPick = (val) => {
  pointpick_handler = new Cesium.ScreenSpaceEventHandler(window.viewer.scene.canvas);
  pointpick_handler.setInputAction((click) => {
    const cartesian = window.viewer.camera.pickEllipsoid(
      click.position,
      viewer.scene.globe.ellipsoid
    );
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    const longitude = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
    const latitude = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);

    if (val === "start") {
      startPoint.longitude = longitude;
      startPoint.latitude = latitude;
    } else if (val === "end") {
      endPoint.longitude = longitude;
      endPoint.latitude = latitude;
    } else if (val === "center") {
      tableData.value.push({ longitude, latitude });
    }

    entity.show = true;
    entity.position = cartesian;
    removeHandler();
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};

const positionPickPolygon = () => {
  // Reset positions and handler
  positions = [];
  if (polygonHandler.value) {
    polygonHandler.value.destroy();
    polygonHandler.value = null;
  }
  if (polygonEntity.value) {
    viewer.entities.remove(polygonEntity.value);
    polygonEntity.value = null;
  }

  polygonHandler.value = new Cesium.ScreenSpaceEventHandler(window.viewer.scene.canvas);
  polygonHandler.value.setInputAction((click) => {
    const cartesian = window.viewer.camera.pickEllipsoid(
      click.position,
      viewer.scene.globe.ellipsoid
    );
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian);

    const longitude = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
    const latitude = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);

    positions.push(parseFloat(longitude), parseFloat(latitude));

    if (positions.length >= 6) {
      if (polygonEntity.value) {
        viewer.entities.remove(polygonEntity.value);
      }
      // Close the polygon by connecting the last point to the first point
      const tempPositions = positions.concat([positions[0], positions[1]]);
      polygonEntity.value = viewer.entities.add({
        polygon: {
          hierarchy: createPolygon(tempPositions),
          material: Cesium.Color.RED.withAlpha(0.5),
        },
      });
    } else if (positions.length >= 2) {
      if (polygonEntity.value) {
        viewer.entities.remove(polygonEntity.value);
      }
      polygonEntity.value = viewer.entities.add({
        polygon: {
          hierarchy: createPolygon(positions),
          material: Cesium.Color.RED.withAlpha(0.5),
        },
      });
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  polygonHandler.value.setInputAction(() => {
    let tempPositions = [];
    if (positions.length >= 6) {
      // Close the polygon by connecting the last point to the first point
      tempPositions = positions.concat([positions[0], positions[1]]);
      const polygonHierarchy = createPolygon(tempPositions);

      // Add polygon entity to the scene
      polygonEntity.value = viewer.entities.add({
        polygon: {
          hierarchy: polygonHierarchy,
          material: Cesium.Color.RED.withAlpha(0.5),
        },
      });
    }
    // Push coordinates of the polygon to tableDataRegion
    tableDataRegion.value.push({
      tempPositions: tempPositions,
      polygonEntity: polygonEntity.value,
    });

    // Cleanup handler
    polygonHandler.value.destroy();
    polygonHandler.value = null;
  }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
};

const removeHandler = () => {
  entity.show = false;
  pointpick_handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
</script>
<style scoped lang="scss">
.router::before {
  content: "";
  position: absolute;
  width: 358px;
  min-height: 628px;
  height: max-content;
  z-index: -1;
  background: rgba(8, 76, 124, 0.5);
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%) 2;
  backdrop-filter: blur(10px);
  top: -10px;
}

.router {
  width: 358px;
  min-height: 628px;
  height: max-content;

  .router-header {
    background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center center;
    background-size: 100% 100%;
    width: 184.04px;
    height: 32px;
    padding: 5px 30px;
    margin: 5px 10px;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: bold;
    color: aliceblue;
  }

  .router-menu {
    padding: 10px 10px;

    .deleteIcon {
      width: 100%;
      height: 12px;
      background: url("@/assets/xtui/tools/deleteicon.png") no-repeat center center;
    }

    .router-menu-point {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      width: 100%;
      align-items: center;
      margin: 5px 0px;

      span {
        font-family: 思源黑体;
        font-size: 12px;
        font-weight: normal;
        line-height: 20px;
        text-align: right;
        color: #ffffff;
      }
    }
  }

  .header-with-add {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .positionPick-add {
      cursor: pointer;
      width: 32px;
      height: 32px;
      background-repeat: no-repeat;
      background-image: url("@/assets/xtui/tools/addimg.png");
    }
  }
}

.dialogbutton {
  width: 100%;
  display: flex;
  justify-content: space-evenly;

  .masbutton {
    width: 100px;
    height: 32px;
    color: #ffffff;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    text-align: center;
    display: flex;
    align-content: center;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &.submit {
      background-image: url("@/assets/xtui/command/onduty/sunmitback.png");
    }
    &.cancer {
      background-image: url("@/assets/xtui/command/onduty/cancleback.png");
    }
  }
}

.positionPick {
  cursor: pointer;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
  background-image: url("@/assets/xtui/tools/pickimg.png");
}
</style>
