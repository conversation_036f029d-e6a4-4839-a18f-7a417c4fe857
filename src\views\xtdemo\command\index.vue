

<template>
  <div class="command">
    <div class="command-header">
      <Header :name="headerName">
        <template #menu>
          <div class="menu">
            <!-- <el-button>应急预案</el-button>
            <el-button>演练信息</el-button>
            <el-button>处置流程</el-button> -->
          </div>
        </template>
        <template #userInfo>
          <div class="userinfo">
            <!-- <el-button>指挥席位</el-button> -->
          </div>
        </template>
      </Header>
    </div>
    <div class="command-tools">
      <Tools></Tools>
    </div>
    <div class="command-menus">
      <div
        v-for="(item, index) in commandMenus"
        @click="changeMenu(item.value, index)"
        class="command-menus-item"
        :class="{ isActiveMenu: index === currentMenuIndex }"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="left-panel">
      <LeftBase></LeftBase>
    </div>
    <div class="right-panel">
      <!-- <component :is="componentName"></component> -->
      <keep-alive>
        <component :is="rightTabs[currentRightTab]"></component>
      </keep-alive>
    </div>
    <div class="table-wrapper">
      <el-table
        :data="tableData"
        class="table-demo"
        width="100%"
        :row-style="tableRowStyle"
        :cell-style="cellStyle"
        :header-cell-style="tableHeaderStyle"
        highlight-current-row
      >
        <el-table-column prop="date" label="Date" width="180" />
        <el-table-column prop="name" label="Name" width="180" />
        <el-table-column prop="address" label="Address" />
      </el-table>
    </div>
  </div>
</template>

<script setup>
import Header from "@/views/xtdemo/common/header/index";
import Tools from "@/views/xtdemo/common/tools/index";

import LeftBase from "@/views/xtdemo/command/leftpanel/LeftBase";
import RightBase from "@/views/xtdemo/command/rightpanel/RightBase";
import RightInstruct from "@/views/xtdemo/command/rightpanel/RightInstruct";
import RightNode from "@/views/xtdemo/command/rightpanel/RightNode";
import RabbitmqClient from "@/utils/rmq.js";
import { nextTick, ref, reactive, computed, onMounted } from "vue";
import { sendRmqMsg } from "@/api/drill/common/index.js";

import { getMyKey } from "../common/js/tool";
// 获取xttid

const xttid = getMyKey("xttid");
// const headerName = ref("");
const headerName = ref("demo-测试");

const currentRightTab = ref("RightBase");
const rightTabs = {
  RightBase,
  RightInstruct,
  RightNode,
};
const commandMenus = reactive([
  {
    name: "menu1",
    value: "RightBase",
  },
  {
    name: "menu2",
    value: "RightInstruct",
  },
  {
    name: "menu3",
    value: "RightNode",
  },
]);

const currentMenuIndex = ref(0);

function changeMenu(val, i) {
  currentRightTab.value = val;
  currentMenuIndex.value = i;
}
// -------------------------地球操作----------------------------

function czFn() {
  window.viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(
      105.01145621478398,
      23.58828659369774,
      95000.0
    ),
  });
}

// -------------------------连接mq----------------------------
const options = {
  rmqUrl: `${window.xtmapConfig.rmq.urlB}`,
};
const rmqObj = new RabbitmqClient(options);

function getData(d) {
  // console.log(d + "-------ffff");
  // console.dir(JSON.parse(d), "----eee");
  // JSON.parse(d);
  const x = JSON.parse(d);
  window.plotPlugin.set(x.msg.map);
}

onMounted(() => {
  emitter.on("viewerLoad", (data) => {
    czFn();
  });

  rmqObj.initMQ().then(() => {
    rmqObj.subMsg(`${window.xtmapConfig.rmq.subB}/${xttid}`, (msg) => {
      console.log(msg.body, "msg  body");
      // console.log(JSON.parse(msg.body), "msg  body  parse");
      // console.log(JSON.parse(JSON.stringify(msg.body)) + "-------555555555");
      // window.plotPlugin.set(JSON.parse(msg.body));
      getData(msg.body);
    });
    rmqObj.reconnectMQ(3);
  });
});

// -------------------------表格demo----------------------------

const tableData = [
  {
    date: "2016-05-03",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-01",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
];

const tableRowStyle = (row) => {
  return row.rowIndex % 2 === 0
    ? { background: "rgba(14, 95, 255, 0.0)", height: "56px" }
    : // : { background: "rgba(14, 95, 255, 0.3)" };
      { background: "rgba(92 ,217 ,100 , 0.5)", height: "36px" };
};
const cellStyle = () => {
  return {
    background: "transparent",
    color: "yellow",
    fontSize: "12px",
    textAlign: "center",
    // padding: "30px",  //这个和css里的.cell都可以
  };
};
const tableHeaderStyle = () => {
  return {
    height: "66px",
    // background: "#ca6532de",
    background: " rgba(102, 63, 11, 0.208)",
    color: "blue",
    fontSize: "12px",
    textAlign: "center",
  };
};
</script>


<style scoped lang="scss">
.command {
  // background-color: rgba(113, 197, 10, 0.917);
  // background: url("@/assets/xtui/layout/leftbg.png");
  // background-size: 100% 100%;
  width: 100%;
  height: 100vh;

  position: relative;
  top: 0;
  left: 0;
  .command-header {
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 100;
    pointer-events: auto;
  }
  .command-tools {
    position: absolute;
    right: 500px;
    top: 180px;
    z-index: 100;
    pointer-events: auto;
  }
  .command-menus {
    position: absolute;
    left: 36%;
    top: 106px;
    z-index: 99;
    pointer-events: auto;
    width: 500px;
    height: 60px;
    // display: none;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    background-color: rgba(160, 81, 45, 0.531);
    .command-menus-item {
      background-color: rgba(63, 165, 229, 0.305);
      padding: 10px 20px;
    }
    .isActiveMenu {
      background-color: rgba(63, 165, 229, 0.805);
    }
  }
  .left-panel {
    position: absolute;
    left: 40px;
    top: 0;
    z-index: 99;
    pointer-events: auto;
  }
  .right-panel {
    position: absolute;
    right: 30px;
    top: 0;
    z-index: 99;
    pointer-events: auto;
  }
  .table-wrapper {
    width: 650px;
    height: 400px;
    position: absolute;
    left: 30%;
    top: 30%;
    padding: 30px;
    background-color: rgba(102, 63, 11, 0);
    pointer-events: auto;
    :deep(.el-table) {
      --el-table-bg-color: transparent; //背景色
      --el-table-border-color: red; //最底部的一根横线
      // --el-table-border-color: transparent;
      --el-table-border: none; //行中间的多条横线

      // --el-table-row-hover-bg-color: red;
      // --el-table-current-row-bg-color: green;
      // --el-table-header-bg-color: yellow; //表头背景色  不生效

      // --el-table-tr-bg-color: yellow; //行背景色 生效  会被js代码里的覆盖
      // --el-table-expanded-cell-bg-color: transparent;
      // --el-table-fixed-right-border-color: red; //右侧固定列的边框颜色
      .cell {
        padding: 10px;
      }
      /* .el-table__body {
        tr:hover > td {
          background-color: rgb(230, 42, 73) !important;
        }
      } */
      /* .el-table__body {
        .el-table__row:hover > td {
          background-color: rgb(37, 179, 32) !important;
        }
      } */
    }
    /* :deep(.el-table tbody tr:hover) > td {
      background-color: #a0131371;
    } */
    :deep(.el-table__body .el-table__row:hover > td) {
      background-color: #0d48dd !important;
      // 核心不在于scss咋写  而是加important
    }

    /* :deep(.el-table .cell) {
           这个可以  上门两个不行
    } */
  }
}

//  单元格 https://blog.csdn.net/qq_55031668/article/details/128831533?utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-0-128831533-blog-141405745.235^v43^pc_blog_bottom_relevance_base9&spm=1001.2101.3001.4242.1&utm_relevant_index=3
</style>

