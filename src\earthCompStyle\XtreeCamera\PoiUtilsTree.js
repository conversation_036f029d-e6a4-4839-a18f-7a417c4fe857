export default class PoiUtils {
	constructor() {
		poiHanderLeft = null;
	}
	static createPoi(i, type, picName) {
		/* console.log(
			new URL(`@/assets/icons/billboard/${type}.png`, import.meta.url)
				.href,
			'wwwww',
			type
		); */
		const entity_p = new Cesium.Entity({
			position: Cesium.Cartesian3.fromDegrees(i.longitude, i.latitude),
			// position: Cesium.Cartesian3.fromDegrees(i.lon, i.lat),
			id: i.id || i.uid,
			billboard: {
				image: `${window.xtmapConfig.billboard.path}${picName}.png`,
				// image: `@/assets/icons/billboard/${type}.png`,
				// image: new URL(
				// 	`@/assets/icons/billboard/${type}.png`,
				// 	import.meta.url
				// ).href,
				height: window.xtmapConfig.billboard.h,
				width: window.xtmapConfig.billboard.w,
			},
			label: {
				text: i.name || i.unitName || i.materialName,
				font: 'bold 12px MicroSoft YaHei',
				outlineWidth: 2,
				horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
				// verticalOrigin: Cesium.VerticalOrigin.TOP,
				pixelOffset: new Cesium.Cartesian2(0, -37), //偏移量
				showBackground: true,
				backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
				backgroundPadding: new Cesium.Cartesian2(6, 3),
				scaleByDistance: new Cesium.NearFarScalar(2000, 1, 12000, 0),
			},
			properties: {
				use: 'resource_tree',
				type: type, // 这个是关键
				query: i.type, // 这个是关键
				confidenceLabel: 'xxx',
				detectionTime: 'xxx',
				cameraItem: i,
			},
		});
		return entity_p;
	}

	static createPoiByPri(i, picName) {
		const bill = {
			position: Cesium.Cartesian3.fromDegrees(i.lon, i.lat),
			id: i,
			// id: i.id,
			image: `${window.xtmapConfig.billboard.path}${picName}.png`,
			height: window.xtmapConfig.billboard.h,
			width: window.xtmapConfig.billboard.w,
		};
		return bill;
	}

	static addListener(viewer, callback) {
		this.poiHanderLeft = new Cesium.ScreenSpaceEventHandler(
			viewer.scene.canvas
		);
		this.poiHanderLeft.setInputAction((e) => {
			const pick = viewer.scene.pick(e.position);
			if (
				// 防止点击报错
				Cesium.defined(pick) &&
				Cesium.defined(pick.id)
				// pick.id.properties.use._value === 'resource_tree'
			) {
				callback(viewer, pick);
			}
		}, Cesium.ScreenSpaceEventType.LEFT_CLICK);

		return this.poiHanderLeft;
	}
	static getListener() {
		return this.poiHanderLeft;
	}
	static removeListener() {
		this.poiHanderLeft?.destroy();
		this.poiHanderLeft = null;
	}
}

// 使用示例

// function getAndAddPoi(allPoi, collection, type) {
// 	currentItem.value = allPoi;
// 	for (const i of allPoi) {
// 	  let e_poi = PoiUtils.createPoiByPicName(i, "uavType", "uav");  //中间的type会加到properties中
// 	  collection.entities.add(e_poi);
// 	}
// 	// viewer.flyTo(collection);
// 	PoiUtils.addListenerNew(window.viewer, eventCallbackSat);
//   }
//   function eventCallbackSat(viewer, pick) {

// 	if (!pick.id.properties || pick.id.properties.type._value !== "uavType") { //type对应的就是properties中的
// 	  return;
// 	}

// 	if (currentItem.value[0].id === pick.id._id) {
// 	  popupItem.value = currentItem.value[0];
// 	}

// 	isPopShow.value = true;
// 	// 固定面板
// 	nextTick(() => {
// 	  const popDom = window.document.querySelector("#popup-info-uav");
// 	  popDom.style.position = "fixed";
// 	  popDom.style.left = "608px";
// 	  popDom.style.top = "270px";
// 	});

// 	// 随地球移动面板
// 	/* nextTick(() => {
// 	  const popDom = window.document.querySelector("#popup-info-uav");
// 	  postRenderingFn = viewer.scene.postRender.addEventListener(() => {
// 		const screenC = viewer.scene.cartesianToCanvasCoordinates(
// 		  pick.id.position._value
// 		);
// 		if (screenC) {
// 		  popDom.style.left = screenC.x - 1 + "px";
// 		  popDom.style.top = screenC.y - 395 + "px";
// 		}
// 	  });
// 	}); */
//   }
