import request from '@/utils/request'

// 查询承包商复工申请列表
export function listFieldOperation(query) {
  return request({
    url: '/contractor/FieldOperation',
    method: 'get',
    params: query
  })
}

// 查询承包商复工申请详细
export function getFieldOperation(id) {
  return request({
    url: '/contractor/FieldOperation/' + id,
    method: 'get'
  })
}

// 新增承包商复工申请
export function addFieldOperation(data) {
  return request({
    url: '/contractor/FieldOperation',
    method: 'post',
    data: data
  })
}

// 修改承包商复工申请
export function updateFieldOperation(data) {
  return request({
    url: '/contractor/FieldOperation',
    method: 'post',
    data: data
  })
}

// 删除承包商复工申请
export function delFieldOperation(data) {
  return request({
    url: '/contractor/FieldOperation/',
    method: 'delete',
    data: data
  })
}
