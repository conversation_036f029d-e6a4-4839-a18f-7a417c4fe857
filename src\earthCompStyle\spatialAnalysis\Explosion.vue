<template>
  <div class="router">
    <div class="router-path">
      <div class="router-header">爆炸分析</div>
      <div class="router-menu">
        <div class="router-menu-point">
          <span>中心点</span>
          <el-input
            v-model.number="ruleForm.lon"
            style="width: 80px; height: 32px"
            placeholder="请输入经度"
          />
          <el-input
            v-model.number="ruleForm.lat"
            style="width: 80px; height: 32px"
            placeholder="请输入纬度"
          />
          <div class="positionPick" @click="positionPick()"></div>
        </div>

        <el-form
          ref="ruleFormRef"
          style="max-width: 600px"
          :model="ruleForm"
          label-width="auto"
          class="demo-ruleForm"
          :size="formSize"
          label-position="left"
          status-icon
        >
          <el-form-item label="体积" prop="totalReserves">
            <el-input v-model="ruleForm.totalReserves" placeholder="kg" />
          </el-form-item>
          <el-form-item label="锅炉压力" prop="sI">
            <el-input v-model="ruleForm.sI" placeholder="Pa" />
          </el-form-item>
          <el-form-item label="TNT燃烧热" prop="qf">
            <el-input v-model="ruleForm.qf" placeholder="KJ/kg" />
          </el-form-item>
          <el-form-item label="干饱和水蒸气占总体积比例" prop="atmosphericPressure">
            <el-input v-model="ruleForm.atmosphericPressure" placeholder="Pa" />
          </el-form-item>
        </el-form>

        <div class="router-menu-point">
          <div class="result-img damageRadius"></div>
          <span>轻伤半径</span>
          <el-input
            v-model.number="ruleForm.damageRadius"
            style="width: 220px; height: 32px"
            placeholder="计算结果：轻伤半径"
          />
        </div>
        <div class="router-menu-point">
          <div class="result-img injuryRadius"></div>
          <span>重伤半径</span>
          <el-input
            v-model.number="ruleForm.injuryRadius"
            style="width: 220px; height: 32px"
            placeholder="计算结果：重伤半径"
          />
        </div>
        <div class="router-menu-point">
          <div class="result-img deathRadius"></div>
          <span>死亡半径</span>
          <el-input
            v-model.number="ruleForm.deathRadius"
            style="width: 220px; height: 32px"
            placeholder="计算结果：死亡半径"
          />
        </div>
      </div>
    </div>

    <div class="dialogbutton">
      <div class="masbutton cancer" @click="resetForm()">
        <span>清空</span>
      </div>
      <div class="masbutton submit" @click="submitForm()">
        <span>模拟</span>
      </div>
    </div>
  </div>
</template>
<script setup>
import { DrawCircle } from "./drawCircle.js";
import { ElMessage } from "element-plus";
let drawCircle = reactive({});
let radusValue = ref(0);
const formSize = ref("small");
const ruleFormRef = ref();
let ruleForm = reactive({
  atmosphericPressure: 101300,
  density: 0,
  ep: 1.8,
  explosiveType: 0,
  mI: 17000,
  personnelDensity: 2,
  qf: 4520,
  sI: 44000,
  totalReserves: 15,
  lat: undefined,
  lon: undefined,
  deathRadius: undefined,
  injuryRadius: undefined,
  damageRadius: undefined,
  radiusOfMinorInjuryed: "",
  radiusOfPropertyLoss: "",
  radiusOfSeriouslyInjured: "",
});
let pointpick_handler = reactive({});

onMounted(() => {
  emitter.on("viewerLoad", (data) => {
    drawCircle = new DrawCircle(window.viewer);
  });
  if (window.viewer) {
    drawCircle = new DrawCircle(window.viewer);
  }
});
onUnmounted(() => {
  if (drawCircle && drawCircle.remove()) drawCircle.remove();
});
const getradusValue = (val) => {
  ruleForm.deathRadius = val.toFixed(2);
  ruleForm.injuryRadius = (val * 1.5).toFixed(2);
  ruleForm.damageRadius = (val * 5).toFixed(2);
};
const submitForm = () => {
  radusValue.value =
    parseFloat(ruleForm.totalReserves) * 10 +
    parseFloat(ruleForm.sI) / 1000 +
    parseFloat(ruleForm.qf) / 100 +
    parseFloat(ruleForm.atmosphericPressure) / 10000;

  getradusValue(radusValue.value);
  if (drawCircle && drawCircle.remove()) drawCircle.remove();
  if (ruleForm.lat && ruleForm.lon) {
    // 绘制一个圆形
    drawCircle.draw(ruleForm.lat, ruleForm.lon, radusValue.value, Cesium.Color.GREEN);
    drawCircle.draw(
      ruleForm.lat,
      ruleForm.lon,
      radusValue.value * 1.5,
      Cesium.Color.YELLOW
    );
    drawCircle.draw(ruleForm.lat, ruleForm.lon, radusValue.value * 2, Cesium.Color.RED);
    // 将相机飞行到圆的可视范围
    window.viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(
        ruleForm.lon,
        ruleForm.lat,
        radusValue.value * 5
      ),
      orientation: {
        heading: Cesium.Math.toRadians(0.0),
        pitch: Cesium.Math.toRadians(-90.0),
        roll: 0.0,
      },
    });
  } else {
    ElMessage.error("请选择经纬度");
  }
};

const resetForm = () => {
  if (drawCircle && drawCircle.remove()) drawCircle.remove();
  ruleForm.lat = undefined;
  ruleForm.lon = undefined;
  ruleForm.totalReserves = 15;
  ruleForm.sI = 44000;
  ruleForm.qf = 4520;
  ruleForm.atmosphericPressure = 101300;
  ruleForm.deathRadius = undefined;
  ruleForm.injuryRadius = undefined;
  ruleForm.damageRadius = undefined;
};

const positionPick = (val) => {
  pointpick_handler = new Cesium.ScreenSpaceEventHandler(window.viewer.scene.canvas);
  pointpick_handler.setInputAction((click) => {
    const cartesian = window.viewer.camera.pickEllipsoid(
      click.position,
      viewer.scene.globe.ellipsoid
    );
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    ruleForm.lon = parseFloat(Cesium.Math.toDegrees(cartographic.longitude).toFixed(6));
    ruleForm.lat = parseFloat(Cesium.Math.toDegrees(cartographic.latitude).toFixed(6));
    removehandler();
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};

const removehandler = () => {
  pointpick_handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
</script>
<style scoped lang="scss">
.router {
  width: 315px;
  min-height: 450px;
  height: max-content;
  background: rgba(8, 76, 124, 0.5);
  background: rgba(8, 76, 124, 0.5);
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%) 2;
  position: fixed;
  backdrop-filter: blur(10px);
  top: 100px;
  left: 445px;
  .router-header {
    background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center center;
    background-size: 100% 100%;
    width: 100%;
    height: 32px;
    margin: 10px 2px 5px 2px;
    padding: 5px 30px;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0px;
    color: aliceblue;
  }
  .router-menu {
    padding: 10px 10px;
    .deleteIcon {
      width: 100%;
      height: 12px;
      background: url("@/assets/xtui/tools/deleteicon.png") no-repeat center center;
    }
    .router-menu-point {
      display: flex;
      flex-direction: row;
      // justify-content: space-around;
      justify-content: space-between;
      width: 100%;
      align-items: center;
      margin: 5px 0px;
      color: aliceblue;
      span {
        font-family: 思源黑体;
        font-size: 12px;
        font-weight: 700;
        line-height: 20px;
        text-align: right;
        letter-spacing: 0px;
        font-feature-settings: "kern" on;
        color: #ffffff;
        width: 78px;
        text-align: left;
      }
    }
  }
}

.dialogbutton {
  width: 100%;
  display: flex;
  justify-content: space-evenly;
  .masbutton {
    width: 100px;
    height: 32px;
    color: #ffffff;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    text-align: center;
    display: flex;
    align-content: center;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  .submit {
    background-image: url("@/assets/xtui/command/onduty/sunmitback.png");
  }
  .cancer {
    background-image: url("@/assets/xtui/command/onduty/cancleback.png");
  }
}

.positionPick {
  cursor: pointer;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
  background-image: url("@/assets/xtui/tools/pickimg.png");
}
.positionPick-add {
  cursor: pointer;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
  background-image: url("@/assets/xtui/tools/addimg.png");
}

.result-img {
  width: 32px;
  height: 15px;
  margin: 1px 5px;

  &.damageRadius {
    background-color: rgb(154 119 5 / 96%);
  }
  &.injuryRadius {
    background-color: rgb(197 108 7);
  }
  &.deathRadius {
    background-color: rgb(146 27 22);
  }
}
</style>
