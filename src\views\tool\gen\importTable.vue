<template>
  <!-- 导入表 -->
  <el-dialog
    title="导入表"
    v-model="visible"
    width="1080px"
    top="5vh"
    append-to-body
  >
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-row :gutter="20">
        <el-col :sm="18" :lg="24">
          <span style="font-weight: bold;">基础配置</span>
          <hr />
        </el-col>
      </el-row>
      <el-form-item label="模式" prop="mode">
        <el-select
          v-model="queryParams.schemaName"
          placeholder="请选择模式"
          style="width: 192px"
        >
          <el-option
            v-for="item in schemaList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="作者" prop="author">
        <el-input
          v-model="queryParams.GenConfig.author"
          placeholder="请输入作者"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="默认生成包路径" prop="packageName">
        <el-input
          v-model="queryParams.GenConfig.packageName"
          placeholder="请输入默认生成包路径"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否自动去除表前缀" prop="autoRemovePre">
        <el-radio v-model="queryParams.GenConfig.autoRemovePre" :label="true">
          是
        </el-radio>
        <el-radio v-model="queryParams.GenConfig.autoRemovePre" :label="false">
          否
        </el-radio>
      </el-form-item>
      <el-form-item label="表前缀" prop="tablePrefix">
        <el-input
          v-model="queryParams.GenConfig.tablePrefix"
          placeholder="请输入表前缀"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-row :gutter="20">
        <el-col :sm="18" :lg="24">
          <span style="font-weight: bold;">搜索条件</span>
          <hr />
        </el-col>
      </el-row>
      <el-form-item label="表名称" prop="tableName">
        <el-input
          v-model="queryParams.tableName"
          placeholder="请输入表名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="表描述" prop="tableComment">
        <el-input
          v-model="queryParams.tableComment"
          placeholder="请输入表描述"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row style="height: 380px;">
      <el-table
        @row-click="clickRow"
        ref="table"
        :data="dbTableList"
        @selection-change="handleSelectionChange"
        height="290px"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column
          prop="tableName"
          label="表名称"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="tableComment"
          label="表描述"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column prop="createTime" label="创建时间"></el-table-column>
        <el-table-column prop="updateTime" label="更新时间"></el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleImportTable">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  schema as getSchema,
  config,
  listDbTable,
  importTable,
} from "@/api/tool/gen";

const schemaList = ref([]);
const total = ref(0);
const visible = ref(false);
const tables = ref([]);
const dbTableList = ref([]);
const { proxy } = getCurrentInstance();

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  tableName: "",
  tableComment: "",
  schemaName: "",
  GenConfig: {
    author: "",
    packageName: "",
    autoRemovePre: false,
    tablePrefix: "",
  },
});

const emit = defineEmits(["ok"]);

// 查询数据库模式
const getSchemaList = async () => {
  const res = await getSchema();
  if (res.code === 200) {
    schemaList.value = res.data.map((item) => {
      return {
        label: item.schemaComment,
        value: item.schemaName,
      };
    });
    queryParams.schemaName = schemaList?.value[0]?.value ?? "";
  }
};

// 查询代码生成默认配置
const getConfigData = async () => {
  const res = await config();
  if (res.code === 200) {
    queryParams.GenConfig.author = res.data?.author ?? "";
    queryParams.GenConfig.packageName = res.data?.packageName ?? "";
    queryParams.GenConfig.autoRemovePre = res.data?.autoRemovePre ?? false;
    queryParams.GenConfig.tablePrefix = res.data?.tablePrefix ?? "";
  }
};

/** 查询参数列表 */
async function show() {
  try {
    await getSchemaList();
    await getConfigData();
  } catch (error) {
    console.error("Error fetching configuration data:", error);
  } finally {
    getList();
    visible.value = true;
  }
}
/** 单击选择行 */
function clickRow(row) {
  proxy.$refs.table.toggleRowSelection(row);
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
  tables.value = selection.map((item) => item.tableName);
}
/** 查询表数据 */
function getList() {
  const { pageNum, pageSize, tableName, tableComment, schemaName } =
    queryParams;

  const params = {
    pageNum,
    pageSize,
    tableName,
    tableComment,
    schemaName,
  };

  listDbTable(params).then((res) => {
    dbTableList.value = res.rows;
    total.value = res.total;
  });
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 导入按钮操作 */
function handleImportTable() {
  const { schemaName, GenConfig } = queryParams;

  if (!schemaName) {
    proxy.$modal.msgError("请选择模式");
    return;
  }
  const tableNames = tables.value.join(",");
  if (tableNames == "") {
    proxy.$modal.msgError("请选择要导入的表");
    return;
  }

  // query参数
  const params = {
    schemaName,
    tables: tableNames,
  };

  // body参数
  const bodyParams = {
    ...GenConfig,
  };

  importTable(params, bodyParams).then((res) => {
    proxy.$modal.msgSuccess(res.msg);
    if (res.code === 200) {
      visible.value = false;
      emit("ok");
    }
  });
}

defineExpose({
  show,
});
</script>
