import request from '@/utils/request'

// 查询综合评价结果列表
export function listEvaluationFinal(query) {
  return request({
    url: '/contractor/evaluationFinal',
    method: 'get',
    params: query
  })
}

// 查询综合评价结果详细
export function getEvaluationFinal(id) {
  return request({
    url: '/contractor/evaluationFinal/' + id,
    method: 'get'
  })
}

// 新增综合评价结果
export function addEvaluationFinal(data) {
  return request({
    url: '/contractor/evaluationFinal',
    method: 'post',
    data: data
  })
}

// 修改综合评价结果
export function updateEvaluationFinal(data) {
  return request({
    url: '/contractor/evaluationFinal',
    method: 'post',
    data: data
  })
}

// 删除综合评价结果
export function delEvaluationFinal(data) {
  return request({
    url: '/contractor/evaluationFinal/',
    method: 'delete',
    data: data
  })
}
