
import request from '@/utils/request'

// 查询监测大类
export function getSkyBased(query) {
  return request({
    url: '/mine/skyBase',
    method: 'get',
    params: query
  })
}



export function addSkyBased(data) {
  return request({
    url: '/mine/skyBase',
    method: 'post',
    data: data
  })
}


export function updateSkyBased(data) {
  return request({
    url: '/mine/skyBase',
    method: 'post',
    data: data
  })
}


export function delSkyBased(ids) {
  return request({
    url: '/mine/skyBase',
    method: 'delete',
    data: ids
  })
}
