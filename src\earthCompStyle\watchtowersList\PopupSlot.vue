<!--
 * @Descripttion: santana
 * @LastEditTime: 2022-04-10 20:40:45
-->

<template>
  <div class="popup">
    <div class="popup-header">
      <div class="popup-header-title">{{ popupTitle }}</div>
      <CircleClose
        @click="closePopup"
        style="width: 1.5em; height: 1.5em; margin-right: 8px"
      />
    </div>
    <div class="popup-content">
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  popupTitle: {
    type: String,
    default: "",
  },
  item: {
    type: Object,
  },
});
const emits = defineEmits(["closePopup"]); //addEvent是父元素里面的一个函数方法，通过这个方法传参
const closePopup = () => {
  emits("closePopup");
};
</script>

<style scoped lang="scss">
.popup {
  width: 355px; /* 固定宽度 */
  height: 250px; /* 固定高度 */
  background: rgba(8, 76, 124, 0.5);
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%) 2;

  position: fixed;
  padding: 15px 15px 15px 15px;
  color: white;
  .popup-header {
    cursor: pointer;
    background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center center;
    background-size: 100% 100%;
    height: 32px;
    width: 100%;
    margin: 5px 0px 10px 0px;
    padding: 5px 10px;
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: bold;
    letter-spacing: 0px;
    color: aliceblue;
    display: flex;
    justify-content: space-between;
    .popup-header-title {
      margin-left: 20px;
    }
  }
  .popup-content {
    li {
      height: 30px;
      margin-bottom: 5px;
    }
  }
}
</style>
