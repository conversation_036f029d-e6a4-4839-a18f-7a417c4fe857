<template>
  <div class="left-base">
    <cards title="今日天气">
      <div>sssss</div>
      <div>sssss</div>
      <div>sssss</div>
      <div>sssss</div>
      <div>sssss</div>
      <div>sssss</div>
      <div>sssss</div></cards
    >
    <cards title="数据统计"> ssss</cards>
    <cards title="图表统计">
      <DeviceStatus></DeviceStatus>
    </cards>
  </div>
</template>

<script setup>
import { getCarsList, getResourceChart } from "@/api/command/index";
import cards from "@/views/xtsafety/common/cards/index";
import DeviceStatus from "./panel/DeviceStatus";

onMounted(() => {
  // initResourceList();
  // initCarsList();
});

//=========================资源========================
</script>

<style scoped lang="scss">
.left-base {
  width: 420px;
}
</style>
