import { listBasic } from "@/api/xtcontractormag/BaseInfo/basic";

const useContractorStore = defineStore("contractor", {
  state: () => ({
    contractorList: [], //承包商列表
  }),
  actions: {
    setContractorList(newData) {
      if (newData) {
        this.contractorList = newData;
      } else {
        listBasic({
          pageNum: 1,
          pageSize: 100,
        }).then((response) => {
          this.contractorList = response.rows;
        });
      }
    },
  },
});

export default useContractorStore;
