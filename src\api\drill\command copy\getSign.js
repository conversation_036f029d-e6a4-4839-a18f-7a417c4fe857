import MD5 from "md5";

export function getSign(url) {
  let time = Date.parse(new Date());
  let userId = 1;
  let secretkey = "kQwIOrYvnXmSDkwEiFngrKidMcdrgKor";
  let nonce = Math.random().toString(36).slice(-8);
  let sign = MD5(
    `loginId=${userId}&nonce=${nonce}&timestamp=${time}&key=${secretkey}`
  );
  let tag = url.indexOf("?") == -1 ? "?" : "&";
  const xtUrl = `${url}${tag}loginId=${userId}&nonce=${nonce}&timestamp=${time}&sign=${sign}`;
  return xtUrl
}
// export const houzhui = `&loginId=${userId}&timestamp=${time}&nonce=${nonce}&sign=${sign}`;
// export class XtHttp {
//     constructor(arr) {
//             this.xtHttp = {};
//             this.arr = arr;
//             this.target = {
//                     loginId: '1',
//                     nonce: 'ZySO6wfDnMenAAir6D6k',
//                     timestamp: Date.now(),
//                     key: 'kQwIOrYvnXmSDkwEiFngrKidMcdrgKor',
//                     url: '',
//             };
//     }

//     getXtHttp() {
//             for (const i of this.arr) {
//                     let http = null;
//                     http = axios.create({
//                             baseURL: i.baseUrl, // axios中请求配置有baseURL选项，表示请求URL公共部分
//                             timeout: 10000, // 超时
//                     });
//                     http.interceptors.request.use(
//                             (config) => {
//                                     // console.log(config, 'config - wwwwwwwwwwww');

//                                     this.target.url = config.url;

//                                     this.target.key = i.key;
//                                     config.url = this.getXtSignAndUrl(this.target);
//                                     // console.log(config.url, 'url - wwwwwwwwwwww');
//                                     return config;
//                             },
//                             (error) => {
//                                     console.log(error);
//                                     Promise.reject(error);
//                             }
//                     );

//                     this.xtHttp[i.name] = http;
//             }

//             return this.xtHttp;
//     }

//     getXtSignAndUrl(target) {
//             const { loginId, nonce, timestamp, key, url } = target;
//             const code = `loginId=${loginId}&nonce=${nonce}&timestamp=${timestamp}&key=${key}`;
//             const sign = MD5(code);
//             const tag = url.indexOf('?') == -1 ? '?' : '&';
//             const xtUrl = `${url}${tag}loginId=${loginId}&nonce=${nonce}&timestamp=${timestamp}&sign=${sign}`;
//             return xtUrl;
//             // return { sign, xtUrl };
//     }
// }

// const subApi1 = {
//     name: 'emergency',
//     baseUrl: 'https://park.geovisearth.com/emergency',
//     key: 'kQwIOrYvnXmSDkwEiFngrKidMcdrgKor',
// };
// const allSubApiArr = [subApi0, subApi1, subApi2, subApi4,subApi6];

// const xtHttpObj = new XtHttp(allSubApiArr);
// window.xtRequest = xtHttpObj.getXtHttp();
