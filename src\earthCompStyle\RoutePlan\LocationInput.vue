<template>
  <div class="location-input">
    <span>{{ label }}：</span>
    <el-input
      placeholder="经度"
      style="width: 120px; height: 32px"
      v-model="point.longitude"
      readonly
    />
    <el-input
      placeholder="纬度"
      style="width: 120px; height: 32px"
      v-model="point.latitude"
      readonly
    />
    <div class="pick-button" @click="pickLocation"></div>
  </div>
</template>

<script setup>
const props = defineProps({
  label: {
    type: String,
    required: true,
  },
  point: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["pick"]);

const pickLocation = () => {
  emit("pick");
};
</script>

<style scoped lang="scss">
:deep(.el-input__wrapper) {
  background-color: #ffffff00;
  border: 1px solid #91afce;
  box-shadow: 0 0 0 0 #91afce inset;
  border-radius: 0px !important;
}
:deep(.el-input__inner) {
  color: #ffffff;
}
.location-input {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  margin-bottom: 10px;

  span {
    font-family: 思源黑体;
    font-size: 12px;
    color: #ffffff;
    margin-right: 10px;
  }

  .pick-button {
    cursor: pointer;
    width: 32px;
    height: 32px;
    background-repeat: no-repeat;
    background-image: url("@/assets/xtui/tools/pickimg.png");
  }
}
</style>
