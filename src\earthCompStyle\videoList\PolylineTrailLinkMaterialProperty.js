import * as Cesium from 'cesium';
import measureIcon from "@/assets/xtui/tools/measure.png";

export default class PolylineTrailLinkMaterialProperty {
  constructor(options) {
    this._definitionChanged = new Cesium.Event();
    this._color = options.color;
    this.duration = options.duration;
    this.trailImage = options.trailImage;
    this._time = new Date().getTime();
  }

  get isConstant() {
    return false;
  }

  get definitionChanged() {
    return this._definitionChanged;
  }

  getType(time) {
    return "PolylineTrailLink";
  }

  getValue(time, result) {
    if (!Cesium.defined(result)) {
      result = {};
    }
    result.color = Cesium.Property.getValueOrClonedDefault(
      this._color,
      time,
      Cesium.Color.WHITE,
      result.color
    );
    result.image = this.trailImage || Cesium.Material.PolylineTrailLinkImage;

    if (this.duration) {
      result.time =
        ((new Date().getTime() - this._time) % this.duration) / this.duration;
    }
    viewer.scene.requestRender();
    return result;
  }

  equals(other) {
    return (
      this === other ||
      (other instanceof PolylineTrailLinkMaterialProperty &&
        Cesium.Property.equals(this._color, other._color))
    );
  }
}

Object.defineProperties(PolylineTrailLinkMaterialProperty.prototype, {
  color: Cesium.createPropertyDescriptor("color"),
});

Cesium.Material.PolylineTrailLinkType = "PolylineTrailLink";
Cesium.Material.PolylineTrailLinkImage = measureIcon;
Cesium.Material.PolylineTrailLinkSource = `
    czm_material czm_getMaterial(czm_materialInput materialInput) {
      czm_material material = czm_getDefaultMaterial(materialInput);
      vec2 st = materialInput.st;
      vec4 colorImage = texture2D(image, vec2(fract(st.t - time), st.t));
      vec4 fragColor;
      fragColor.rgb = color.rgb / 1.0;
      fragColor = czm_gammaCorrect(fragColor);
      material.alpha = colorImage.a * color.a;
      material.diffuse = color.rgb;
      material.emission = fragColor.rgb;
      return material;
    }
  `;

Cesium.Material._materialCache.addMaterial(
  Cesium.Material.PolylineTrailLinkType,
  {
    fabric: {
      type: Cesium.Material.PolylineTrailLinkType,
      uniforms: {
        color: new Cesium.Color(1.0, 1.0, 1.0, 1),
        image: Cesium.Material.PolylineTrailLinkImage,
        time: 0,
      },
      source: Cesium.Material.PolylineTrailLinkSource,
    },
    translucent: function (material) {
      return true;
    },
  }
);
