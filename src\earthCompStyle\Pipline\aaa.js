class plplelineTree {
	constructor(vDoms = []) {
		this.domMap = new Map();
		this.showList = [];
		this.initVdragDom(vDoms);
		this.imgUrl = 'http://**********:30018/webapps/simulateDrill';
		this.layerList = [
			{
				id: 'gasLine',
				label: '燃气管网',
				color: [234, 76, 31, 1],
				url:
					this.imgUrl +
					'/geovis/projects/project_3331/piplelineTree/燃气管网_4326.geojson',
			},
			{
				id: 'waterLine',
				label: '供水管网',
				color: [13, 210, 173, 1],
				url:
					this.imgUrl +
					'/geovis/projects/project_3331/piplelineTree/供水管网4326.geojson',
			},
			{
				id: 'heatLine',
				label: '供热管网',
				color: [232, 91, 204, 1],
				url:
					this.imgUrl +
					'/geovis/projects/project_3331/piplelineTree/供热管网_4326.geojson',
			},
			{
				id: 'electriedLine',
				label: '电网',
				color: [109, 210, 235, 1],
				url:
					this.imgUrl +
					'/geovis/projects/project_3331/piplelineTree/电网4326.geojson',
			},
		];
		this.initTreeData();
	}

	initVdragDom(arr) {
		arr.map((v) => {
			this.domMap.set(v.key, v.val);
		});
	}

	//初始化图层+图例树数据
	initTreeData() {
		dataHub.piplelineLayers = [];
		dataHub.legendData = [];
		this.layerList.map((v) => {
			//图层树数据
			dataHub.piplelineLayers.push({
				id: v.id,
				label: v.label,
				url: v.url,
				color: v.color,
				disabled: false,
				leftSlot: true,
				rightSlot: true,
				children: [],
			});
			//图例数据
			dataHub.legendData.push({
				color: `rgba(${v.color[0]},${v.color[1]},${v.color[2]},${v.color[3]})`,
				label: v.label,
			});
		});
	}
	// this.domMap.get("报警提示")('visible',false);
	//添加geoJSON管线图层
	addlayer(e) {
		earthMain.loadGeojson({
			groupName: e.id,
			url: e.url,
			stroke: e.hasOwnProperty('color')
				? this.getCColor(e.color)
				: Cesium.Color.YELLOW,
			// fill: new Cesium.Color(255, 0, 0, 1),
			strokeWidth: 3,
			markerSymbol: '?',
		});
		if (!this.showList.includes(e.id)) {
			this.showList.push(e.id);
		}
	}
	//清除geoJSON管线图层
	clearLayerById(e) {
		window.earthMain.clearGroup(e.id);
		window.gvEarth.dataSources.remove(
			window.gvEarth.dataSources.getByName(e.id)[0]
		);
		let idx = this.showList.indexOf(e.id);
		if (idx > -1) {
			this.showList.splice(idx, 1);
		}
	}

	//初始化图层树选项状态：是否选中
	initNodeCheck() {
		this.showList.map((v) => {
			this.domMap.get('图层树选项状态')(v, true);
		});
	}
	//计算Cesium颜色图层
	getCColor(c) {
		let r = (c[0] / 255).toFixed(3);
		let g = (c[1] / 255).toFixed(3);
		let b = (c[2] / 255).toFixed(3);
		return new Cesium.Color(r, g, b, c[3]);
	}
}
