import request from '@/utils/request'

// 查询监测方法
export function getMonitorMethod(query) {
  return request({
    url: '/mine/monitoringMethods',
    method: 'get',
    params: query
  })
}



export function addMonitorMethod(data) {
  return request({
    url: '/mine/monitoringMethods',
    method: 'post',
    data: data
  })
}


export function updateMonitorMethod(data) {
  return request({
    url: '/mine/monitoringMethods',
    method: 'post',
    data: data
  })
}


export function delMonitorMethod(ids) {
  return request({
    url: '/mine/monitoringMethods',
    method: 'delete',
    data: ids
  })
}
