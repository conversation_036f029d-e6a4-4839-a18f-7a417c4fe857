<!--
 * @Descripttion: santana
 * @LastEditTime: 2022-03-02 15:16:31
-->
<template>
  <div id="mouse-position">
    <div class="lon-lat">
      <div class="mouse-lon">经度：{{ nowLon + "\u3000" }}</div>
      <div class="mouse-lat">纬度：{{ nowLat + "\u3000" }}</div>
      <!-- <div class="mouse-lon">经度：{{ nowLonDFM + "\u3000" }}</div>
      <div class="mouse-lat">纬度：{{ nowLatDFM + "\u3000" }}</div> -->
      <div class="mouse-level">层级：{{ nowLevel + "\u3000" }}</div>
    </div>

    <!-- <div class="viewer-level">
      {{ nowLevel }}
    </div> -->
  </div>
</template>

<script setup>
import { onMounted } from "vue";
const nowLon = ref("");
const nowLat = ref("");
const nowLevel = ref("");
const nowLonDFM = ref("");
const nowLatDFM = ref("");
function poiChange() {
  let canvas = window.viewer.scene.canvas;
  let handler = new Cesium.ScreenSpaceEventHandler(canvas);
  handler.setInputAction(getPoi, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
}
function degreeToDFM(param) {
  let degree;
  let minute;
  let second;
  let minuteTemp;

  let arrDFM = [];
  arrDFM = param.toString().split(".");
  // console.log(arrDFM);

  degree = parseInt(arrDFM[0]);
  minuteTemp = "0." + arrDFM[1];
  minute = parseInt(minuteTemp * 60);

  let minuteTemp2 = parseFloat(minuteTemp * 60);
  let minuteTemp3 = "0." + minuteTemp2.toString().split(".")[1];
  second = parseInt(minuteTemp3 * 60);

  // console.log( degree,'---', minute,'---',second,);
  // console.log( typeof degree,'---', typeof minute,'---',typeof second);

  return degree + "°" + minute + "′" + second + "″";
}

function getPoi(movement) {
  let ellipsoid = window.viewer.scene.globe.ellipsoid;
  let cartesian = window.viewer.camera.pickEllipsoid(movement.endPosition, ellipsoid);
  if (cartesian) {
    //将笛卡尔三维坐标转为地图坐标（弧度）
    let cartographic = window.viewer.scene.globe.ellipsoid.cartesianToCartographic(
      cartesian
    );
    //将地图坐标（弧度）转为十进制的度数
    nowLat.value = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);
    nowLon.value = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
    // nowLatDFM.value = nowLat.value;
    // nowLonDFM.value = nowLon.value;
    nowLatDFM.value = degreeToDFM(nowLat.value);
    nowLonDFM.value = degreeToDFM(nowLon.value);
  }
}
function viewerChange() {
  window.viewer.camera.changed.addEventListener(() => {
    nowLevel.value = getZoom();
  });
}
function getZoom() {
  let ellipsoid = window.viewer.scene.globe.ellipsoid;
  //相机为位置的获取
  let cameraHeight = ellipsoid.cartesianToCartographic(window.viewer.camera.position)
    .height;
  let moveRate = cameraHeight / 1000.0;
  if (moveRate > 10123) {
    return 2;
  } else if (moveRate > 7123) {
    return 3;
  } else if (moveRate > 6321) {
    return 4;
  } else if (moveRate > 5522) {
    return 5;
  } else if (moveRate > 3436) {
    return 6;
  } else if (moveRate > 539) {
    return 7;
  } else if (moveRate > 305) {
    return 8;
  } else if (moveRate > 180) {
    return 9;
  } else if (moveRate > 133) {
    return 10;
  } else if (moveRate > 100) {
    return 11;
  } else if (moveRate > 76.5) {
    return 12;
  } else if (moveRate > 58.2) {
    return 13;
  } else if (moveRate > 23.5) {
    return 14;
  } else if (moveRate > 9.6) {
    return 15;
  } else if (moveRate > 4) {
    return 16;
  } else if (moveRate > 2) {
    return 17;
  } else if (moveRate > 1.7) {
    return 18;
  } else {
    return 18;
  }
}
onMounted(() => {
  emitter.on("viewerLoad", () => {
    poiChange();
    viewerChange();
  });
  setTimeout(() => {}, 5000);
});
</script>

<style lang="scss" scoped>
#mouse-position {
  z-index: 99;
  background-color: rgba(13, 63, 143, 0.361);
  background: linear-gradient(rgba(178, 196, 247, 0.001), rgba(13, 63, 143, 0.561));
  //   filter: blur(12px);
  border-radius: 5px;
  height: 30px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 5px;
  .lon-lat {
    width: 480px;
    display: flex;
    justify-content: center;
    font-size: 18px;
    color: aliceblue;
    text-align: center;

    .mouse-lon {
      width: 180px;
    }

    .mouse-lat {
      width: 180px;
    }
    .mouse-level {
      width: 140px;
    }
  }

  .viewer-level {
    font-size: 18px;
    color: aliceblue;
    // width: 100px;
  }
}
</style>
