<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="auto">
            <el-form-item label="项目名称" prop="projectName">
                <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <!-- <el-form-item label="项目规模" prop="projectScale">
                <el-select v-model="queryParams.projectScale" placeholder="请选择项目规模" clearable style="width: 214px;">
                    <el-option v-for="dict in cbs_project_scale" :key="dict.value" :label="dict.label"
                        :value="dict.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="项目状态" prop="projectStatus">
                <el-select v-model="queryParams.projectStatus" placeholder="请选择项目状态" clearable style="width: 214px;">
                    <el-option v-for="dict in project_status" :key="dict.value" :label="dict.label"
                        :value="dict.value" />
                </el-select>
            </el-form-item> -->
            <el-form-item label="项目阶段" prop="projectStage">
                <el-select v-model="queryParams.projectStage" placeholder="请选择项目阶段" clearable style="width: 214px;">
                    <el-option v-for="dict in cbs_project_stage" :key="dict.value" :label="dict.label"
                        :value="dict.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="项目时间起" prop="projectStartTime">
                <el-date-picker clearable v-model="queryParams.projectStartTime" type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" placeholder="请选择项目时间起">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="项目时间止" prop="projectEndTime">
                <el-date-picker clearable v-model="queryParams.projectEndTime" type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" placeholder="请选择项目时间止">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="承包商名称" prop="contractorBasicId">
                <el-select v-model="queryParams.contractorBasicId" placeholder="请选择承包商" clearable>
                    <el-option v-for="item in contractorList" :key="item.id" :label="item.contractorName"
                        :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['contractor:project:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['contractor:project:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="Upload" @click="handleImport"
                    v-hasPermi="['contractor:project:import']">导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport"
                    v-hasPermi="['contractor:project:export']">导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="importTemplate">下载模板</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="projectList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="项目编号" align="center" prop="projectNumber" show-overflow-tooltip />
            <el-table-column label="项目名称" align="center" prop="projectName" show-overflow-tooltip />
            <el-table-column label="承包商名称" align="center" prop="contractorBasicId" show-overflow-tooltip />
            <el-table-column label="项目规模" align="center" prop="projectScale" show-overflow-tooltip>
                <template #default="scope">
                    <dict-tag :options="cbs_project_scale" :value="scope.row.projectScale" />
                </template>
            </el-table-column>
            <el-table-column label="项目内容" align="center" prop="projectContent" show-overflow-tooltip />
            <el-table-column label="备案机关" align="center" prop="filingAuthority" show-overflow-tooltip />
            <el-table-column label="项目状态" align="center" prop="projectStatus" show-overflow-tooltip>
                <template #default="scope">
                    <dict-tag :options="project_status" :value="scope.row.projectStatus" />
                </template>
            </el-table-column>
            <el-table-column label="项目负责人" align="center" prop="projectManager" show-overflow-tooltip />
            <el-table-column label="项目阶段" align="center" prop="projectStage" show-overflow-tooltip>
                <template #default="scope">
                    <dict-tag :options="cbs_project_stage" :value="scope.row.projectStage" show-overflow-tooltip />
                </template>
            </el-table-column>
            <el-table-column label="项目时间起" align="center" prop="projectStartTime" width="160" show-overflow-tooltip>
                <template #default="scope">
                    <span>{{ parseTime(scope.row.projectStartTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="项目时间止" align="center" prop="projectEndTime" width="160" show-overflow-tooltip>
                <template #default="scope">
                    <span>{{ parseTime(scope.row.projectEndTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="验收评价" align="center" prop="acceptanceEvaluation" show-overflow-tooltip />
            <el-table-column label="合同编号" align="center" prop="contractNumber" show-overflow-tooltip />
            <el-table-column label="入园须知" align="center" prop="admissionInstructionsUrl" show-overflow-tooltip />
            <el-table-column label="负责人电话" align="center" prop="managerPhone" show-overflow-tooltip />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['contractor:project:edit']">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['contractor:project:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改项目信息对话框 -->
        <el-dialog :title="title" v-model="open" width="800px" append-to-body>
            <el-form ref="projectRef" :model="form" :rules="rules" label-width="auto">
                <el-form-item label="项目名称" prop="projectName">
                    <el-input v-model="form.projectName" placeholder="请输入项目名称" />
                </el-form-item>
                <el-form-item label="项目编号" prop="projectNumber">
                    <el-input v-model="form.projectNumber" placeholder="请输入项目编号" />
                </el-form-item>
                <el-form-item label="承包商名称" prop="contractorBasicId">
                    <el-select v-model="form.contractorBasicId" placeholder="请选择承包商" filterable>
                        <el-option v-for="item in contractorList" :key="item.id" :label="item.contractorName"
                            :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="项目规模" prop="projectScale">
                    <el-select v-model="form.projectScale" placeholder="请选择项目规模">
                        <el-option v-for="dict in cbs_project_scale" :key="dict.value" :label="dict.label"
                            :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="备案机关" prop="filingAuthority">
                    <el-input v-model="form.filingAuthority" placeholder="请输入备案机关" />
                </el-form-item>
                <el-form-item label="项目状态" prop="projectStatus">
                    <el-radio-group v-model="form.projectStatus">
                        <el-radio v-for="dict in project_status" :key="dict.value"
                            :label="dict.value">{{dict.label}}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="项目负责人" prop="projectManager">
                    <el-input v-model="form.projectManager" placeholder="请输入项目负责人" />
                </el-form-item>
                <el-form-item label="项目阶段" prop="projectStage">
                    <el-select v-model="form.projectStage" placeholder="请选择项目阶段">
                        <el-option v-for="dict in cbs_project_stage" :key="dict.value" :label="dict.label"
                            :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目时间起" prop="projectStartTime">
                    <el-date-picker clearable v-model="form.projectStartTime" type="datetime"
                        value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" placeholder="请选择项目时间起">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="项目时间止" prop="projectEndTime">
                    <el-date-picker clearable v-model="form.projectEndTime" type="datetime"
                        value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" placeholder="请选择项目时间止">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="验收评价" prop="acceptanceEvaluation">
                    <el-input v-model="form.acceptanceEvaluation" type="textarea" placeholder="请输入内容" />
                </el-form-item>
                <el-form-item label="合同编号" prop="contractNumber">
                    <el-input v-model="form.contractNumber" placeholder="请输入合同编号" />
                </el-form-item>
                <el-form-item label="入园须知" prop="admissionInstructionsUrl">
                    <file-upload v-model="form.admissionInstructionsUrl" />
                </el-form-item>
                <el-form-item label="负责人电话" prop="managerPhone">
                    <el-input v-model="form.managerPhone" placeholder="请输入负责人电话" />
                </el-form-item>
                <el-form-item label="项目内容">
                    <editor v-model="form.projectContent" :min-height="192" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 导入对话框 -->
        <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
            <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
                :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
                :auto-upload="false" drag>
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitFileForm">确 定</el-button>
                    <el-button @click="upload.open = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Project">
import { listBasic } from "@/api/xtcontractormag/BaseInfo/basic";
import { addProject, delProject, getProject, listProject, updateProject } from "@/api/xtcontractormag/BaseInfo/project";
import useUserStore from "@/store/modules/user";
import { deepClone, formatDate } from "@/utils";
import { getToken } from "@/utils/auth";

    const userStore = useUserStore();

    const { proxy } = getCurrentInstance();
        const { project_status, cbs_project_stage, cbs_project_scale } = proxy.useDict('project_status', 'cbs_project_stage', 'cbs_project_scale');

    const projectList = ref([]);
    const open = ref(false);
    const loading = ref(true);
    const showSearch = ref(true);
    const ids = ref([]);
    const itemsArr = ref([]);
    const multiple = ref(true);
    const total = ref(0);
    const title = ref("");

    /*** 用户导入参数 */
    const upload = reactive({
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: window.xtmapConfig.xtBaseUrl + "/contractor/project/importData",
    });

    const data = reactive({
        form: {},
        queryParams: {
            pageNum: 1,
            pageSize: 10,
                        projectNumber: null,
                        projectName: null,
                        projectScale: null,
                        projectContent: null,
                        filingAuthority: null,
                        projectStatus: null,
                        projectManager: null,
                        projectStage: null,
                        projectStartTime: null,
                        projectEndTime: null,
                        acceptanceEvaluation: null,
                        contractNumber: null,
                        admissionInstructionsUrl: null,
                        managerPhone: null,
                        contractorBasicId: null
        },
        rules: {
                        projectScale: [
                        { required: true, message: "项目规模不能为空", trigger: "change" }
                    ],
                        projectContent: [
                        { required: true, message: "项目内容不能为空", trigger: "blur" }
                    ],
                        filingAuthority: [
                        { required: true, message: "备案机关不能为空", trigger: "blur" }
                    ],
                        projectStatus: [
                        { required: true, message: "项目状态不能为空", trigger: "change" }
                    ],
                        projectManager: [
                        { required: true, message: "项目负责人不能为空", trigger: "blur" }
                    ],
                        projectStage: [
                        { required: true, message: "项目阶段不能为空", trigger: "change" }
                    ],
                        projectStartTime: [
                        { required: true, message: "项目时间起不能为空", trigger: "blur" }
                    ],
                        projectEndTime: [
                        { required: true, message: "项目时间止不能为空", trigger: "blur" }
                    ],
                        acceptanceEvaluation: [
                        { required: true, message: "项目验收评价不能为空", trigger: "blur" }
                    ],
                        contractNumber: [
                        { required: true, message: "合同编号不能为空", trigger: "blur" }
                    ],
                        admissionInstructionsUrl: [
                        { required: true, message: "入园须知不能为空", trigger: "blur" }
                    ],
                        managerPhone: [
                        { required: true, message: "负责人电话不能为空", trigger: "blur" }
                    ],
        }
    });
const contractorList = ref([]);
// 获取承包商列表
function getContractorList() {
    listBasic().then(response => {
        contractorList.value = response.rows;
    });
}

    const { queryParams, form, rules } = toRefs(data);

    /** 查询项目信息列表 */
    function getList() {
        loading.value = true;
        listProject(queryParams.value).then(response => {
                projectList.value = response.rows;
            total.value = response.total;
            loading.value = false;
        });
    }

    // 取消按钮
    function cancel() {
        open.value = false;
        reset();
    }

    // 表单重置
    function reset() {
        form.value = {
                        id: null,
                        tenantId: null,
                        createBy: null,
                        createTime: null,
                        updateBy: null,
                        updateTime: null,
                        projectNumber: null,
                        projectName: null,
                        projectScale: null,
                        projectContent: null,
                        filingAuthority: null,
                        projectStatus: null,
                        projectManager: null,
                        projectStage: null,
                        projectStartTime: null,
                        projectEndTime: null,
                        acceptanceEvaluation: null,
                        contractNumber: null,
                        admissionInstructionsUrl: null,
                        managerPhone: null,
                        deleted: null,
                        contractorBasicId: null
        };
        proxy.resetForm("projectRef");
    }

    /** 搜索按钮操作 */
    function handleQuery() {
        queryParams.value.pageNum = 1;
        getList();
    }

    /** 重置按钮操作 */
    function resetQuery() {
        proxy.resetForm("queryRef");
        handleQuery();
    }

    // 多选框选中数据
    function handleSelectionChange(selection) {
        ids.value = selection.map(item => item.id);
        itemsArr.value = deepClone(selection);
        multiple.value = !selection.length;
    }

    /** 导入按钮操作 */
    function handleImport() {
        upload.title = "导入";
        upload.open = true;
    }

    /** 下载模板操作 */
    function importTemplate() {
        proxy.download(
                "contractor/project/export/template",
                {},
                `project_${new Date().getTime()}.xlsx`
        );
    }

    /**文件上传中处理 */
    const handleFileUploadProgress = (event, file, fileList) => {
        upload.isUploading = true;
    };

    /** 文件上传成功处理 */
    const handleFileSuccess = (response, file, fileList) => {
        upload.open = false;
        upload.isUploading = false;
        proxy.$refs["uploadRef"].handleRemove(file);
        proxy.$alert(
                "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
                response.msg +
                "</div>",
                "导入结果",
                { dangerouslyUseHTMLString: true }
        );
        getList();
    };

    /** 提交上传文件 */
    function submitFileForm() {
        upload.url = `${window.xtmapConfig.xtBaseUrl}/contractor/project/importData?userId=${userStore.id}`;
        proxy.$refs["uploadRef"].submit();
    }

    /** 新增按钮操作 */
    function handleAdd() {
        reset();
        open.value = true;
        title.value = "添加项目信息";
    }

    /** 修改按钮操作 */
    function handleUpdate(row) {
        reset();
        const _id = row.id || ids.value
        getProject(_id).then(response => {
            form.value = response.data;
            open.value = true;
            title.value = "修改项目信息";
        });
    }

    /** 提交按钮 */
    function submitForm() {
        proxy.$refs["projectRef"].validate(valid => {
            if (valid) {
                if (form.value.id != null) {
                    const data = {
                        ...form.value,
                        tenantId: userStore.tenantId,
                        updateBy: userStore.name,
                        updateTime: formatDate(new Date()),
                    };
                    updateProject(data).then(response => {
                        proxy.$modal.msgSuccess("修改成功");
                        open.value = false;
                        getList();
                    });
                } else {
                    const data = {
                        ...form.value,
                        tenantId: userStore.tenantId,
                        createBy: userStore.name,
                        createTime: formatDate(new Date()),
                        updateBy: userStore.name,
                        updateTime: formatDate(new Date()),
                        deleted: '0'
                    };
                    addProject(data).then(response => {
                        proxy.$modal.msgSuccess("新增成功");
                        open.value = false;
                        getList();
                    });
                }
            }
        });
    }

    /** 删除按钮操作 */
    function handleDelete(row) {
        const _ids = row.id ? [row.id] : ids.value;
        proxy.$modal.confirm('是否确认删除项目信息编号为"' + _ids + '"的数据项？').then(function() {
            return delProject(_ids);
        }).then(() => {
            getList();
            proxy.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    }

    /** 导出按钮操作 */
    function handleExport() {
        const params = itemsArr.value.length > 0 ? itemsArr.value : undefined;
        proxy.codeDownload(
                'contractor/project/export',
                params,
                `project_${new Date().getTime()}.xlsx`
        );
    }
getContractorList();
    getList();
</script>
