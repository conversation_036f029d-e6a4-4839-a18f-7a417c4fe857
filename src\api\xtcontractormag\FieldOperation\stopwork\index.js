import request from '@/utils/request'

// 查询承包商停工列表
export function listStopwork(query) {
  return request({
    url: '/contractor/stopwork',
    method: 'get',
    params: query
  })
}

// 查询承包商停工详细
export function getStopwork(id) {
  return request({
    url: '/contractor/stopwork/' + id,
    method: 'get'
  })
}

// 新增承包商停工
export function addStopwork(data) {
  return request({
    url: '/contractor/stopwork',
    method: 'post',
    data: data
  })
}

// 修改承包商停工
export function updateStopwork(data) {
  return request({
    url: '/contractor/stopwork',
    method: 'post',
    data: data
  })
}

// 删除承包商停工
export function delStopwork(data) {
  return request({
    url: '/contractor/stopwork/',
    method: 'delete',
    data: data
  })
}
