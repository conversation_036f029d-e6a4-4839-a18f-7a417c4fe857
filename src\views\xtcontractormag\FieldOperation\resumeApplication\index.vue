<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="auto"
    >
      <el-form-item label="停工指令" prop="stopOrderId">
        <el-input
          v-model="queryParams.stopOrderId"
          placeholder="请输入关联停工指令ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="承包商名称" prop="contractorName">
        <el-select v-model="queryParams.contractorName" placeholder="请选择承包商名称">
          <el-option
            v-for="dict in contractorStore.contractorList"
            :key="dict.value"
            :label="dict.contractorName"
            :value="dict.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请人" prop="applyBy">
        <el-input
          v-model="queryParams.applyBy"
          placeholder="请输入申请人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请时间" prop="applyTime">
        <el-date-picker
          clearable
          v-model="queryParams.applyTime"
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
          placeholder="请选择申请时间"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['contractor:resumeApplication:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['contractor:resumeApplication:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['contractor:resumeApplication:import']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['contractor:resumeApplication:export']"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="importTemplate"
          >下载模板</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="resumeApplicationList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column show-overflow-tooltip type="selection" width="55" align="center" />
      <el-table-column
        show-overflow-tooltip
        label="停工指令"
        align="center"
        prop="stopOrderId"
      />
      <el-table-column
        show-overflow-tooltip
        label="承包商名称"
        align="center"
        prop="contractorName"
      />
      <el-table-column
        show-overflow-tooltip
        label="整改措施"
        align="center"
        prop="rectificationMeasure"
      />
      <el-table-column
        show-overflow-tooltip
        label="验证材料"
        align="center"
        prop="verifyMaterialsUrl"
      />
      <el-table-column
        show-overflow-tooltip
        label="记录材料"
        align="center"
        prop="retrainingRecordUrl"
      />
      <el-table-column
        show-overflow-tooltip
        label="申请人"
        align="center"
        prop="applyBy"
      />
      <el-table-column
        show-overflow-tooltip
        label="申请时间"
        align="center"
        prop="applyTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.applyTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        label="审批状态"
        align="center"
        prop="approvalStatus"
      />
      <el-table-column
        show-overflow-tooltip
        label="审批人"
        align="center"
        prop="approvalBy"
      />
      <el-table-column
        show-overflow-tooltip
        label="审批时间"
        align="center"
        prop="approvalTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.approvalTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        label="审批备注"
        align="center"
        prop="approvalRemark"
      />
      <el-table-column
        show-overflow-tooltip
        label="申请人名称"
        align="center"
        prop="applyName"
      />
      <el-table-column
        show-overflow-tooltip
        label="审批人名称"
        align="center"
        prop="approvalName"
      />
      <el-table-column
        label="操作"
        align="center"
        width="180"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['contractor:resumeApplication:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['contractor:resumeApplication:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改承包商复工申请对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="resumeApplicationRef" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="停工指令" prop="stopOrderId">
          <el-input v-model="form.stopOrderId" placeholder="请输入关联停工指令ID" />
        </el-form-item>
        <el-form-item label="承包商名称" prop="contractorName">
          <el-select v-model="form.contractorName" placeholder="请选择承包商名称">
            <el-option
              v-for="dict in contractorStore.contractorList"
              :key="dict.value"
              :label="dict.contractorName"
              :value="dict.id"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="申请人" prop="applyBy">
          <el-input v-model="form.applyBy" placeholder="请输入申请人" />
        </el-form-item>
        <el-form-item label="申请时间" prop="applyTime">
          <el-date-picker
            clearable
            v-model="form.applyTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            placeholder="请选择申请时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批人" prop="approvalBy">
          <el-input v-model="form.approvalBy" placeholder="请输入审批人" />
        </el-form-item>
        <el-form-item label="审批时间" prop="approvalTime">
          <el-date-picker
            clearable
            v-model="form.approvalTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            placeholder="请选择审批时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批备注" prop="approvalRemark">
          <el-input
            v-model="form.approvalRemark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="申请人名称" prop="applyName">
          <el-input v-model="form.applyName" placeholder="请输入申请人名称" />
        </el-form-item>
        <el-form-item label="审批人名称" prop="approvalName">
          <el-input v-model="form.approvalName" placeholder="请输入审批人名称" />
        </el-form-item>
        <el-form-item label="整改措施" prop="rectificationMeasure">
          <el-input
            v-model="form.rectificationMeasure"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="验证材料" prop="verifyMaterialsUrl">
          <el-input
            v-model="form.verifyMaterialsUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="记录材料" prop="retrainingRecordUrl">
          <el-input
            v-model="form.retrainingRecordUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ResumeApplication">
import {
  listResumeApplication,
  getResumeApplication,
  delResumeApplication,
  addResumeApplication,
  updateResumeApplication,
} from "@/api/xtcontractormag/FieldOperation/resumeApplication";
import { deepClone, formatDate } from "@/utils";
import { getToken } from "@/utils/auth";
import useUserStore from "@/store/modules/user";
import useContractorStore from "@/store/modules/contractor";
const userStore = useUserStore();
const contractorStore = useContractorStore();
const { proxy } = getCurrentInstance();

const resumeApplicationList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const itemsArr = ref([]);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层
  open: false,
  // 弹出层标题
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: window.xtmapConfig.xtBaseUrl + "/contractor/resumeApplication/importData",
});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    stopOrderId: null,
    contractorId: null,
    contractorName: null,
    rectificationMeasure: null,
    verifyMaterialsUrl: null,
    retrainingRecordUrl: null,
    applyBy: null,
    applyTime: null,
    approvalStatus: null,
    approvalBy: null,
    approvalTime: null,
    approvalRemark: null,
    applyName: null,
    approvalName: null,
  },
  rules: {
    stopOrderId: [{ required: true, message: "关联停工指令ID不能为空", trigger: "blur" }],
    contractorId: [{ required: true, message: "承包商ID不能为空", trigger: "blur" }],
    contractorName: [{ required: true, message: "承包商名称不能为空", trigger: "blur" }],
    rectificationMeasure: [
      { required: true, message: "整改措施不能为空", trigger: "blur" },
    ],
    verifyMaterialsUrl: [
      { required: true, message: "验证材料不能为空", trigger: "blur" },
    ],
    retrainingRecordUrl: [
      { required: true, message: "人员再培训记录材料路径不能为空", trigger: "blur" },
    ],
    applyBy: [{ required: true, message: "申请人不能为空", trigger: "blur" }],
    applyTime: [{ required: true, message: "申请时间不能为空", trigger: "blur" }],
    approvalStatus: [{ required: true, message: "审批状态不能为空", trigger: "change" }],
    approvalBy: [{ required: true, message: "审批人不能为空", trigger: "blur" }],
    approvalTime: [{ required: true, message: "审批时间不能为空", trigger: "blur" }],
    approvalRemark: [{ required: true, message: "审批备注不能为空", trigger: "blur" }],
    createBy: [{ required: true, message: "创建人不能为空", trigger: "blur" }],
    createTime: [{ required: true, message: "创建时间不能为空", trigger: "blur" }],
    updateBy: [{ required: true, message: "更新人不能为空", trigger: "blur" }],
    updateTime: [{ required: true, message: "更新时间不能为空", trigger: "blur" }],
    deleted: [{ required: true, message: "是否删除不能为空", trigger: "blur" }],
    tenantId: [{ required: true, message: "租户ID不能为空", trigger: "blur" }],
    applyName: [{ required: true, message: "申请人名称不能为空", trigger: "blur" }],
    approvalName: [{ required: true, message: "审批人名称不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询承包商复工申请列表 */
function getList() {
  loading.value = true;
  listResumeApplication(queryParams.value).then((response) => {
    resumeApplicationList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    stopOrderId: null,
    contractorId: null,
    contractorName: null,
    rectificationMeasure: null,
    verifyMaterialsUrl: null,
    retrainingRecordUrl: null,
    applyBy: null,
    applyTime: null,
    approvalStatus: null,
    approvalBy: null,
    approvalTime: null,
    approvalRemark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    deleted: null,
    tenantId: null,
    applyName: null,
    approvalName: null,
  };
  proxy.resetForm("resumeApplicationRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  itemsArr.value = deepClone(selection);
  multiple.value = !selection.length;
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "导入";
  upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download(
    "contractor/resumeApplication/export/template",
    {},
    `resumeApplication_${new Date().getTime()}.xlsx`
  );
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
      response.msg +
      "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  upload.url = `${window.xtmapConfig.xtBaseUrl}/contractor/resumeApplication/importData?userId=${userStore.id}`;
  proxy.$refs["uploadRef"].submit();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加承包商复工申请";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getResumeApplication(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改承包商复工申请";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["resumeApplicationRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        const data = {
          ...form.value,
          tenantId: userStore.tenantId,
          updateBy: userStore.name,
          updateTime: formatDate(new Date()),
        };
        updateResumeApplication(data).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        const data = {
          ...form.value,
          tenantId: userStore.tenantId,
          createBy: userStore.name,
          createTime: formatDate(new Date()),
          updateBy: userStore.name,
          updateTime: formatDate(new Date()),
          deleted: "0",
        };
        addResumeApplication(data).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id ? [row.id] : ids.value;
  proxy.$modal
    .confirm('是否确认删除承包商复工申请编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delResumeApplication(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  const params = itemsArr.value.length > 0 ? itemsArr.value : undefined;
  proxy.codeDownload(
    "contractor/resumeApplication/export",
    params,
    `resumeApplication_${new Date().getTime()}.xlsx`
  );
}

getList();

onMounted(() => {
  contractorStore.setContractorList();
});
</script>
