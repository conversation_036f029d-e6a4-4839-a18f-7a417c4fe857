import request from '@/utils/request'

// 查询部门人员信息
export function getEmployee(query) {
  return request({
    url: '/mine/employee',
    method: 'get',
    params: query
  })
}



export function addEmployee(data) {
  return request({
    url: '/mine/employee',
    method: 'post',
    data: data
  })
}


export function updateEmployee(data) {
  return request({
    url: '/mine/employee',
    method: 'post',
    data: data
  })
}


export function delEmployee(ids) {
  return request({
    url: '/mine/employee',
    method: 'delete',
    data: ids
  })
}
