/*
 * @Descripttion: santana
 * @LastEditTime: 2022-01-12 18:32:55
 */

window.czLayerConfig = {
	czBaseMap: {
		// 图层组名称
		name: '基础地图',
		DB_IMAGE: {
			// 天地图 示例
			/* serviceType: 'WMTS',
			treeName: '地图影像',
			layerName: 'wtms_image',
			url: `https://fxpc.mem.gov.cn/data_preparation/d6d95e8b-a494-4b48-a510-83ae6726524f/f5ebb90e-1f81-4a07-a4f6-1280f7b260ea/wmts?service=wmts&request=GetTile&version=1.0.0&layer=vec&style=default&tileMatrixSet=c&format=tiles&tilematrix=z&tilerow=x&tilecol=y&geokey=32B94C2A2D0CCCE05AB320745F190B70`,

			layer: 'vec',
			style: 'defatlt',
			format: 'tiles',
			maximumLevel: 18,
			tileMatrixSetID: 'c',

			tilingScheme: new Cesium.GeographicTilingScheme(),
			tileMatrixLabels: [
				'1',
				'2',
				'3',
				'4',
				'5',
				'6',
				'7',
				'8',
				'9',
				'10',
				'11',
				'12',
				'13',
				'14',
				'15',
				'16',
				'17',
				'18',
			], */

			tileMatrixSetID: 'EPSG:4326',
			tilingScheme: new Cesium.WebMercatorTilingScheme(),
			tileMatrixSetID: 'EPSG:3857',
			serviceType: 'WMTS',
			treeName: '数据盒子地图影像',
			layerName: 'wtms_image',
			url: 'http://223.84.66.67:8092/tilecache/service/wmts?layer=image&Service=WMTS',
			layer: 'image',
			style: 'defatlt',
			format: 'image/png',
			maximumLevel: 15,
			tilingScheme: new Cesium.GeographicTilingScheme(),
			tileMatrixSetID: 'EPSG:4326',
		},
		DB_LABEL: {
			serviceType: 'WMTS',
			treeName: '222地图影像',
			layerName: 'wtms_image',
			url: `https://fxpc.mem.gov.cn/data_preparation/d6d95e8b-a494-4b48-a510-83ae6726524f/0cb3f5a7-85d5-4864-8227-c0b65f694311/wmts?service=wmts&request=GetTile&version=1.0.0&layer=cia&style=default&tilematrixset=w&format=tiles&tilematrix=z&tilerow=y&tilecol=x&geokey=32B94C2A2D0CCCE05AB320745F190B70`,

			layer: 'cia',
			style: 'defatlt',
			format: 'tiles',
			// tilingScheme: new Cesium.GeographicTilingScheme(),

			maximumLevel: 18,
			// tileMatrixSetID: 'w',
			// tileMatrixLabels: ['1', '2', '3', '4', '5', '6', '7'],

			tilingScheme: new Cesium.WebMercatorTilingScheme(),

			// tileMatrixSetID: 'EPSG:3857',
			// tileMatrixSetID: 'EPSG:4326',
		},
		/* DB_LABEL: {
			serviceType: 'WMTS',
			treeName: '数据盒子地图注记',
			layerName: 'wtms_label',
			url: 'http://223.84.66.67:8092/tilecache/service/wmts?layer=ImageLabel&Service=WMTS',
			layer: 'ImageLabel',
			style: 'defatlt',
			format: 'image/png',
			maximumLevel: 15,
			tilingScheme: new Cesium.GeographicTilingScheme(),
			tileMatrixSetID: 'EPSG:4326',
		}, */
		DB_LABEL_XYZ: {
			serviceType: 'XYZ',
			treeName: '盒子注记xyz版',
			layerName: 'xyz_label',
			url: 'http://223.84.66.67:8092/xyz/ImageLabel/{z}/{x}/{y}.jpg',
		},
		// databox
		DB_IMG_XYZ: {
			serviceType: 'XYZ',
			treeName: '盒子影像xyz版',
			layerName: 'xyz_label',
			url: 'http://223.84.66.67:8092/xyz/image/{z}/{x}/{y}.jpeg',
		},
		DB_DEM: {
			serviceType: 'CZDEM',
			treeName: '盒子terrain地形',
			layerName: 'cz_dem',
			url: 'http://223.84.66.67:8092/terrain/DEM',
		},
	},
	czThematicMap: {
		name: '专题地图',
		DB_LABzEL_XYZ: {
			serviceType: 'WMTS',
			treeName: '天地图矢量图层',
			layerName: 'tdt_wtms_vector',
			url: 'http://t0.tianditu.com/vec_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=068486abb23e217da3bc0e653ade3994',

			layer: 'tdtBasicLayer',
			style: 'defatlt',
			format: 'image/png',
			maximumLevel: 15,
			tilingScheme: new Cesium.WebMercatorTilingScheme(),
			tileMatrixSetID: 'EPSG:3857',
		},
		DB_IMAGE_sea: {
			serviceType: 'WMTS',
			treeName: 'seaicenter',
			layerName: 'wtms_image_sea',
			url: 'http://icenter.geovis.online/tilecache/service/wmts?layer=Global_SeaMap-PNG-3857',
			layer: 'Global_SeaMap-PNG-3857',
			style: 'defatlt',
			format: 'image/png',
			maximumLevel: 15,
			tilingScheme: new Cesium.WebMercatorTilingScheme(),
			// tilingScheme: new Cesium.GeographicTilingScheme(),
			tileMatrixSetID: 'EPSG:3857',
		},
		DB_LABzEL_XYZz: {
			serviceType: 'XYZ',
			treeName: '盒子注记xyz版',
			layerName: 'xyz_label',
			url: 'http://223.84.66.67:8092/xyz/ImageLabel/{z}/{x}/{y}.jpg',
		},
		DB_LABzEL_XYyZ: {
			serviceType: 'XYZ',
			treeName: '盒子注记xyz版',
			layerName: 'xyz_label',
			url: 'http://223.84.66.67:8092/xyz/VectorMap/{z}/{x}/{y}.png',
		},
		DB_LABzEL_XgyZ: {
			serviceType: 'XYZ',
			treeName: '盒子注记xyz版',
			layerName: 'xyz_label',
			url: 'http://223.84.66.67:8092/xyz/ImageLabel/{z}/{x}/{y}.jpg',
		},
		DB_LABzEL_XryZ: {
			serviceType: 'XYZ',
			treeName: '盒子注记xyz版',
			layerName: 'xyz_label',
			url: 'http://223.84.66.67:8092/xyz/ImageLabel/{z}/{x}/{y}.jpg',
		},
		DB_LABzEL_XtyZ: {
			serviceType: 'XYZ',
			treeName: '盒子注记xyz版',
			layerName: 'xyz_label',
			url: 'http://223.84.66.67:8092/xyz/ImageLabel/{z}/{x}/{y}.jpg',
		},
		DB_LABzaEL_XtyZ: {
			sersviceType: 'XYZ',
			treeName: '盒子注记xyz版',
			layerName: 'xyz_label',
			url: 'http://223.84.66.67:8092/xyz/ImageLabel/{z}/{x}/{y}.jpg',
		},
		DB_LABzEsL_XtyZ: {
			serviceType: 'XYZ',
			treeName: '盒子注记xyz版',
			layerName: 'xyz_label',
			url: 'http://223.84.66.67:8092/xyz/ImageLabel/{z}/{x}/{y}.jpg',
		},
		DB_LABzdEL_XtyZ: {
			serviceType: 'XYZ',
			treeName: '盒子注记xyz版',
			layerName: 'xyz_label',
			url: 'http://223.84.66.67:8092/xyz/ImageLabel/{z}/{x}/{y}.jpg',
		},
		DB_LABrrzdEL_XtyZ: {
			serviceType: 'XYZ',
			treeName: '盒子注记xyz版',
			layerName: 'xyz_label',
			url: 'http://223.84.66.67:8092/xyz/ImageLabel/{z}/{x}/{y}.jpg',
		},
		DB_LABzdrEL_XtyZ: {
			serviceType: 'XYZ',
			treeName: '盒子注记xyz版',
			layerName: 'xyz_label',
			url: 'http://223.84.66.67:8092/xyz/ImageLabel/{z}/{x}/{y}.jpg',
		},
		DB_LABzdrrrEL_XtyZ: {
			serviceType: 'XYZ',
			treeName: '盒子注记xyz版',
			layerName: 'xyz_label',
			url: 'http://223.84.66.67:8092/xyz/ImageLabel/{z}/{x}/{y}.jpg',
		},
	},
};

/* trdata: [
	
	{
		id: 2,
		label: '一级 2',
		children: [
			{
				id: 5,
				label: '二级 2-1',
			},
			{
				id: 6,
				label: '二级 2-2',
			},
		],
	} 
], */
