

<script setup>
import Header from "@/views/xtdemo/common/header/index";
import Tools from "@/views/xtdemo/common/tools/index";

import LeftBase from "@/views/xtdemo/execute/leftpanel/LeftBase";
import RightBase from "@/views/xtdemo/execute/rightpanel/RightBase";
import RightInstruct from "@/views/xtdemo/execute/rightpanel/RightInstruct";
import RabbitmqClient from "@/utils/rmq.js";
import { nextTick, ref, reactive, computed, onMounted } from "vue";
import { sendRmqMsg } from "@/api/drill/common/index.js";
import { getMyKey } from "../common/js/tool";
// 获取xttid

const xttid = getMyKey("xttid");
const currentTab = ref("RightBase");
const tabs = {
  RightBase,
  RightInstruct,
};
function czFn() {
  window.viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(
      135.01145621478398,
      22.58828659369774,
      195000.0
    ),
  });
}

// -------------------------连接mq----------------------------
const options = {
  rmqUrl: window.xtmapConfig.rmq.urlB,
  // subUrl: window.xtmapConfig.rmq.subA,
};
const rmqObj = new RabbitmqClient(options);
// callback
function getData(d) {
  // console.log(d + "-------ffff");
  const x = JSON.parse(d);
  window.plotPlugin.clear();
  window.plotPlugin.set(x.msg.map);
}

onMounted(() => {
  emitter.on("viewerLoad", (data) => {
    czFn();
  });

  rmqObj.initMQ().then(() => {
    rmqObj.subMsg(`${window.xtmapConfig.rmq.subA}`, (msg) => {
      console.log(msg.body, "msg  body ssss");
      console.log(JSON.parse(msg.body), "msg  body  parse");
      // console.log(JSON.parse(JSON.stringify(msg.body)) + "-------555555555");
      // window.plotPlugin.set(JSON.parse(msg.body));
      getData(msg.body);
    });
    rmqObj.reconnectMQ(3);
  });
});
</script>

<template>
  <div class="execute">
    <div class="execute-header">
      <Header name="执行席位">
        <template #userInfo>
          <div class="userinfo">
            <!-- <el-button>指挥席位</el-button> -->
          </div>
        </template>
      </Header>
    </div>
    <div class="execute-tools">
      <Tools></Tools>
    </div>
    <div class="left-panel">
      <LeftBase></LeftBase>
    </div>
    <div class="right-panel">
      <!-- <component :is="componentName"></component> -->
      <keep-alive>
        <component :is="tabs[currentTab]"></component>
      </keep-alive>
    </div>
  </div>
</template>

<style scoped lang="scss">
.execute {
  // background-color: rgba(113, 197, 10, 0.917);
  // background: url("@/assets/xtui/layout/leftbg.png");
  // background-size: 100% 100%;
  width: 100%;
  height: 100vh;

  position: relative;
  top: 0;
  left: 0;
  .execute-header {
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 100;
    pointer-events: auto;
  }
  .execute-tools {
    position: absolute;
    right: 500px;
    top: 100px;
    z-index: 99;
    pointer-events: auto;
  }
  .left-panel {
    position: absolute;
    left: 40px;
    top: 0;
    z-index: 99;
    pointer-events: auto;
  }
  .right-panel {
    position: absolute;
    right: 30px;
    top: 0;
    z-index: 99;
    pointer-events: auto;
  }
}
</style>

