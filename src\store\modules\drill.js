import { fa } from "element-plus/es/locale/index.mjs";

export const taskId = defineStore("taskId", {
    state: () => ({
      id: '',
      timeLineData: [],
      normalTime: '15:27:00',
      baseDrillData: {

      },
      basePlanDetail: [

      ],
      ylTime: ''
    }),
    actions: {
      setData(newData) {
        this.id = newData;
      },
      setTimeData(newData) {
        this.timeLineData = newData;
      },
      setNormalTine(val){
        this.normalTime = val;
      },
      setBaseDrillData(newData) {
        this.baseDrillData = newData;
      },
      setBasePlanDetail(newData) {
        this.basePlanDetail = newData;
      },
      setYlTime(val) {
        this.ylTime = val;
      }
    },
});  