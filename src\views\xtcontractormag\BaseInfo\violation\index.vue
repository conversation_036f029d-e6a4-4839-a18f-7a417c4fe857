<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="auto">
                        <el-form-item label="承包商名称" prop="contractorBasicId">
                            <el-input
                                    v-model="queryParams.contractorBasicId"
                                    placeholder="请输入承包商名称"
                                    clearable
                                    @keyup.enter="handleQuery"
                            />
                        </el-form-item>
                        <el-form-item label="地点" prop="location">
                            <el-input
                                    v-model="queryParams.location"
                                    placeholder="请输入地点"
                                    clearable
                                    @keyup.enter="handleQuery"
                            />
                        </el-form-item>
                        <el-form-item label="发生时间" prop="occurrenceTime">
                            <el-date-picker clearable
                                            v-model="queryParams.occurrenceTime"
                                            type="datetime"
                                            value-format="YYYY-MM-DD HH:mm:ss"
                                            style="width: 100%;"
                                            placeholder="请选择发生时间">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="类型" prop="violationType">
                            <el-select v-model="queryParams.violationType" placeholder="请选择类型" clearable style="width: 214px;">
                                <el-option
                                        v-for="dict in cbs_violation_type"
                                        :key="dict.value"
                                        :label="dict.label"
                                        :value="dict.value"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="违章人" prop="violator">
                            <el-input
                                    v-model="queryParams.violator"
                                    placeholder="请输入违章人"
                                    clearable
                                    @keyup.enter="handleQuery"
                            />
                        </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                        type="primary"
                        plain
                        icon="Plus"
                        @click="handleAdd"
                        v-hasPermi="['contractor:violation:add']"
                >新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="danger"
                        plain
                        icon="Delete"
                        :disabled="multiple"
                        @click="handleDelete"
                        v-hasPermi="['contractor:violation:remove']"
                >删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="info"
                        plain
                        icon="Upload"
                        @click="handleImport"
                        v-hasPermi="['contractor:violation:import']"
                >导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="warning"
                        plain
                        icon="Download"
                        @click="handleExport"
                        v-hasPermi="['contractor:violation:export']"
                >导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="warning"
                        plain
                        icon="Download"
                        @click="importTemplate"
                >下载模板</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="violationList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
                    <el-table-column label="承包商名称" align="center" prop="contractorBasicId" />
                    <el-table-column label="地点" align="center" prop="location" />
                    <el-table-column label="发生时间" align="center" prop="occurrenceTime" width="180">
                        <template #default="scope">
                            <span>{{ parseTime(scope.row.occurrenceTime, '{y}-{m}-{d}') }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="类型" align="center" prop="violationType">
                        <template #default="scope">
                                <dict-tag :options="cbs_violation_type" :value="scope.row.violationType"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="处理过程" align="center" prop="handlingProcess" />
                    <el-table-column label="违规描述" align="center" prop="violationDescription" />
                    <el-table-column label="违章人" align="center" prop="violator" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['contractor:violation:edit']">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['contractor:violation:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
                v-show="total>0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
        />

        <!-- 添加或修改承包商违章对话框 -->
        <el-dialog :title="title" v-model="open" width="800px" append-to-body>
            <el-form ref="violationRef" :model="form" :rules="rules" label-width="auto">
                                <el-form-item label="承包商名称" prop="contractorBasicId">
                                    <el-input v-model="form.contractorBasicId" placeholder="请输入承包商名称" />
                                </el-form-item>
                                <el-form-item label="地点" prop="location">
                                    <el-input v-model="form.location" placeholder="请输入地点" />
                                </el-form-item>
                                <el-form-item label="发生时间" prop="occurrenceTime">
                                    <el-date-picker clearable
                                                    v-model="form.occurrenceTime"
                                                    type="datetime"
                                                    value-format="YYYY-MM-DD HH:mm:ss"
                                                    style="width: 100%;"
                                                    placeholder="请选择发生时间">
                                    </el-date-picker>
                                </el-form-item>
                                <el-form-item label="类型" prop="violationType">
                                    <el-select v-model="form.violationType" placeholder="请选择类型">
                                        <el-option
                                                v-for="dict in cbs_violation_type"
                                                :key="dict.value"
                                                :label="dict.label"
                                                :value="dict.value"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="处理过程" prop="handlingProcess">
                                    <el-input v-model="form.handlingProcess" type="textarea" placeholder="请输入内容" />
                                </el-form-item>
                                <el-form-item label="违规描述" prop="violationDescription">
                                    <el-input v-model="form.violationDescription" type="textarea" placeholder="请输入内容" />
                                </el-form-item>
                                <el-form-item label="违章人" prop="violator">
                                    <el-input v-model="form.violator" placeholder="请输入违章人" />
                                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 导入对话框 -->
        <el-dialog
                :title="upload.title"
                v-model="upload.open"
                width="400px"
                append-to-body
        >
            <el-upload
                    ref="uploadRef"
                    :limit="1"
                    accept=".xlsx, .xls"
                    :headers="upload.headers"
                    :action="upload.url"
                    :disabled="upload.isUploading"
                    :on-progress="handleFileUploadProgress"
                    :on-success="handleFileSuccess"
                    :auto-upload="false"
                    drag
            >
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitFileForm">确 定</el-button>
                    <el-button @click="upload.open = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Violation">
import { addViolation, delViolation, getViolation, listViolation, updateViolation } from "@/api/xtcontractormag/BaseInfo/violation";
import useUserStore from "@/store/modules/user";
import { deepClone, formatDate } from "@/utils";
import { getToken } from "@/utils/auth";

    const userStore = useUserStore();

    const { proxy } = getCurrentInstance();
        const { cbs_violation_type } = proxy.useDict('cbs_violation_type');

    const violationList = ref([]);
    const open = ref(false);
    const loading = ref(true);
    const showSearch = ref(true);
    const ids = ref([]);
    const itemsArr = ref([]);
    const multiple = ref(true);
    const total = ref(0);
    const title = ref("");

    /*** 用户导入参数 */
    const upload = reactive({
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: window.xtmapConfig.xtBaseUrl + "/contractor/violation/importData",
    });

    const data = reactive({
        form: {},
        queryParams: {
            pageNum: 1,
            pageSize: 10,
                        contractorBasicId: null,
                        location: null,
                        occurrenceTime: null,
                        violationType: null,
                        handlingProcess: null,
                        violationDescription: null,
                        violator: null
        },
        rules: {
                        location: [
                        { required: true, message: "地点不能为空", trigger: "blur" }
                    ],
                        violationType: [
                        { required: true, message: "类型不能为空", trigger: "change" }
                    ],
                        handlingProcess: [
                        { required: true, message: "处理过程不能为空", trigger: "blur" }
                    ],
                        violationDescription: [
                        { required: true, message: "违规描述不能为空", trigger: "blur" }
                    ],
                        violator: [
                        { required: true, message: "违章人不能为空", trigger: "blur" }
                    ]
        }
    });

    const { queryParams, form, rules } = toRefs(data);

    /** 查询承包商违章列表 */
    function getList() {
        loading.value = true;
        listViolation(queryParams.value).then(response => {
                violationList.value = response.rows;
            total.value = response.total;
            loading.value = false;
        });
    }

    // 取消按钮
    function cancel() {
        open.value = false;
        reset();
    }

    // 表单重置
    function reset() {
        form.value = {
                        id: null,
                        tenantId: null,
                        createBy: null,
                        createTime: null,
                        updateBy: null,
                        updateTime: null,
                        contractorBasicId: null,
                        location: null,
                        occurrenceTime: null,
                        violationType: null,
                        handlingProcess: null,
                        violationDescription: null,
                        deleted: null,
                        violator: null
        };
        proxy.resetForm("violationRef");
    }

    /** 搜索按钮操作 */
    function handleQuery() {
        queryParams.value.pageNum = 1;
        getList();
    }

    /** 重置按钮操作 */
    function resetQuery() {
        proxy.resetForm("queryRef");
        handleQuery();
    }

    // 多选框选中数据
    function handleSelectionChange(selection) {
        ids.value = selection.map(item => item.id);
        itemsArr.value = deepClone(selection);
        multiple.value = !selection.length;
    }

    /** 导入按钮操作 */
    function handleImport() {
        upload.title = "导入";
        upload.open = true;
    }

    /** 下载模板操作 */
    function importTemplate() {
        proxy.download(
                "contractor/violation/export/template",
                {},
                `violation_${new Date().getTime()}.xlsx`
        );
    }

    /**文件上传中处理 */
    const handleFileUploadProgress = (event, file, fileList) => {
        upload.isUploading = true;
    };

    /** 文件上传成功处理 */
    const handleFileSuccess = (response, file, fileList) => {
        upload.open = false;
        upload.isUploading = false;
        proxy.$refs["uploadRef"].handleRemove(file);
        proxy.$alert(
                "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
                response.msg +
                "</div>",
                "导入结果",
                { dangerouslyUseHTMLString: true }
        );
        getList();
    };

    /** 提交上传文件 */
    function submitFileForm() {
        upload.url = `${window.xtmapConfig.xtBaseUrl}/contractor/violation/importData?userId=${userStore.id}`;
        proxy.$refs["uploadRef"].submit();
    }

    /** 新增按钮操作 */
    function handleAdd() {
        reset();
        open.value = true;
        title.value = "添加承包商违章";
    }

    /** 修改按钮操作 */
    function handleUpdate(row) {
        reset();
        const _id = row.id || ids.value
        getViolation(_id).then(response => {
            form.value = response.data;
            open.value = true;
            title.value = "修改承包商违章";
        });
    }

    /** 提交按钮 */
    function submitForm() {
        proxy.$refs["violationRef"].validate(valid => {
            if (valid) {
                if (form.value.id != null) {
                    const data = {
                        ...form.value,
                        tenantId: userStore.tenantId,
                        updateBy: userStore.name,
                        updateTime: formatDate(new Date()),
                    };
                    updateViolation(data).then(response => {
                        proxy.$modal.msgSuccess("修改成功");
                        open.value = false;
                        getList();
                    });
                } else {
                    const data = {
                        ...form.value,
                        tenantId: userStore.tenantId,
                        createBy: userStore.name,
                        createTime: formatDate(new Date()),
                        updateBy: userStore.name,
                        updateTime: formatDate(new Date()),
                        deleted: '0'
                    };
                    addViolation(data).then(response => {
                        proxy.$modal.msgSuccess("新增成功");
                        open.value = false;
                        getList();
                    });
                }
            }
        });
    }

    /** 删除按钮操作 */
    function handleDelete(row) {
        const _ids = row.id ? [row.id] : ids.value;
        proxy.$modal.confirm('是否确认删除承包商违章编号为"' + _ids + '"的数据项？').then(function() {
            return delViolation(_ids);
        }).then(() => {
            getList();
            proxy.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    }

    /** 导出按钮操作 */
    function handleExport() {
        const params = itemsArr.value.length > 0 ? itemsArr.value : undefined;
        proxy.codeDownload(
                'contractor/violation/export',
                params,
                `violation_${new Date().getTime()}.xlsx`
        );
    }

    getList();
</script>
