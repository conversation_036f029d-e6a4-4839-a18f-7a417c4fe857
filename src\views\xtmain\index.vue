<template>
  <div class="xtmain">
    <Header></Header>
    <Tools></Tools>
    <router-view class="router-view"></router-view>
    <XtMap></XtMap>
  </div>
</template>

<script setup>
import Header from "@/views/xtmain/common/header/index";
import Tools from "@/views/xtmain/common/tools/index";
import XtMap from "@/views/xtmap/index";
</script>

<style scoped lang="scss">
.xtmain {
  width: 100%;
  height: 100vh;
  .router-view {
    width: 100vw;
    height: 100vh;
    // pointer-events: auto;
    // background-image: url("@/assets/xtui/layout/allbg.png");
    background-size: 100% 100%;
    z-index: 99;
  }
}
</style>
