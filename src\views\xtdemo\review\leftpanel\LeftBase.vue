<template>
  <div class="left-base"></div>
</template>

<script setup>
import { nextTick, ref, reactive, computed, onMounted } from "vue";
const state = reactive({ count: 0 });
</script>

<style scoped lang="scss">
.left-base {
  background-color: rgba(123, 213, 229, 0);
  height: 100vh;
  width: 350px;

  // background: url("@/assets/xtui/layout/leftbg.png");
  // background-size: 100% 100%;
}
</style>

