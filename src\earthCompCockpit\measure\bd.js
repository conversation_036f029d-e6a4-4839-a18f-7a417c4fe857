function _classCallCheck(a, n) {
  if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function");
}
function _defineProperties(e, r) {
  for (var t = 0; t < r.length; t++) {
    var o = r[t];
    o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o);
  }
}
function _createClass(e, r, t) {
  return r && _defineProperties(e.prototype, r), Object.defineProperty(e, "prototype", {
    writable: !1
  }), e;
}
function _defineProperty(e, r, t) {
  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
    value: t,
    enumerable: !0,
    configurable: !0,
    writable: !0
  }) : e[r] = t, e;
}
function _toPrimitive(t, r) {
  if ("object" != typeof t || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r );
    if ("object" != typeof i) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (String )(t);
}
function _toPropertyKey(t) {
  var i = _toPrimitive(t, "string");
  return "symbol" == typeof i ? i : i + "";
}

/**
 * @description: 获取地球坐标
 * @param {obj} viewer viewer对象
 * @param {e} pxCoor e.position
 * @param {string} resultType 设置返回值的显示格式是经纬度还是xyz(cartesian)
 * @return { obj } 返回的默认格式：{ lon: lon, lat: lat, height: height }
 */

// 外层必须传 viewer和e ,所以没有设置fn默认值
function getCoorFromPx(_ref) {
  var _ref$resultType = _ref.resultType,
    resultType = _ref$resultType === void 0 ? 'jwd' : _ref$resultType,
    viewer = _ref.viewer,
    pxCoor = _ref.pxCoor;
  //判定标识
  var isOnOsgb;
  var isTerrainOpen;
  var cartesian = null;
  var jwdCoor = null;
  var xyzCoor = null;
  var pick = viewer.scene.pick(pxCoor);

  // 应该用drill pick,获取所有通过点击捕获到的实体,如果有模型,就获取模型，
  // 否则，当模型如果处在地面以下一部分时，pick是会直接判定为点击到地面

  // 有无模型
  if (pick && pick.primitive instanceof Cesium.Cesium3DTileFeature || pick && pick.primitive instanceof Cesium.Cesium3DTileset || pick && pick.primitive instanceof Cesium.Model) {
    isOnOsgb = true;
  }
  // 有无地形,Cesium.EllipsoidTerrainProvider是用户不加载地形时，cz默认的空地形,所以t时无 , f时有
  if (viewer.terrainProvider instanceof Cesium.EllipsoidTerrainProvider) {
    isTerrainOpen = false;
  } else {
    isTerrainOpen = true;
  }
  if (isOnOsgb) {
    cartesian = viewer.scene.pickPosition(pxCoor);
  } else {
    if (isTerrainOpen) {
      var ray = viewer.scene.camera.getPickRay(pxCoor);
      if (!ray) return;
      cartesian = viewer.scene.globe.pick(ray, viewer.scene);
    } else {
      cartesian = viewer.scene.camera.pickEllipsoid(pxCoor, viewer.scene.globe.ellipsoid);
    }
  }
  if (cartesian) {
    var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    var lon = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
    var lat = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);
    var height = cartographic.height > 0 ? cartographic.height : 0.1; // 模型高度

    jwdCoor = {
      lon: lon,
      lat: lat,
      height: height
    };
    xyzCoor = cartesian;
    return resultType === 'xyz' ? xyzCoor : jwdCoor;

    /* let position = transformCartesianToWGS84(viewer, cartesian);
    if (position.alt < 0) {
    	coor = transformWGS84ToCartesian(viewer, position, 0.1);
    } */
  }
  return 'null_coor';
}

/* 
const handler = new Cesium.ScreenSpaceEventHandler(viewer.canvas);
return new Promise((resolve, reject) => {
	handler.setInputAction((e) => {
		let posi = e.position;
		let pickedObj = scene.pick(e.position);

		let coor = getCatesian3FromPX(viewer, endPos);

		resolve(coor);
		reject('--err--');
	}, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}); 
 
handler.setInputAction(function (movement) {
    let endPos = movement.endPosition;
    CreateRemindertip(toolTip, endPos, true);
    if (Cesium.defined(polyline)) {
    anchorpoints.pop();
    let cartesian = getCatesian3FromPX(viewer, endPos);
    anchorpoints.push(cartesian);
    }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE); 
*/

var MeasureArea = /*#__PURE__*/function () {
  function MeasureArea(viewer) {
    var _this = this;
    _classCallCheck(this, MeasureArea);
    _defineProperty(this, "leftEvent", function (e) {
      // let earthPosition = this.getPosition(e, 'click');
      var earthPosition = getCoorFromPx({
        viewer: _this.viewer,
        pxCoor: e.position,
        resultType: 'xyz'
      });
      if (Cesium.defined(earthPosition)) {
        var tempPoint;
        if (_this.poiArr.length === 0) {
          _this.floatingPoint = _this.createPoint(earthPosition, '左键绘制，右键结束');
          _this.viewer.entities.add(_this.floatingPoint);
          _this.poiArr.push(earthPosition);
          var dynamicPositions = new Cesium.CallbackProperty(function () {
            return new Cesium.PolygonHierarchy(_this.poiArr);
          }, false);
          _this.activeShape = _this.createArea(dynamicPositions);
        }
        _this.poiArr.push(earthPosition);
        tempPoint = _this.createPoint(earthPosition);
        _this.tempPointArr.entities.add(tempPoint);
      }
    });
    _defineProperty(this, "moveEvent", function (e) {
      if (Cesium.defined(_this.floatingPoint)) {
        // let newPosition = this.getPosition(e, 'move');
        var newPosition = getCoorFromPx({
          viewer: _this.viewer,
          pxCoor: e.endPosition,
          resultType: 'xyz'
        });
        if (Cesium.defined(newPosition)) {
          _this.floatingPoint.position.setValue(newPosition);
          // if (this.poiArr.length > 1) {
          _this.poiArr.pop();
          _this.poiArr.push(newPosition);
          // }
        }
      }
    });
    this.viewer = viewer;
    this.eventHandler = undefined;
    this.poiArr = [];
    this.tempPointArr = new Cesium.CustomDataSource('poidata');
    this.viewer.dataSources.add(this.tempPointArr);
    this.floatingPoint = undefined;
    this.activeShape = undefined;
    this.area = undefined;
    this.labelTxt = undefined;
  }
  return _createClass(MeasureArea, [{
    key: "createEvent",
    value: function createEvent() {
      var _this2 = this;
      return new Promise(function (resolve, reject) {
        _this2.eventHandler = new Cesium.ScreenSpaceEventHandler(_this2.viewer.scene.canvas);
        _this2.eventHandler.setInputAction(_this2.leftEvent, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        _this2.eventHandler.setInputAction(_this2.moveEvent, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        _this2.eventHandler.setInputAction(_this2.rightEvent(resolve, reject), Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      });
    }
  }, {
    key: "rightEvent",
    value: function rightEvent(resolve, reject) {
      var _this3 = this;
      var r = function r(e) {
        if (_this3.poiArr.length == 0) {
          return;
        }
        // let Position = this.getPosition(e, 'click');
        var Position = getCoorFromPx({
          viewer: _this3.viewer,
          pxCoor: e.position,
          resultType: 'xyz'
        });
        if (Cesium.defined(Position)) {
          _this3.poiArr.pop();
          _this3.poiArr.push(Position);
          var endP = _this3.createPoint(Position);
          _this3.tempPointArr.entities.add(endP);
          _this3.getAreaNum();
          _this3.viewer.entities.remove(_this3.floatingPoint);
          _this3.eventHandler.destroy();
          _this3.eventHandler = undefined;
          resolve(_this3.area);
          reject('err----');
        }
      };
      return r;
    }
  }, {
    key: "createPoint",
    value: function createPoint(poi, txt) {
      var point = new Cesium.Entity({
        position: poi,
        point: {
          color: Cesium.Color.YELLOW.withAlpha(0.6),
          pixelSize: 6
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        label: {
          text: txt || '',
          font: 'bold 12px MicroSoft YaHei',
          outlineWidth: 2,
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          verticalOrigin: Cesium.VerticalOrigin.TOP,
          pixelOffset: new Cesium.Cartesian2(15, 0),
          // pixelOffset: new Cesium.Cartesian2(-15, 16), //偏移量
          showBackground: true,
          backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
          backgroundPadding: new Cesium.Cartesian2(6, 3),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      });
      return point;
    }
  }, {
    key: "createArea",
    value: function createArea(poiArr, material) {
      var shape = this.viewer.entities.add({
        polygon: {
          hierarchy: poiArr,
          //positions : Array.<Cartesian3>
          // height: 20, // 多边形相对于椭球面的高度
          material: Cesium.Color.fromCssColorString('rgb(20, 120, 255)').withAlpha(0.33),
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH
          /* material: new Cesium.PolylineDashMaterialProperty({
          	color: Cesium.Color.fromCssColorString('rgb(20, 120, 255)').withAlpha(0.83),
          }), */
          // outline: true,
          // outlineColor: Cesium.Color.YELLOW,
          // outlineWidth: 2.0,
        }
      });
      return shape;
    }
  }, {
    key: "createLabel",
    value: function createLabel(centerPoint, text) {
      return this.viewer.entities.add({
        position: centerPoint,
        label: {
          text: text,
          font: '18px sans-serif',
          fillColor: Cesium.Color.GOLD,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlineWidth: 2,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(-40, 0)
        }
      });
    }
  }, {
    key: "getAreaNum",
    value: function getAreaNum() {
      var positions = this.poiArr;
      var points = positions.map(function (_) {
        var cartographic = Cesium.Cartographic.fromCartesian(_);
        return {
          lon: Cesium.Math.toDegrees(cartographic.longitude),
          lat: Cesium.Math.toDegrees(cartographic.latitude),
          height: cartographic.height
        };
      });
      var res = 0;
      //拆分三角曲面

      for (var i = 0; i < points.length - 2; i++) {
        var j = (i + 1) % points.length;
        var k = (i + 2) % points.length;
        var totalAngle = this.Angle(points[i], points[j], points[k]);
        var dis_temp1 = this.distance(positions[i], positions[j]);
        var dis_temp2 = this.distance(positions[j], positions[k]);
        // let dis_temp1 = distance(points[i], points[j]);
        // let dis_temp2 = distance(points[j], points[k]);
        res += dis_temp1 * dis_temp2 * Math.abs(Math.sin(totalAngle));
      }
      this.area = res.toFixed(2);
      // this.area = (res / 1000 / 1000).toFixed(2);

      var polyPositions = this.activeShape.polygon.hierarchy.getValue(Cesium.JulianDate.now()).positions;
      var polyCenter = Cesium.BoundingSphere.fromPoints(polyPositions).center; //中心点
      polyCenter = Cesium.Ellipsoid.WGS84.scaleToGeodeticSurface(polyCenter);
      this.activeShape.position = polyCenter;
      var aNum = this.area < 10000 ? "".concat(this.area, " M\xB2") : "".concat((this.area / 1000000).toFixed(2), " KM\xB2");
      this.activeShape.label = {
        text: aNum,
        // text: `${this.area * 1000000}平方米`,
        font: '18px sans-serif',
        fillColor: Cesium.Color.GOLD,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 2,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM
        // pixelOffset: new Cesium.Cartesian2(0, 0),
      };
      return (res / 1000 / 1000).toFixed(2);
    }
  }, {
    key: "Angle",
    value: function Angle(p1, p2, p3) {
      var bearing21 = this.Bearing(p2, p1);
      var bearing23 = this.Bearing(p2, p3);
      var angle = bearing21 - bearing23;
      if (angle < 0) {
        angle += 360;
      }
      return angle;
    }
    /*方向*/
  }, {
    key: "Bearing",
    value: function Bearing(from, to) {
      var radiansPerDegree = Math.PI / 180.0; //角度转化为弧度(rad)
      var degreesPerRadian = 180.0 / Math.PI; //弧度转化为角度
      var lat1 = from.lat * radiansPerDegree;
      var lon1 = from.lon * radiansPerDegree;
      var lat2 = to.lat * radiansPerDegree;
      var lon2 = to.lon * radiansPerDegree;
      var angle = -Math.atan2(Math.sin(lon1 - lon2) * Math.cos(lat2), Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon1 - lon2));
      if (angle < 0) {
        angle += Math.PI * 2.0;
      }
      angle = angle * degreesPerRadian; //角度
      return angle;
    }
  }, {
    key: "distance",
    value: function distance(point1, point2) {
      var point1cartographic = Cesium.Cartographic.fromCartesian(point1);
      var point2cartographic = Cesium.Cartographic.fromCartesian(point2);
      /**根据经纬度计算出距离**/
      var geodesic = new Cesium.EllipsoidGeodesic();
      geodesic.setEndPoints(point1cartographic, point2cartographic);
      var s = geodesic.surfaceDistance;
      //返回两点之间的距离
      s = Math.sqrt(Math.pow(s, 2) + Math.pow(point2cartographic.height - point1cartographic.height, 2));
      return s;
    }
  }, {
    key: "reset",
    value: function reset() {
      this.removePolygon();
      this.poiArr = [];
      this.floatingPoint = undefined;
      this.startPoint = undefined;
      this.endPoint = undefined;
      this.activeShape = undefined;
      this.activeSup = undefined;
      this.labelTxt = undefined;
      this.tempPointArr.entities.removeAll();
      if (this.eventHandler) {
        this.eventHandler.destroy();
      }
      this.eventHandler = undefined;
      // this.viewer.dataSources.remove(this.tempPointArr);
    }
  }, {
    key: "resetHandler",
    value: function resetHandler() {
      if (this.eventHandler) {
        this.eventHandler.destroy();
      }
      this.eventHandler = undefined;
    }
  }, {
    key: "removePolygon",
    value: function removePolygon() {
      this.viewer.entities.remove(this.activeShape);
      this.viewer.entities.remove(this.endPoint);
      this.viewer.entities.remove(this.startPoint);
      this.viewer.entities.remove(this.labelTxt);
    }
  }, {
    key: "getPosition",
    value: function getPosition(e, type) {
      var ellipsoid = this.viewer.scene.globe.ellipsoid;
      // let earthPosition = this.viewer.camera.pickEllipsoid(event.position, ellipsoid);
      var earthPosition; //cartesian3
      var tmp;
      if (type === 'move') {
        tmp = e.endPosition;
      } else if (type === 'click') {
        tmp = e.position;
      }
      var pick = this.viewer.scene.pick(tmp);
      var is3D;
      if (pick && pick.primitive instanceof Cesium.Cesium3DTileFeature || pick && pick.primitive instanceof Cesium.Cesium3DTileset || pick && pick.primitive instanceof Cesium.Model) {
        is3D = true;
      }
      if (this.viewer.scene.pickPositionSupported && is3D) {
        earthPosition = this.viewer.scene.pickPosition(tmp);
      } else {
        if (!this.viewer.terrainProvider instanceof Cesium.EllipsoidTerrainProvider) {
          var ray = this.viewer.camera.getPickRay(tmp);
          earthPosition = this.viewer.scene.globe.pick(ray, this.viewer.scene);
        } else {
          earthPosition = this.viewer.camera.pickEllipsoid(tmp, ellipsoid);
        }
      }
      return earthPosition;
    }
  }]);
}();

var MeasureHeight = /*#__PURE__*/function () {
  function MeasureHeight(viewer) {
    var _this = this;
    _classCallCheck(this, MeasureHeight);
    _defineProperty(this, "leftEvent", function (e) {
      var earthPosition = getCoorFromPx({
        viewer: _this.viewer,
        pxCoor: e.position,
        resultType: 'xyz'
      });
      if (Cesium.defined(earthPosition) && _this.poiArr.length === 0) {
        _this.startPoint = _this.createPoint(earthPosition);
        _this.floatingPoint = _this.createFloatPoint(earthPosition, '左键绘制，右键结束');
        _this.viewer.entities.add(_this.floatingPoint);
        var dynamicPositions = new Cesium.CallbackProperty(function () {
          return _this.poiArr;
        }, false);
        _this.activeShape = _this.createLine(dynamicPositions);
        _this.poiArr.push(earthPosition);
        _this.poiArr.push(earthPosition);
      }
    });
    _defineProperty(this, "moveEvent", function (e) {
      if (Cesium.defined(_this.floatingPoint)) {
        var newPosition = getCoorFromPx({
          viewer: _this.viewer,
          pxCoor: e.endPosition,
          resultType: 'xyz'
        });
        if (Cesium.defined(newPosition)) {
          _this.floatingPoint.position.setValue(newPosition);
          _this.poiArr.pop();
          _this.poiArr.push(newPosition);
        }
      }
    });
    this.viewer = viewer;
    // this.options = options;
    this.eventHandler = undefined;
    this.poiArr = [];
    this.floatingPoint = undefined;
    this.startPoint = undefined;
    this.endPoint = undefined;
    this.activeShape = undefined;
    this.activeSup = undefined;
    this.height = undefined;
    this.labelTxt = undefined;
  }
  return _createClass(MeasureHeight, [{
    key: "createEvent",
    value: function createEvent() {
      var _this2 = this;
      return new Promise(function (resolve, reject) {
        _this2.eventHandler = new Cesium.ScreenSpaceEventHandler(_this2.viewer.scene.canvas);
        _this2.eventHandler.setInputAction(_this2.leftEvent, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        _this2.eventHandler.setInputAction(_this2.moveEvent, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        _this2.eventHandler.setInputAction(_this2.rightEvent(resolve, reject), Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      });
    }
  }, {
    key: "rightEvent",
    value: function rightEvent(resolve, reject) {
      var _this3 = this;
      var r = function r(e) {
        if (_this3.poiArr.length == 0) {
          return;
        }
        var Position = getCoorFromPx({
          viewer: _this3.viewer,
          pxCoor: e.position,
          resultType: 'xyz'
        });
        if (Cesium.defined(Position)) {
          _this3.poiArr.pop();
          _this3.poiArr.push(Position);
          _this3.createSup(_this3.poiArr);
          _this3.endPoint = _this3.createPoint(_this3.poiArr.at(-1));
          _this3.viewer.entities.remove(_this3.floatingPoint);
          _this3.eventHandler.destroy();
          _this3.eventHandler = undefined;
          resolve(_this3.height);
          reject('err----');
        }
      };
      return r;
    }
  }, {
    key: "createPoint",
    value: function createPoint(poi, txt) {
      var point = this.viewer.entities.add({
        position: poi,
        point: {
          color: Cesium.Color.YELLOW.withAlpha(0.6),
          pixelSize: 10
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        label: {
          text: txt || '',
          font: '14x MicroSoft YaHei',
          //bold
          outlineWidth: 2,
          fillColor: Cesium.Color.BLACK,
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          pixelOffset: new Cesium.Cartesian2(15, -10),
          //偏移量
          showBackground: true,
          backgroundColor: new Cesium.Color(1, 1, 1, 0.7),
          backgroundPadding: new Cesium.Cartesian2(8, 4)
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        }
      });
      return point;
    }
  }, {
    key: "createFloatPoint",
    value: function createFloatPoint(poi, txt) {
      var point = new Cesium.Entity({
        position: poi,
        point: {
          color: Cesium.Color.YELLOW.withAlpha(0.6),
          pixelSize: 6
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        label: {
          text: txt || '',
          font: 'bold 12px MicroSoft YaHei',
          outlineWidth: 2,
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          verticalOrigin: Cesium.VerticalOrigin.TOP,
          pixelOffset: new Cesium.Cartesian2(15, 0),
          showBackground: true,
          backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
          backgroundPadding: new Cesium.Cartesian2(6, 3),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      });
      return point;
    }
  }, {
    key: "createLine",
    value: function createLine(poiArr, material) {
      var shape = this.viewer.entities.add({
        polyline: {
          positions: poiArr,
          // clampToGround: true,
          width: 3,
          material: material || Cesium.Color.YELLOW
          // material: this.options.lineColor,
        }
      });
      return shape;
    }
  }, {
    key: "createLabel",
    value: function createLabel(centerPoint, text) {
      return this.viewer.entities.add({
        position: centerPoint,
        label: {
          text: text,
          font: '18px sans-serif',
          fillColor: Cesium.Color.GOLD,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlineWidth: 2,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(40, 0),
          showBackground: true,
          backgroundColor: new Cesium.Color(1, 1, 1, 0.7),
          backgroundPadding: new Cesium.Cartesian2(8, 4)

          // scaleByDistance: new Cesium.NearFarScalar(
          // 	150000,
          // 	0.8,
          // 	250000,
          // 	0
          // ),
        }
      });
    }

    // 画辅助线和高度标牌
  }, {
    key: "createSup",
    value: function createSup(poiArr) {
      var cartographicS = Cesium.Cartographic.fromCartesian(poiArr[0]);
      var cartographicE = Cesium.Cartographic.fromCartesian(poiArr[1]);
      this.height = Math.abs(cartographicE.height - cartographicS.height).toFixed(2);
      var mid = Cesium.Cartesian3.fromRadians(cartographicE.longitude, cartographicE.latitude, cartographicS.height
      // cartographicS.longitude,
      // cartographicS.latitude,
      // cartographicE.height
      );
      var dash = new Cesium.PolylineDashMaterialProperty({
        color: Cesium.Color.YELLOW
      });
      this.activeSup = this.createLine([this.poiArr[0], mid, this.poiArr[1]], dash);
      var labelPosition = Cesium.Cartesian3.midpoint(poiArr[1], mid, new Cesium.Cartesian3());
      this.labelTxt = this.createLabel(labelPosition, "".concat(this.height, " M"));
    }
  }, {
    key: "reset",
    value: function reset() {
      this.removeLine();
      this.poiArr = [];
      this.floatingPoint = undefined;
      this.startPoint = undefined;
      this.endPoint = undefined;
      this.activeShape = undefined;
      this.activeSup = undefined;
      this.labelTxt = undefined;
      if (this.eventHandler) {
        this.eventHandler.destroy();
      }
      this.eventHandler = undefined;
    }
  }, {
    key: "resetHandler",
    value: function resetHandler() {
      if (this.eventHandler) {
        this.eventHandler.destroy();
      }
      this.eventHandler = undefined;
    }
  }, {
    key: "removeLine",
    value: function removeLine() {
      this.viewer.entities.remove(this.activeShape);
      this.viewer.entities.remove(this.activeSup);
      this.viewer.entities.remove(this.endPoint);
      this.viewer.entities.remove(this.startPoint);
      this.viewer.entities.remove(this.labelTxt);
    }
  }]);
}();

// 0.1 - 测量距离
// 0.2 - 抽离函数、浮动提示、显示单位
var MeasureLength = /*#__PURE__*/function () {
  function MeasureLength(viewer) {
    var _this = this;
    _classCallCheck(this, MeasureLength);
    _defineProperty(this, "leftEvent", function (e) {
      var earthPosition = getCoorFromPx({
        viewer: _this.viewer,
        pxCoor: e.position,
        resultType: 'xyz'
      });
      if (Cesium.defined(earthPosition)) {
        var tempPoint;
        if (_this.poiArr.length === 0) {
          // this.viewer.dataSources.add(this.tempPointArr);
          // this.floatingPoint = this.createPoint(earthPosition, '');
          _this.floatingPoint = _this.createFloatPoint(earthPosition, '左键绘制，右键结束');
          _this.viewer.entities.add(_this.floatingPoint);
          tempPoint = _this.createPoint(earthPosition);
          _this.poiArr.push(earthPosition);
          var dynamicPositions = new Cesium.CallbackProperty(function () {
            return _this.poiArr;
          }, false);
          _this.activeShape = _this.createPolyline(dynamicPositions);
        } else {
          // 计算段落长度
          _this.tempLength = _this.getSurfaceDistance(_this.poiArr.at(-2), _this.poiArr.at(-1));
          _this.length += parseFloat(_this.tempLength);
          var dt = _this.length > 1000 ? (_this.length / 1000).toFixed(2) + ' KM' : _this.length.toFixed(2) + ' M';
          tempPoint = _this.createPoint(earthPosition, dt);
        }
        _this.poiArr.push(earthPosition);
        _this.tempPointArr.entities.add(tempPoint);
        // this.tipInfo.innerHTML = ' 右键单击结束测量 ';
      }
    });
    _defineProperty(this, "moveEvent", function (e) {
      // this.setTipStyle(e);
      var newPosition = getCoorFromPx({
        viewer: _this.viewer,
        pxCoor: e.endPosition,
        resultType: 'xyz'
      });
      if (Cesium.defined(_this.floatingPoint) && Cesium.defined(newPosition)) {
        _this.floatingPoint.position.setValue(newPosition);
        _this.poiArr.pop();
        _this.poiArr.push(newPosition);
      }
    });
    this.viewer = viewer;
    this.eventHandler = undefined;
    this.poiArr = [];
    // 点集和线是分开绘制的，所以为左键点集合声明一个CustomDataSource
    this.tempPointArr = new Cesium.CustomDataSource('poidata');
    this.viewer.dataSources.add(this.tempPointArr);
    this.floatingPoint = undefined;
    this.activeShape = undefined;
    this.length = 0;
    this.tempLength = 0;
    // this.tipInfo;
  }
  return _createClass(MeasureLength, [{
    key: "createEvent",
    value: function createEvent() {
      var _this2 = this;
      // this.tipInfo = window.document.createElement('div');
      // this.tipInfo.innerHTML = ' 左键单击开始测量 ';
      // this.setTipStyle();
      // window.document.body.appendChild(this.tipInfo);

      return new Promise(function (resolve, reject) {
        _this2.eventHandler = new Cesium.ScreenSpaceEventHandler(_this2.viewer.scene.canvas);
        _this2.eventHandler.setInputAction(_this2.leftEvent, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        _this2.eventHandler.setInputAction(_this2.moveEvent, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        _this2.eventHandler.setInputAction(_this2.rightEvent(resolve, reject), Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      });
    }
  }, {
    key: "rightEvent",
    value: function rightEvent(resolve, reject) {
      var _this3 = this;
      var r = function r(e) {
        if (_this3.poiArr.length == 0) {
          return;
        }
        var Position = getCoorFromPx({
          viewer: _this3.viewer,
          pxCoor: e.position,
          resultType: 'xyz'
        });
        if (Cesium.defined(Position)) {
          _this3.poiArr.pop();
          _this3.poiArr.push(Position);
          _this3.tempLength = _this3.getSurfaceDistance(_this3.poiArr.at(-2), _this3.poiArr.at(-1));
          _this3.length += parseFloat(_this3.tempLength);
          var dt = _this3.length > 1000 ? (_this3.length / 1000).toFixed(2) + ' KM' : _this3.length.toFixed(2) + ' M';
          var endPoint = _this3.createPoint(Position, dt);
          _this3.tempPointArr.entities.add(endPoint);
          _this3.viewer.entities.remove(_this3.floatingPoint);
          _this3.eventHandler.destroy();
          _this3.eventHandler = undefined;
          // window.document.body.removeChild(this.tipInfo);
          resolve(_this3.length);
          reject('err----');
        }
      };
      return r;
    }
  }, {
    key: "createPoint",
    value: function createPoint(poi, txt) {
      var point = new Cesium.Entity({
        position: poi,
        point: {
          color: Cesium.Color.YELLOW.withAlpha(0.6),
          pixelSize: 6
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        label: {
          text: txt || '',
          font: '18px sans-serif',
          fillColor: Cesium.Color.GOLD,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlineWidth: 2,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -20)
        }
      });
      return point;
    }
  }, {
    key: "createFloatPoint",
    value: function createFloatPoint(poi, txt) {
      var point = new Cesium.Entity({
        position: poi,
        point: {
          color: Cesium.Color.YELLOW.withAlpha(0.6),
          pixelSize: 6
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        label: {
          text: txt || '',
          font: 'bold 12px MicroSoft YaHei',
          outlineWidth: 2,
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          verticalOrigin: Cesium.VerticalOrigin.TOP,
          pixelOffset: new Cesium.Cartesian2(15, 0),
          // pixelOffset: new Cesium.Cartesian2(-15, 16), //偏移量
          showBackground: true,
          backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
          backgroundPadding: new Cesium.Cartesian2(6, 3),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      });
      return point;
    }
  }, {
    key: "createPolyline",
    value: function createPolyline(poiArr, material) {
      var shape = this.viewer.entities.add({
        polyline: {
          positions: poiArr,
          clampToGround: true,
          width: 3,
          // material: Cesium.Color.RED,
          material: new Cesium.PolylineDashMaterialProperty({
            color: Cesium.Color.fromCssColorString('rgb(20, 120, 255)').withAlpha(0.83)
          })
        }
      });
      return shape;
    }
  }, {
    key: "setTipStyle",
    value: function setTipStyle() {
      var e = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
      if (e) {
        this.tipInfo.style.display = 'block';
        this.tipInfo.style.position = 'absolute';
        this.tipInfo.style.left = e.endPosition.x + 10 + 'px';
        this.tipInfo.style.top = e.endPosition.y + 'px';
        this.tipInfo.style.zIndex = 99;
      } else {
        this.tipInfo.style.position = 'absolute';
        this.tipInfo.style.display = 'none';
        this.tipInfo.style.fontSize = '14px';
        this.tipInfo.style.backgroundColor = '#c2bdbd94';
        this.tipInfo.style.padding = '3px 6px';
      }
    }
  }, {
    key: "getSurfaceDistance",
    value: function getSurfaceDistance(point1, point2) {
      var point1cartographic = Cesium.Cartographic.fromCartesian(point1);
      var point2cartographic = Cesium.Cartographic.fromCartesian(point2);
      var geodesic = new Cesium.EllipsoidGeodesic();
      geodesic.setEndPoints(point1cartographic, point2cartographic);
      var s = geodesic.surfaceDistance;
      // s = Math.sqrt(Math.pow(s, 2) + Math.pow(point2car.toFixedtographic.height - point1cartographic.height, 2));
      var r = s.toFixed(2);
      return r;
    }
  }, {
    key: "reset",
    value: function reset() {
      this.removeLine();
      this.poiArr = [];
      this.floatingPoint = undefined;
      this.activeShape = undefined;
      this.tempLength = 0;
      this.length = 0;
      if (this.eventHandler) {
        this.eventHandler.destroy();
      }
      this.eventHandler = undefined;
    }
  }, {
    key: "resetHandler",
    value: function resetHandler() {
      if (this.eventHandler) {
        this.eventHandler.destroy();
      }
      this.eventHandler = undefined;
    }
  }, {
    key: "removeLine",
    value: function removeLine() {
      this.viewer.entities.remove(this.activeShape);
      this.tempPointArr.entities.removeAll();
    }
  }]);
}();

export { MeasureArea, MeasureHeight, MeasureLength };
