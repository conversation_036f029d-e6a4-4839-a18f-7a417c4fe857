<template>
  <div class="cz_layers">
    <el-input v-model="filterText" style="width: 220px" placeholder="搜索" />
    <div class="czlc_tree">
      <el-tree
        :data="layerTreeData"
        show-checkbox
        default-expand-all
        node-key="id"
        ref="tree"
        highlight-current
        :props="defaultProps"
        @check-change="handleCheckChange"
        :filter-node-method="filterNode"
      >
      </el-tree>
    </div>

    <PopupSlot
      v-if="isPopShow"
      ref="popupRef"
      id="popup-info-camera"
      @closePopup="closePopup"
      popupTitle="摄像头详情"
    >
      <template #content>
        <div class="video-player">
          <video ref="videoElement" controls autoplay></video>
        </div>
      </template>
    </PopupSlot>
  </div>
</template>

<script setup>
import { getVideoTree, getVideoUrl } from "@/api/tree/index";
import PoiUtils from "./PoiUtils";
import PopupSlot from "./PopupSlot";
import Hls from "hls.js";
const videoElement = ref(null);
const videoSrc = ref(null);
let hls = null;
const filterText = ref("");
const tree = ref("");
const layerTreeData = ref([]);
const defaultProps = {
  children: "children",
  label: "name",
};
const popupItem = ref({});
const isPopShow = ref(false);
watch(filterText, (val) => {
  !tree.value.filter(val);
});

const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.includes(value);
};
const initTree = () => {
  getVideoTree().then((res) => {
    if (res.code === 200) {
      layerTreeData.value = res.data;
    }
  });
};
let poiCollectionArr = reactive([]);
let poiC = reactive({});
let currentPopType = ref(null);
let postRenderingFn = reactive({});
const handleCheckChange = (data, checked, indeterminate) => {
  if (checked && data.children.length === 0) {
    let tempObj = {
      [data.id]: new Cesium.CustomDataSource(data.id),
      onlyID: data.id,
    };
    poiCollectionArr.push(tempObj);
    currentPopType.value = data.id;
    getAndAddPoi(data, tempObj[data.id], data.ptype);
    window.viewer.dataSources.add(tempObj[data.id]);
    poiC = poiCollectionArr.find((item) => item.onlyID === data.id);
    setTimeout(() => {
      PoiUtils.addListener(window.viewer, eventCallback);
    }, 200);
  } else if (!checked && data.children.length === 0) {
    if (isPopShow.value) isPopShow.value = false;
    if (typeof postRenderingFn === "function" && postRenderingFn()) postRenderingFn();
    poiC = poiCollectionArr.find((item) => item.onlyID === data.id);
    poiC[data.id].entities.removeAll();
    window.viewer.dataSources.remove(toRaw(poiC[data.id]), true);
    poiCollectionArr.splice(poiCollectionArr.indexOf(poiC), 1);
  }
};
const closePopup = () => {
  isPopShow.value = false;
  if (hls) {
    hls.destroy();
  }
};
function eventCallback(viewer, pick) {
  if (isPopShow.value) isPopShow.value = false;
  if (typeof postRenderingFn === "function" && postRenderingFn()) postRenderingFn();
  if (pick.id.name === "camerapick" && pick.id.position) {
    popupItem.value = pick.id.properties;
    getHLSURL(popupItem.value);
    isPopShow.value = true;
    nextTick(() => {
      const popDom = window.document.querySelector("#popup-info-camera");
      postRenderingFn = viewer.scene.postRender.addEventListener(() => {
        const screenC = viewer.scene.cartesianToCanvasCoordinates(
          pick.id.position._value
        );
        if (screenC && popDom && popDom.style) {
          popDom.style.left = screenC.x - 10 + "px";
          popDom.style.top = screenC.y - 250 + "px";
        }
      });
    });
  }
}

const getHLSURL = (item) => {
  console.log(item, "item");
  const params = {
    devicePath: item.device_path._value + "/" + item.uid._value,
    disableAudio: true,
    request: "open.video.HLS",
    videoQuality: 1,
  };
  getVideoUrl(params)
    .then((res) => {
      console.log(res, "res");
      if (res && res.code === 200) {
        const videoSrc = res.data.url;
        if (Hls.isSupported()) {
          hls = new Hls();
          hls.loadSource(videoSrc);
          hls.attachMedia(videoElement.value);
        } else if (videoElement.value.canPlayType("application/vnd.apple.mpegurl")) {
          // For Safari, which has built-in HLS support
          videoElement.value.src = videoSrc;
        }
      }
    })
    .catch((err) => {});
};
const getAndAddPoi = (data, collection, type) => {
  console.log(data, collection, type);
  let e_poi = PoiUtils.createPoi({
    center: [data.longitude, data.latitude],
    pickName: "camerapick",
    ptype: "camera",
    labelName: data.name,
    properties: data,
  });
  collection.entities.add(e_poi);

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(data.longitude, data.latitude, 2000.0), // 设置位置
    orientation: {
      heading: Cesium.Math.toRadians(20.0), // 方向
      pitch: Cesium.Math.toRadians(-90.0), // 倾斜角度
      roll: 0,
    },
    duration: 2, // 设置飞行持续时间，默认会根据距离来计算
    // complete: function () {
    // 	// 到达位置后执行的回调函数
    // },
    // cancle: function () {
    // 	// 如果取消飞行则会调用此函数
    // },
    // pitchAdjustHeight: -90, // 如果摄像机飞越高于该值，则调整俯仰俯仰的俯仰角度，并将地球保持在视口中。
    // maximumHeight: 5000, // 相机最大飞行高度
    // flyOverLongitude: 100, // 如果到达目的地有2种方式，设置具体值后会强制选择方向飞过这个经度(这个，很好用)
  });
  // viewer.flyTo(collection);
};
onMounted(() => {
  initTree();
});
onBeforeUnmount(() => {
  if (hls) {
    hls.destroy();
  }
});
</script>

<style scoped lang="scss">
.cz_layers {
  width: 240px;
  height: max-content;
  padding: 8px;
  :deep(.el-input__wrapper) {
    background-color: #ffffff00;
    border: 1px solid rgb(90, 174, 226);
    box-shadow: 0 0 0 0 #91afce inset;
    border-radius: 0px !important;
  }
  :deep(.el-input__inner) {
    color: #ffffff;
  }
}
.czlc_header {
  background: url("@/assets/xtui/tools/dialogopen.png");
  background-size: 100% 100%;
  height: 32px;
  width: 100%;
  margin: 5px 0px 15px 0px;
  padding: 5px 30px;
  font-family: Source Han Sans;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 0px;
  color: aliceblue;
}
.czlc_tree {
  .el-tree {
    padding: 10px 0px;
    width: 100%;
    min-height: 500px;
    color: #cce3ff;
    background: rgba(26, 69, 86, 0);
    max-height: 18rem;
    overflow: auto;
    border: 0px solid rgb(53, 183, 234);
    font-size: 12px;
    font-weight: bold;
  }

  :deep(.el-tree-node__content:hover) {
    background: transparent !important;
    color: rgb(96, 167, 230);
  }

  :deep(.el-tree > .el-tree-node > .el-tree-node__content) {
    background: transparent !important;
    height: 30px;
  }

  :deep(.el-tree > .el-tree-node:focus > .el-tree-node__content) {
    background: transparent !important;
  }

  :deep(.el-tree
      > .el-tree-node
      > .el-tree-node__children
      > .el-tree-node
      > .el-tree-node__content) {
    background: transparent !important;
    height: 30px;
  }

  :deep(.el-tree
      > .el-tree-node
      > .el-tree-node__children
      > .el-tree-node
      > .el-tree-node__children
      > .el-tree-node
      > .el-tree-node__content) {
    background: transparent !important;
    padding: 5px;
    height: 30px;
    :hover {
      background: transparent !important;
      color: rgb(96, 167, 230);
    }
  }
}
.video-player {
  width: 320px; /* 固定宽度 */
  height: 160px; /* 固定高度 */
}
video {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 控制视频填充方式 */
}
</style>
