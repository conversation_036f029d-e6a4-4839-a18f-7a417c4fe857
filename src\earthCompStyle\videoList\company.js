// import * as Cesium from "cesium";
import measureIcon from '@/assets/xtui/tools/dialogopen.png';
// import PolylineTrailLinkMaterialProperty from './PolylineTrailLinkMaterialProperty';

export class Company {
	constructor() {
		ArrowFlowMaterialProperty.init();
	}

	_groupMap = new Map();

	createFence(coorArrs, color) {
		return this.getFence(coorArrs, color);
	}

	getFence(coorArrs, color = 'red') {
		const minimumHeights = new Array(coorArrs.length / 3);
		const maximumHeights = new Array(coorArrs.length / 3);
		// const polylineTrailLinkMaterial = new PolylineTrailLinkMaterialProperty({
		//   color: new Cesium.Color(1.0, 0.0, 0.0, 1.0), // 颜色为红色
		//   duration: 3000, // 持续时间为3000毫秒
		//   trailImage: Cesium.Material.PolylineTrailLinkImage // 轨迹图像的路径
		// });
		minimumHeights.fill(0);
		maximumHeights.fill(150);
		return new Cesium.Entity({
			wall: {
				distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
					0.0,
					100000
				),
				positions: Cesium.Cartesian3.fromDegreesArrayHeights(coorArrs),
				minimumHeights,
				material: new ArrowFlowMaterialProperty(
					Cesium.Color.fromCssColorString(color)
				),
				maximumHeights,
			},
		});
	}

	addGraphic(group, graphic) {
		const dataSource = this._getGroup(group);
		dataSource.entities.add(graphic);
		return dataSource;
	}

	removeGraphic(group, graphic) {
		const dataSource = this._getGroup(group);
		dataSource.entities.remove(graphic);
	}

	clearGroup(group) {
		const dataSource = this._getGroup(group);
		dataSource.entities.removeAll();
	}

	getGroup(groupName = 'default') {
		const group = this._groupMap.get(groupName);
		if (!group) {
			return this._addGroup(groupName);
		}
		return group;
	}

	removeGroup(groupName) {
		const viewer = window.viewer;
		const dataSource = this.getGroup(groupName);
		this._groupMap.delete(groupName);
		viewer.dataSources.remove(dataSource);
	}

	_getGroup(group_) {
		if (!group_) {
			return this.getGroup();
		}
		if (typeof group_ === 'string') {
			return this.getGroup(group_);
		}
		return group_;
	}

	_addGroup(groupName) {
		const viewer = window.viewer;
		const group = this._groupMap.get(groupName);
		if (group) {
			throw new Error(groupName + '该分组已存在！');
		}
		const dataSource = new Cesium.CustomDataSource(groupName);
		viewer.dataSources.add(dataSource);
		this._groupMap.set(groupName, dataSource);
		return dataSource;
	}
}

/**
 * 动态围栏
 */
class ArrowFlowMaterialProperty {
	static type = 'ArrowFlow1';
	axisY = true;
	time = -1;
	_definitionChanged = new Cesium.Event();
	_color = undefined;
	_colorSubscription = undefined;
	color;
	_time = new Date().getTime();
	constructor(color) {
		this.color = color;
	}
	isTranslucent() {
		return true;
	}
	get isConstant() {
		return false;
	}
	get definitionChanged() {
		return this._definitionChanged;
	}

	static init() {
		const shader = `
    czm_material czm_getMaterial(czm_materialInput materialInput) {
      czm_material material = czm_getDefaultMaterial(materialInput);
      vec2 st = materialInput.st;
      float time = fract(czm_frameNumber * speed / 1000.0);
    
      vec4 gvImage;
      if(axisY) {
        if(reverse)
          gvImage = texture2D(image, vec2(fract(float(count) * st.s - time)));
        else
          gvImage = texture2D(image, vec2(fract(float(count) * st.s + time)));
      } else {
        if(reverse)
          gvImage = texture2D(image, vec2(fract(float(count) * st.t - time)));
        else
          gvImage = texture2D(image, vec2(fract(float(count) * st.t + time)));
      }
    
      float perDis = 1.0 / count / 3.0;
      material.alpha = gvImage.a * color.a * smoothstep(.2, 1., distance(st.t * perDis, 1. + perDis));
    
      if(bloom) {
        //泛光
        vec4 fragColor;
        fragColor.rgb = (gvImage.rgb + color.rgb) / 1.0;
        fragColor = czm_gammaCorrect(fragColor);
    
        material.diffuse = gvImage.rgb;
        material.emission = fragColor.rgb;
      } else {
        material.diffuse = max(color.rgb * material.alpha * 1.5, color.rgb);
        material.emission = max(color.rgb * material.alpha * 1.5, color.rgb);
      }
      return material;
    }
    
    `;
		Cesium.Material._materialCache.addMaterial(
			ArrowFlowMaterialProperty.type,
			{
				fabric: {
					type: ArrowFlowMaterialProperty.type,
					uniforms: {
						axisY: false,
						bloom: true,
						color: new Cesium.Color(1, 0, 0, 1),
						image: measureIcon,
						reverse: false,
						count: 2,
						speed: 20,
					},
					source: shader,
				},
				translucent: function (material) {
					return true;
				},
			}
		);
	}

	getType() {
		return ArrowFlowMaterialProperty.type;
	}
	getValue(time, result) {
		if (!Cesium.defined(result)) {
			result = {};
		}
		result.color = this.color;
		result.image = measureIcon;
		result.axisY = false;
		result.count = 2;
		result.bloom = true;
		result.speed = 20;
		result.reverse = false;
		return result;
	}
	equals(other) {
		return (
			this === other ||
			(other instanceof ArrowFlowMaterialProperty &&
				Cesium.Property.equals(this._color, other._color))
		);
	}
}

/**
 * 带方向的墙体
 * @param {*} options.get:true/false
 * @param {*} options.count:数量
 * @param {*} options.freely:vertical/standard
 * @param {*} options.direction:+/-
 */
function _getDirectionWallShader(options) {
	if (options && options.get) {
		var materail =
			'czm_material czm_getMaterial(czm_materialInput materialInput)\n\
          {\n\
          czm_material material = czm_getDefaultMaterial(materialInput);\n\
          vec2 st = materialInput.st;';
		if (options.freely == 'vertical') {
			//（由下到上）
			materail +=
				'vec4 colorImage = texture2D(image, vec2(fract(st.s), fract(float(' +
				options.count +
				')*st.t' +
				options.direction +
				' time)));\n ';
		} else {
			//（逆时针）
			materail +=
				'vec4 colorImage = texture2D(image, vec2(fract(float(' +
				options.count +
				')*st.s ' +
				options.direction +
				' time), fract(st.t)));\n ';
		}
		//泛光
		materail +=
			'vec4 fragColor;\n\
          fragColor.rgb = (colorImage.rgb+color.rgb) / 1.0;\n\
          fragColor = czm_gammaCorrect(fragColor);\n\
          material.diffuse = colorImage.rgb;\n\
          material.alpha = colorImage.a;\n\
          material.emission = fragColor.rgb;\n\
          return material;\n\
          }';
		return materail;
	}
}

export default Company;
