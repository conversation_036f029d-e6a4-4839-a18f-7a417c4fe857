<template>
  <div>
    <div class="resource-tree">
      <div class="tree-header">资源树</div>
      <el-input v-model="filterText" style="width: 210px" placeholder="搜索" />
      <div class="tree-body">
        <el-tree
          :data="treeData"
          show-checkbox
          default-expand-all
          ref="resTree"
          highlight-current
          :props="defaultProps"
          @check-change="handleCheckChange"
          :filter-node-method="filterNode"
        />
      </div>
    </div>
    <div>
      <PopupInfo
        v-if="isPopupVisible"
        ref="popupRef"
        id="popup-info"
        :popInfo="popupInfo"
        :fields="fields"
        :poptitle="poptitle"
        @closePopup="closePopup"
      />
    </div>
  </div>
</template>

<script setup>
// 引入所需的模块和组件
import {
  getResourceTree,
  getResourcesExpert,
  getResourcesTeam,
  getResourcesGoods,
  getResourcesMedical,
  getResourcesRefuge,
  getResourcesStore,
} from "@/api/tree/index";
import PoiUtils from "@/earthCompStyle/resource/PoiUtils.js";
import PopupInfo from "./PopupInfo.vue";
import { command } from "@/store/modules/command";
const store = command();

// 响应式变量定义
const filterText = ref("");
const resTree = ref("");
const popupInfo = ref({});
const fields = ref([]);
const resultInfo = ref();
const resultlabelName = ref();
const poptitle = ref();
const poiCollection = ref({});
const treeData = ref([]);
const defaultProps = {
  children: "children",
  label: "label",
};
const poiCollectionArr = reactive([]);
const isPopupVisible = ref(false);
const currentPopupType = ref({});
let postRenderingFn = reactive({});
watch(filterText, (val) => {
  !resTree.value.filter(val);
});

const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.includes(value);
};
// 初始化资源树数据
const initTreeData = () => {
  getResourceTree().then((res) => {
    if (res.code === 200) {
      treeData.value = res.data;
    }
  });
};

// 处理节点勾选变化
const handleCheckChange = (data, checked) => {
  if (checked && !data.children.length && data.id) {
    // 当节点被选中且没有子节点时，创建一个新的数据源并添加到poiCollectionArr中
    let tempObj = {
      [data.type]: new Cesium.CustomDataSource(data.type),
      uuid: data.type,
    };
    poiCollectionArr.push(tempObj);
    // 根据类型获取资源数据
    getResourceDataByType(data.id, data.type, tempObj[data.type]);
    window.viewer.dataSources.add(tempObj[data.type]);
  } else if (!checked && !data.children.length && data.id) {
    // 当节点取消选中且没有子节点时，移除相关数据源
    // if (currentPopupType.value === data.type) isPopupVisible.value = false;
    isPopupVisible.value = false;
    if (typeof postRenderingFn === "function" && postRenderingFn()) postRenderingFn();
    let poi = poiCollectionArr.find((item) => item.uuid === data.type);
    poi[data.type].entities.removeAll();
    window.viewer.dataSources.remove(toRaw(poi[data.type]), true);
    poiCollectionArr.splice(
      poiCollectionArr.findIndex((item) => item.uuid === data.type),
      1
    );
  }
};

// 根据类别获取资源数据
const getResourceDataByType = async (id, type, collection) => {
  switch (type) {
    case "expert":
      popupInfo.value = await getResourcesExpertById(id);
      fields.value = [
        { label: "专家姓名", prop: "expertName" },
        { label: "专家学历", prop: "degree" },
        { label: "专家专业", prop: "major" },
        { label: "隶属机构", prop: "affiliation" },
        { label: "联系电话", prop: "telephone" },
        { label: "联系邮箱", prop: "email" },
      ];
      poptitle.value = "应急专家";
      nextTick(() => {
        isPopupVisible.value = true;
      });
      break;
    case "team":
      resultInfo.value = await getResourcesTeamById(id);
      resultlabelName.value = resultInfo.value.unitName;
      break;
    case "goods":
      resultInfo.value = await getResourcesGoodsById(id);
      resultlabelName.value = resultInfo.value.materialName;
      break;
    case "refuge":
      resultInfo.value = await getResourcesRefugeById(id);
      resultlabelName.value = resultInfo.value.shelterName;
      break;
    case "medical":
      resultInfo.value = await getResourcesMedicalById(id);
      resultlabelName.value = resultInfo.value.institutionName;
      break;
    case "store":
      resultInfo.value = await getResourcesStoreById(id);
      resultlabelName.value = resultInfo.value.name;
      break;
    default:
      break;
  }

  if (type != "expert") {
    // 创建并添加POI
    let poi = PoiUtils.createPoi(resultInfo.value, type, resultlabelName.value);
    collection.entities.add(poi);
    viewer.flyTo(collection);
  }
};

// 各类资源请求函数

// 专家
const getResourcesExpertById = async (id) => {
  const res = await getResourcesExpert(id);
  if (res.code === 200 && res.data) {
    return res.data;
  }
};
// 队伍
const getResourcesTeamById = async (id) => {
  const res = await getResourcesTeam(id);
  if (res.code === 200 && res.data) {
    return res.data;
  }
};

// 物资
const getResourcesGoodsById = async (id) => {
  const res = await getResourcesGoods(id);
  if (res.code === 200 && res.data) {
    return res.data;
  }
};

// 医疗机构
const getResourcesMedicalById = async (id) => {
  const res = await getResourcesMedical(id);
  if (res.code === 200 && res.data) {
    return res.data;
  }
};

// 避难场所
const getResourcesRefugeById = async (id) => {
  const res = await getResourcesRefuge(id);
  if (res.code === 200 && res.data) {
    return res.data;
  }
};

// 仓库
const getResourcesStoreById = async (id) => {
  const res = await getResourcesStore(id);
  if (res.code === 200 && res.data) {
    return res.data;
  }
};
// 处理点击图标后的回调
const handleEventCallback = (viewer, pick) => {
  if (isPopupVisible.value) isPopupVisible.value = false;
  if (typeof postRenderingFn === "function" && postRenderingFn()) postRenderingFn();
  if (pick.id.name === "reTree" && store.isPanelShow) {
    // 显示弹窗信息
    popupInfo.value = pick.id.properties;
    isPopupVisible.value = true;
    currentPopupType.value = pick.id.properties.type._value;
    setPopkind(currentPopupType.value);
    nextTick(() => {
      const popupElement = window.document.querySelector("#popup-info");
      postRenderingFn = viewer.scene.postRender.addEventListener(() => {
        if (pick && pick.id && pick.id.position && pick.id.position._value) {
          const screenCoordinates = viewer.scene.cartesianToCanvasCoordinates(
            pick.id.position._value
          );
          if (
            screenCoordinates &&
            popupElement &&
            popupElement.style &&
            isPopupVisible.value
          ) {
            popupElement.style.left = `${screenCoordinates.x - 205}px`;
            popupElement.style.top = `${screenCoordinates.y - 460}px`;
          }
        }
      });
    });
  }
};
const setPopkind = (type) => {
  switch (type) {
    case "expert":
      break;
    case "team":
      fields.value = [
        { label: "救援队名称", prop: "unitName" },
        { label: "联系人", prop: "director" },
        { label: "联系电话", prop: "telephone" },
        { label: "隶属机构", prop: "affiliation" },
        { label: "地址", prop: "address" },
      ];
      poptitle.value = "救援队伍";
      break;
    case "goods":
      fields.value = [
        { label: "物资名称", prop: "materialName" },
        { label: "联系人", prop: "director" },
        { label: "联系电话", prop: "telephone" },
        { label: "隶属机构", prop: "affiliation" },
        { label: "所属仓库", prop: "storeName" },
        { label: "地址", prop: "address" },
      ];
      poptitle.value = "应急物资";
      break;
    case "refuge":
      fields.value = [
        { label: "场所名称", prop: "shelterName" },
        { label: "面积(㎡)", prop: "area" },
        { label: "联系人", prop: "director" },
        { label: "联系电话", prop: "telephone" },
        { label: "隶属机构", prop: "affiliation" },
        { label: "地址", prop: "address" },
      ];
      poptitle.value = "避难场所";
      break;
    case "medical":
      fields.value = [
        { label: "机构名称", prop: "institutionName" },
        { label: "联系人", prop: "director" },
        { label: "联系电话", prop: "telephone" },
        { label: "地址", prop: "address" },
      ];
      poptitle.value = "医疗机构";
      break;
    case "store":
      fields.value = [
        { label: "仓库名称", prop: "name" },
        { label: "联系人", prop: "director" },
        { label: "联系电话", prop: "telephone" },
        { label: "隶属机构", prop: "affiliation" },
        { label: "地址", prop: "address" },
      ];
      poptitle.value = "应急仓库";
      break;
    default:
      break;
  }
};
// 关闭弹窗
const closePopup = () => {
  if (typeof postRenderingFn === "function" && postRenderingFn()) postRenderingFn();
  isPopupVisible.value = false;
};

// 生命周期钩子
onMounted(() => {
  initTreeData();
  setTimeout(() => {
    PoiUtils.addListener(window.viewer, handleEventCallback);
  }, 200);
});

onActivated(() => {
  setTimeout(() => {
    PoiUtils.addListener(window.viewer, handleEventCallback);
  }, 200);
});

onDeactivated(() => {
  if (isPopupVisible.value) isPopupVisible.value = false;
  if (typeof postRenderingFn === "function" && postRenderingFn()) postRenderingFn();
  PoiUtils.removehanderListener();
  if (poiCollection.value && poiCollection.value[currentPopupType.value])
    poiCollection.value[currentPopupType.value].entities.removeAll();
  if (poiCollection.value && poiCollection.value[currentPopupType.value])
    window.viewer.dataSources.remove(
      toRaw(poiCollection.value[currentPopupType.value]),
      true
    );
});
</script>

<style scoped lang="scss">
.resource-tree {
  width: 230px;
  min-height: 500px;
  max-height: max-content;
  // background: url("@/assets/xtui/tools/dialogback.png") no-repeat center center;
  // background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  color: antiquewhite;
  background: rgba(8, 76, 124, 0.5);
  // box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%) 2;
  backdrop-filter: blur(10px);
  padding: 6px;
  :deep(.el-input__wrapper) {
    background-color: #ffffff00;
    border: 1px solid rgb(90, 174, 226);
    box-shadow: 0 0 0 0 #91afce inset;
    border-radius: 0px !important;
  }
  :deep(.el-input__inner) {
    color: #ffffff;
  }
  .tree-header {
    background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center center;
    background-size: 100% 100%;
    height: 32px;
    width: 100%;
    padding: 5px 30px;
    margin: 5px 0px;
    // font-family: Source Han Sans;
    // font-family: ysbthzt;
    // font-size: 20px;
    // font-weight: bold;
    // letter-spacing: 0px;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0px;
    color: aliceblue;
    color: antiquewhite;
  }

  .tree-body {
    .el-tree {
      padding: 10px;
      width: 100%;
      min-height: 500px;
      color: #cce3ff;
      background: rgba(26, 69, 86, 0);
      max-height: 18rem;
      overflow: auto;
      border: 0px solid rgb(53, 183, 234);
      font-size: 12px;
      font-weight: bold;
    }

    :deep(.el-tree-node__content:hover) {
      background: transparent !important;
      color: rgb(96, 167, 230);
    }

    :deep(.el-tree > .el-tree-node > .el-tree-node__content) {
      background: transparent !important;
      height: 30px;
    }

    :deep(.el-tree > .el-tree-node:focus > .el-tree-node__content) {
      background: transparent !important;
    }

    :deep(.el-tree
        > .el-tree-node
        > .el-tree-node__children
        > .el-tree-node
        > .el-tree-node__content) {
      background: transparent !important;
      height: 30px;
    }

    :deep(.el-tree
        > .el-tree-node
        > .el-tree-node__children
        > .el-tree-node
        > .el-tree-node__children
        > .el-tree-node
        > .el-tree-node__content) {
      background: transparent !important;
      padding: 15px;
      height: 30px;
      :hover {
        background: transparent !important;
        color: rgb(96, 167, 230);
      }
    }
  }
}
</style>
