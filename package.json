{"name": "singleinit", "version": "3.6.4", "description": "singleinit", "author": "singleinit", "license": "MIT", "type": "module", "scripts": {"start": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Cloud.git"}, "dependencies": {"@easydarwin/easyplayer": "^5.1.1", "@element-plus/icons-vue": "2.3.1", "@turf/turf": "^7.0.0", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.6.1", "axios": "0.27.2", "dayjs": "^1.11.13", "echarts": "5.4.3", "element-plus": "2.4.3", "file-saver": "2.0.5", "fuse.js": "6.6.2", "hls.js": "^1.5.15", "html2canvas": "^1.4.1", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "md5": "^2.3.0", "mitt": "^3.0.1", "nprogress": "0.2.0", "pinia": "2.1.7", "sockjs-client": "^1.6.1", "stompjs": "^2.3.3", "swiper": "^9.4.1", "v-scale-screen": "^2.3.0", "vue": "3.3.9", "vue-carousel-3d": "^1.0.1", "vue-cropper": "1.1.1", "vue-router": "4.2.5", "vue3-carousel-3d": "^1.0.4"}, "devDependencies": {"@vitejs/plugin-vue": "4.5.0", "@vue/compiler-sfc": "3.3.9", "sass": "1.69.5", "unplugin-auto-import": "0.17.1", "unplugin-vue-setup-extend-plus": "1.0.0", "vite": "5.0.4", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}