<template>
  <div class="weather">
    <div class="weather-panel-top">
      <div class="t-item">
        <span style="color: #d7dddf">天气:</span> {{ wVal.weather }}
      </div>
      <div class="t-item">
        <span style="color: #d7dddf">温度:</span> {{ wVal.temperature }}℃
      </div>
    </div>
    <div class="weather-panel-bottom">
      <div class="b-item">
        <div class="windpower"></div>
        <div class="b-item-txt">
          <span>风力</span>
          <span>{{ wVal.windpower }}m/s</span>
        </div>
      </div>
      <div class="b-item">
        <div class="winddirection"></div>
        <div class="b-item-txt">
          <span>风向</span>
          <span>{{ wVal.winddirection }}风</span>
        </div>
      </div>
      <div class="b-item">
        <div class="humidity"></div>
        <div class="b-item-txt">
          <span>湿度</span>
          <span>{{ wVal.humidity }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { nextTick, ref, toRaw, reactive, computed, onMounted } from "vue";

import axios from "axios";
const { proxy } = getCurrentInstance();

const emits = defineEmits(["changeQuery"]); //参数为数组
// emits("getNum", 10);

// ======================天气===============================
const wVal = ref({
  // province: "山西",
  // city: "长子县",
  // adcode: "140428",
  weather: "小雨",
  temperature: "24",
  winddirection: "南",
  windpower: "≤3",
  humidity: "80",
  // reporttime: "2024-08-09 15:33:19",
  // temperature_float: "24.0",
  // humidity_float: "100.0",
});
// const apiKey = "18896ed542df67203d37466738ab1147";
// const city = "140428";
getAmapWeather(window.xtmapConfig.weather.apiKey, window.xtmapConfig.weather.city).then(
  (res) => {
    // console.log(res, "wwwwwwwwwwwww");
    wVal.value = res;
  }
);
async function getAmapWeather(apiKey, city) {
  // 构建天气查询的URL
  const apiUrl = "https://restapi.amap.com/v3/weather/weatherInfo";
  try {
    // 发起 GET 请求
    const response = await axios.get(apiUrl, {
      params: {
        key: apiKey,
        city: encodeURIComponent(city),
      },
    });

    // 解析返回的 JSON 数据
    const responseData = response.data;

    // 判断 API 返回状态
    if (responseData.status === "1") {
      // 处理天气信息
      const weatherData = responseData.lives[0]; // 获取实况天气信息
      return weatherData;
    } else {
      // API 返回错误信息
      throw new Error("API Error: " + responseData.info);
    }
  } catch (error) {
    // 捕获异常，处理请求失败或网络错误
    throw new Error("Request Failed or Network Error: " + error.message);
  }
}
</script>

<style scoped lang="scss">
.weather {
  height: 130px;

  pointer-events: auto;
  padding: 10px 10px 10px 0;

  .weather-panel-top {
    display: flex;
    justify-content: space-evenly;
    margin-top: 10px;
    align-items: center;
    .t-item {
      // font-family: ysbthzt;
      font-family: 思源黑体;
      font-size: 20px;
      font-weight: 600;
      line-height: normal;
      letter-spacing: 0em;
      color: #3cd5ff;
      // width: 70px;
      height: 35px;
      // background-color: rgba(79, 147, 192, 0.814);
    }
  }
  .weather-panel-bottom {
    display: flex;
    justify-content: space-evenly;
    .b-item {
      width: 120px;
      height: 70px;
      // background-color: aquamarine;
      color: #3cd5ff;
      font-size: 16px;
      // margin-left: 15px;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      .b-item-txt {
        font-family: 思源黑体;
        font-size: 16px;
        font-weight: normal;
        line-height: normal;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
        font-size: 18px;
        span:nth-child(1) {
          color: rgba(255, 255, 255, 0.883);
          font-size: 16px;
        }
        span:nth-child(2) {
          font-family: Source Han Sans;
          font-size: 16px;
          font-weight: bold;
          line-height: normal;
          color: #3cd5ff;
        }
      }

      .windpower {
        width: 60px;
        height: 60px;
        // background-color: rosybrown;
        background-image: url("@/assets/xtui/fire/fl.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .winddirection {
        // background-color: rosybrown;
        width: 60px;
        height: 60px;
        background-image: url("@/assets/xtui/fire/fx.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .humidity {
        // background-color: rosybrown;
        width: 60px;
        height: 60px;
        background-image: url("@/assets/xtui/fire/sd.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }
  }
}
</style>
