<template>
  <div class="phone">
    <el-form :inline="true" :model="formInline" class="demo-form-inline">
      <el-form-item label="姓名">
        <el-input
          v-model="formInline.name"
          placeholder="姓名"
          style="width: 180px"
        ></el-input>
      </el-form-item>
      <el-form-item label="所属单位">
        <el-input
          v-model="formInline.company"
          placeholder="所属单位"
          style="width: 180px"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button class="searchButton" @click="onSubmit">查 询</el-button>
        <el-button class="searchButton-reset" @click="reset">重 置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="phoneList" max-height="450" stripe>
      <el-table-column property="name" align="center" label="姓名" />
      <el-table-column property="sex" align="center" label="性别">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.sex == '1'" size="small"
            >女
          </el-tag>
          <el-tag type="success" v-else size="small">男</el-tag>
        </template>
      </el-table-column>
      <el-table-column property="post" align="center" label="职务" />
      <el-table-column property="company" align="center" label="单位" />
      <el-table-column property="telephone" align="center" label="联系方式" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      layout="total, prev, pager, next"
      v-model:page="formInline.pageNum"
      v-model:limit="formInline.pageSize"
      @pagination="initdutyAddress"
    />
  </div>
</template>

<script setup>
const formInline = ref({
  name: "qa",
  company: "ww",
});

const props = defineProps({
  phoneList: {
    type: Array,
    required: false,
    default: [
      {
        name: "qq",
        sex: "1",
        post: "qq",
        company: "qq",
        telephone: "qq",
      },
    ],
  },
});

const total = ref(0);
onMounted(() => {});
</script>

<style scoped lang="scss">
.notice {
  width: 400px;
  height: 200px;
  font-size: 12px;
  color: #ffffff;
  padding: 10px;
  font-family: sans-serif;
  font-weight: 700;
}
.notice-ref {
  width: 380px;
  height: 180px;
  overflow-y: auto;
}
.notice-items {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-bottom: 10px;
}
.notice-item-time {
  width: 90px;
  color: rgb(60, 213, 255);
  text-align: center;
}
.notice-item-des {
  width: 300px;
  word-break: break-all;
  line-height: 17px;
}
</style>
