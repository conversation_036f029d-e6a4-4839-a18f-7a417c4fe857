

<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import { nextTick, ref, reactive, computed, onMounted, toRaw } from "vue";

const heading = ref("");
const pitch = ref("");
const roll = ref("");
const position = ref("");
const lon = ref("");
const lat = ref("");
const height = ref("");

function printCamera() {
  heading.value = viewer.scene.camera.heading;
  pitch.value = viewer.scene.camera.pitch;
  roll.value = viewer.scene.camera.roll;
  position.value = viewer.scene.camera.position;
  const degree = cart3ToDegree(position.value);
  lon.value = degree.lon;
  lat.value = degree.lat;
  height.value = degree.height;
}

function cart3ToDegree(position) {
  let radian = Cesium.Cartographic.fromCartesian(position);
  let lat = parseFloat(Cesium.Math.toDegrees(radian.latitude).toFixed(6));
  let lon = parseFloat(Cesium.Math.toDegrees(radian.longitude).toFixed(6));
  let height = radian.height > 0 ? radian.height : 0.01;
  // let height=(viewer.camera.positionCartographic.height/1000).toFixed(2);
  let degree = { lat, lon, height };
  return degree;
}

function setCamera() {
  // const initialPosition = toRaw(position.value);
  // const initialPosition = position.value;

  // console.log(toRaw(position.value), "toRaw(position.value).value111111");

  /* const initialPosition = new Cesium.Cartesian3(
    toRaw(position.value).x,
    toRaw(position.value).y,
    toRaw(position.value).z
    // position.value.x,
    // position.value.y,
    // position.value.z
  ); */
  // console.log(initialPosition, "initialPosition.value222222");

  const initialPosition = Cesium.Cartesian3.fromDegrees(
    lon.value,
    lat.value,
    height.value
  );
  const orientation = {
    heading: parseFloat(heading.value),
    // 视角
    pitch: parseFloat(pitch.value),
    roll: parseFloat(roll.value),
  };
  /* var initialPosition = new Cesium.Cartesian3(
    -2704200.415014977,
    4702023.785328262,
    3345501.4084866922
  ); // 相机的位置

  var orientation = {
    heading: 6.283185307179586,
    // 视角
    pitch: -1.5686521559334161,
    roll: 0,
  }; */

  viewer.scene.camera.flyTo({
    destination: initialPosition, // 相机的位置
    orientation: orientation,
    duration: 3,
  });
  /* viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      lon.value,
      lat.value,
      height.value
    ), // 相机的位置
    orientation: {
      heading: parseFloat(heading.value),
      // 视角
      pitch: parseFloat(pitch.value),
      roll: parseFloat(roll.value),
    },
    duration: 3,
  }); */
}

onMounted(() => {});
</script>

<template>
  <div class="print">
    <div class="print-input">
      <div>缩放地图视角至指定位置后，点击打印即可显示当前视角</div>
    </div>
    <div class="print-button">
      <el-button type="primary" plain @click="printCamera">打印</el-button>
      <el-button type="primary" plain @click="setCamera">跳转</el-button>
    </div>
    <div class="print-content">
      <ul>
        <li>heading:{{ heading }}</li>
        <li>pitch :{{ pitch }}</li>
        <li>roll :{{ roll }}</li>
        <li>position :{{ position }}</li>
        <li>lon :{{ lon }}</li>
        <li>lat :{{ lat }}</li>
        <li>height :{{ height }}</li>
      </ul>
    </div>
  </div>
</template>

<style scoped lang="scss">
.print {
  width: 320px;
  height: 380px;
  background-color: rgba(100, 235, 237, 0.534);
}
</style>

<!-- https://www.jianshu.com/p/b0203deb43fe -->