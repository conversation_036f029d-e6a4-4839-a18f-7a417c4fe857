<template>
    <div class="app-container">
       <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
 
          <el-form-item label="名称" prop="name">
             <el-input v-model="queryParams.name" placeholder="请输入名称" clearable style="width: 200px"
                @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="所属矿山" prop="mineName">
            <el-select v-model="queryParams.mineName" placeholder="所属矿山" clearable style="width: 200px">
               <el-option v-for="item in options" :key="item.name" :label="item.name" :value="item.name" />
            </el-select>
          </el-form-item>
          <el-form-item>
             <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
             <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
       </el-form>
 
       <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
             <el-button type="primary" plain icon="Plus" @click="handleAdd"
                v-hasPermi="['system:post:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
             <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
                v-hasPermi="['system:post:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
             <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                v-hasPermi="['system:post:remove']">批量删除</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button
               type="warning"
               plain
               icon="Download"
               @click="handleExport"
               v-hasPermi="['system:post:export']"
            >导出</el-button>
         </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
       </el-row>
 
       <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange"  height="670"
      style="max-height: 670px; overflow-y: auto">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="部门名称" align="center" prop="name" />
          <!-- <el-table-column label="部门类型" align="center" prop="departmentType" /> -->
          <el-table-column label="部门类型" align="center" prop="departmentType">
            <template #default="scope">
               <dict-tag :options="gn_department_type" :value="scope.row.departmentType" />
            </template>
         </el-table-column>
          <el-table-column label="所属矿山" align="center" prop="mineId">
            <template #default="{ row }">
               <span>{{ getNameById(row.mineId) }}</span>
            </template> </el-table-column>
          <el-table-column label="负责人" align="center" prop="manager" />
          <el-table-column label="电子邮件" align="center" prop="contactEmail" />
          <el-table-column label="联系电话" align="center" prop="contactPhone" />
          <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width">
             <template #default="scope">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                   v-hasPermi="['system:post:edit']">修改</el-button>
                <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                   v-hasPermi="['system:post:remove']">删除</el-button>
             </template>
          </el-table-column>
       </el-table>
 
       <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
 
       <!-- 添加或修改岗位对话框 -->
       <el-dialog :title="title" v-model="open" width="500px" append-to-body>
          <el-form ref="postRef" :model="form" :rules="rules" label-width="80px">
             <el-form-item label="部门名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入名称" />
             </el-form-item>
             <el-form-item label="所属矿山" prop="mineId">
               <el-select v-model="form.mineId" placeholder="所属矿山" style="width: 240px">
              
                  <el-option v-for="item in options" :key="item.id" :label="item.openPitName"
                  :value="item.id" />
               </el-select>
              
             </el-form-item>
          
             <el-form-item label="部门类型" prop="departmentType">
                     <el-select v-model="form.departmentType" placeholder="请选择">
                        <el-option
                           v-for="dict in gn_department_type"
                           :key="dict.value"
                           :label="dict.label"
                           :value="dict.value"
                        ></el-option>
                     </el-select>
                  </el-form-item>
             <el-form-item label="负责人" prop="manager">
                <el-input v-model="form.manager" placeholder="请输入负责人" />
             </el-form-item>
             <el-form-item label="电子邮件" prop="contactEmail">
                <el-input v-model="form.contactEmail" placeholder="请输入电子邮件" />
             </el-form-item>
             <el-form-item label="联系电话" prop="contactPhone">
                <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
             </el-form-item>
             <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" placeholder="请输入备注" />
             </el-form-item>
          </el-form>
          <template #footer>
             <div class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
             </div>
          </template>
       </el-dialog>
    </div>
 </template>
 
 <script setup name="Post">
 import { getMineDepartment, addMineDepartment, delMineDepartment, updateMineDepartment } from "@/api/xtdata/organization/Department";
 import { getMine } from "@/api/xtdata/organization/Mine";
 const { proxy } = getCurrentInstance();
 const { gn_department_type } = proxy.useDict("gn_department_type");
 
 const postList = ref([]);
 const open = ref(false);
 const loading = ref(true);
 const showSearch = ref(true);
 const ids = ref([]);
 const single = ref(true);
 const multiple = ref(true);
 const total = ref(0);
 const title = ref("");
 const options = ref([]);
 const data = reactive({
    form: {},
    queryParams: {
       pageNum: 1,
       pageSize: 10,
       name: undefined,
       mineName: undefined,
 
    },
    rules: {
      name: [
          { required: true, message: '请输入部门名称', trigger: 'blur' }
        ],
        mineId: [
          { required: true, message: '请输入所属矿山', trigger: 'blur' }
        ],
        departmentType: [
          { required: true, message: '请输入部门类型', trigger: 'blur' }
        ],
        manager: [
          { required: true, message: '请输入负责人', trigger: 'blur' }
        ],
        contactEmail: [
          { required: true, message: '请输入电子邮件', trigger: 'blur' },
          { type: 'email', message: '请输入有效的电子邮件地址', trigger: ['blur', 'change'] }
        ],
        contactPhone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { pattern: /^[1][3,4,5,7,8,9][0-9]{9}$/, message: '请输入有效的电话号码', trigger: ['blur', 'change'] }
        ]
 
    }
 });
 
 const { queryParams, form, rules } = toRefs(data);
 
 /** 查询岗位列表 */
 function getList() {
    loading.value = true;
 
    getMineDepartment(queryParams.value).then(response => {
       postList.value = response.rows;
       total.value = response.total;
       loading.value = false;
    });
 }
 /** 取消按钮 */
 function cancel() {
    open.value = false;
    //  reset();
 }
 /** 表单重置 */
 function reset() {
    form.value = {
       id: undefined,
       name: undefined,
 
 
    };
    proxy.resetForm("postRef");
 }
 function getNameById(id) {
   const item = options.value.find(item => item.id == id);
   return item ? item.openPitName : '未知';
}
function getMineInfo() {

   getMine().then(response => {
 options.value = response.rows;

});
}
 /** 搜索按钮操作 */
 function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
 }
 /** 重置按钮操作 */
 function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
 }
 /** 多选框选中数据 */
 function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
 }
 /** 新增按钮操作 */
 function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加监测大类";
 }
 /** 修改按钮操作 */
 function handleUpdate(row) {
    reset();
    form.value = {};
    form.value = row;
    open.value = true;
    title.value = "修改监督大类";
 }
 /** 提交按钮 */
 function submitForm() {
    proxy.$refs["postRef"].validate(valid => {
       if (valid) {
          if (form.value.id != undefined) {
             updateMineDepartment(form.value).then(response => {
                proxy.$modal.msgSuccess("修改成功");
                open.value = false;
                getList();
             });
          } else {
             addMineDepartment(form.value).then(response => {
                proxy.$modal.msgSuccess("新增成功");
                open.value = false;
                getList();
             });
          }
       }
    });
 }
 /** 删除按钮操作 */
 function handleDelete(row) {
    const postIds = row.id ? [row.id] : ids.value;
    proxy.$modal.confirm('是否确认删除？').then(function () {
       return delMineDepartment(postIds);
    }).then(() => {
       getList();
       proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
 }
 /** 导出按钮操作 */
 function handleExport() {
    proxy.download("system/post/export", {
       ...queryParams.value
    }, `post_${new Date().getTime()}.xlsx`);
 }
 
 getList();
 getMineInfo()
 </script>
 