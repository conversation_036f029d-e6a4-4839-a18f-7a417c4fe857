import request from '@/utils/request'

// 查询监测大类
export function getMiningOperation(query) {
  return request({
    url: '/mine/miningOperation',
    method: 'get',
    params: query
  })
}



export function addMiningOperation(data) {
  return request({
    url: '/mine/miningOperation',
    method: 'post',
    data: data
  })
}


export function updateMiningOperation(data) {
  return request({
    url: '/mine/miningOperation',
    method: 'post',
    data: data
  })
}


export function delMiningOperation(ids) {
  return request({
    url: '/mine/miningOperation',
    method: 'delete',
    data: ids
  })
}
