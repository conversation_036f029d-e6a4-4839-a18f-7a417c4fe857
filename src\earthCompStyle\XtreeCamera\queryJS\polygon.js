import { getCoorFromPx, cart3ToDegree } from './getCoorFromPx.js';
// 有三种type : cztype（哪种entity）,pictype(哪张图),busitype(图是哪一类）
// 					)
class PlotPolygon {
	constructor(viewer) {
		this.viewer = viewer;

		this.plotHandler = null;

		this.myCollection = null;
		this.yourCollection = null;

		// 点集和线是分开绘制的，所以为左键点集合声明一个CustomDataSource
		this.tempPointArr;

		this.floatingPoint = undefined;
		this.poiArr = [];
		this.polygonEntity = undefined; //当前正在绘制的  随着鼠标移动变化的线
		this.init(this.viewer);

		this.jsonCollection = []; // 用json组织数据  json结构需要统一
		// class extend 形式 写个绘制基类  其他的线、面都去extend
	}
	init(viewer) {
		this.myCollection = new Cesium.CustomDataSource('myplotdata');
		this.yourCollection = new Cesium.CustomDataSource('yourplotdata');
		this.viewer.dataSources.add(this.myCollection);
		this.viewer.dataSources.add(this.yourCollection);
		this.tempPointArr = new Cesium.CustomDataSource('poiPolygonData');
		this.viewer.dataSources.add(this.tempPointArr);
	}
	draw(options = { picType: 'p1111', czType: '', busiType: '' }) {
		this.plotHandler = new Cesium.ScreenSpaceEventHandler(
			this.viewer.scene.canvas
		);
		this.plotHandler.setInputAction(
			this.leftEvent(options),
			Cesium.ScreenSpaceEventType.LEFT_CLICK
		);
		this.plotHandler.setInputAction(
			this.moveEvent,
			Cesium.ScreenSpaceEventType.MOUSE_MOVE
		);
		return new Promise((resolve, reject) => {
			this.plotHandler.setInputAction(
				this.rightEvent(resolve, reject, options),
				Cesium.ScreenSpaceEventType.RIGHT_CLICK
			);
		});
	}
	leftEvent(options) {
		const r = (e) => {
			let earthPosition = getCoorFromPx({
				viewer: this.viewer,
				pxCoor: e.position,
				resultType: 'xyz',
			});

			if (Cesium.defined(earthPosition)) {
				let tempPoint;
				if (this.poiArr.length === 0) {
					this.floatingPoint = this.createPoint(
						earthPosition,
						'左键绘制，右键结束'
					);
					this.viewer.entities.add(this.floatingPoint);

					let dynamicPositions = new Cesium.CallbackProperty(() => {
						return new Cesium.PolygonHierarchy(this.poiArr);
					}, false);

					this.polygonEntity = this.createArea(
						dynamicPositions,
						options
					);
					this.myCollection.entities.add(this.polygonEntity);

					this.poiArr.push(earthPosition);
					tempPoint = this.createPoint(earthPosition);
				} else {
					tempPoint = this.createPoint(earthPosition);
				}

				this.tempPointArr.entities.add(tempPoint);
				this.poiArr.push(earthPosition);
			}
		};
		return r;
	}
	moveEvent = (e) => {
		let newPosition = getCoorFromPx({
			viewer: this.viewer,
			pxCoor: e.endPosition,
			resultType: 'xyz',
		});

		if (Cesium.defined(this.floatingPoint) && Cesium.defined(newPosition)) {
			this.floatingPoint.position.setValue(newPosition);
			this.poiArr.pop();
			this.poiArr.push(newPosition);
		}
	};
	rightEvent(resolve, reject, options) {
		const r = (e) => {
			if (this.poiArr.length === 0) {
				return;
			}
			let Position = getCoorFromPx({
				viewer: this.viewer,
				pxCoor: e.position,
				resultType: 'xyz',
			});
			if (Cesium.defined(Position)) {
				this.poiArr.pop();
				this.poiArr.push(Position);

				let endPoint = this.createPoint(Position, '');
				this.tempPointArr.entities.add(endPoint);

				this.viewer.entities.remove(this.floatingPoint);
				this.floatingPoint = null;

				const singleArr = [...this.poiArr];
				const singleEntity = this.createArea(singleArr, options);
				this.myCollection.entities.add(singleEntity);
				this.myCollection.entities.remove(this.polygonEntity);
				this.polygonEntity = null;

				this.tempPointArr.entities.removeAll();
				this.poiArr.length = 0;

				let poiResultArr = cart3ToDegree(singleArr);
				let tempResultArr = [];
				for (const i of poiResultArr) {
					let temp = {};
					temp.longitude = i[0];
					temp.latitude = i[1];
					tempResultArr.push(temp);
				}
				poiResultArr = tempResultArr;
				poiResultArr.push(poiResultArr[0]); //后台接口需要把第一个点闭环

				// console.log(poiResultArr, 'poiResultArr');

				const plotInfo = {
					name: 'xxx',
					position: poiResultArr,
					czType: options.czType,
					busiType: options.busiType,
					picType: options.picType,
					entityOptions: options,
				};

				this.destroyHandler();
				resolve(plotInfo);
				reject('err----');
			}
		};
		return r;
	}
	reDraw(options) {
		const e = this.createArea(options.position, options.entityOptions);
		this.yourCollection.entities.add(e);
	}

	addPolyline(position, options) {
		let material;
		switch (options.picType) {
			case 'l111':
				material = new Cesium.PolylineDashMaterialProperty({
					color: Cesium.Color.fromCssColorString(
						'rgb(20, 120, 255)'
					).withAlpha(0.83),
				});
				break;
			case 'l112':
				material = Cesium.Color.RED;
				break;
			case 'l113':
				material = new Cesium.LineFlowMaterialProperty({
					color: new Cesium.Color.fromCssColorString(
						'rgba(39, 125, 195, 1)'
					),
					speed: 6,
					// speed: 10 * Math.random(),
					percent: 0.75,
					gradient: 0.91,
				});

				break;

			default:
				break;
		}
		return new Cesium.Entity({
			polyline: {
				positions: position,
				clampToGround: true,
				width: 3,
				material: material,
			},
		});
	}
	createArea(position, options) {
		const shape = new Cesium.Entity({
			polygon: {
				hierarchy: position, //positions : Array.<Cartesian3>
				// height: 20, // 多边形相对于椭球面的高度
				material:
					Cesium.Color.fromCssColorString(
						'rgb(20, 120, 255)'
					).withAlpha(0.33),
				// heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
				classificationType: Cesium.ClassificationType.BOTH,
				/* material: new Cesium.PolylineDashMaterialProperty({
					color: Cesium.Color.fromCssColorString('rgb(20, 120, 255)').withAlpha(0.83),
				}), */
				// outline: true,
				// outlineColor: Cesium.Color.YELLOW,
				// outlineWidth: 2.0,
			},
		});
		return shape;
	}
	createPoint(poi, txt) {
		let point = new Cesium.Entity({
			position: poi,
			point: {
				color: Cesium.Color.YELLOW.withAlpha(0.6),
				pixelSize: 6,
				// heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
			},
			label: {
				text: txt || '',
				font: 'bold 12px MicroSoft YaHei',
				outlineWidth: 2,

				horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
				verticalOrigin: Cesium.VerticalOrigin.TOP,
				pixelOffset: new Cesium.Cartesian2(15, 0),
				// pixelOffset: new Cesium.Cartesian2(-15, 16), //偏移量
				showBackground: true,
				backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
				backgroundPadding: new Cesium.Cartesian2(6, 3),
				heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
			},
		});
		return point;
	}
	getPlotData() {
		return this.jsonCollection;
	}
	setPlotData(data) {
		this.reDraw(data);

		/* for (const i of data) {
			this.reDraw(i);
		} */
	}
	clearData() {
		this.myCollection.entities.removeAll();
		this.yourCollection.entities.removeAll();
	}
	destroyHandler() {
		// this.plotHandler.removeInputAction(
		// 	Cesium.ScreenSpaceEventType.LEFT_CLICK
		// );

		if (this.plotHandler) {
			this.plotHandler.destroy();
		}
		this.plotHandler = null;
	}
}

export { PlotPolygon };
