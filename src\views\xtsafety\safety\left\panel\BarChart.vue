<template>
  <div
    :class="className"
    class="record-chart"
    :style="{ height: height, width: width }"
  ></div>
</template>

<script>
import * as echarts from "echarts";
// require("echarts/theme/macarons"); // echarts theme
import resize from "../../right/mixins/resize";
import { getPlanNum } from "@/api/cockpit/index";
const animationDuration = 1000;

/* //查询演练和计划数量
export function getPlanNum() {
  let url = "/bus/record/recordChart";
  return request({
    url,
    method: "get",
  });
} */

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "90%",
    },
    height: {
      type: String,
      default: "220px",
    },
    /* chartBar: {
      type: Object,
      required: true,
    }, */
  },
  data() {
    return {
      chart: null,
      // chartBar: {},
      chartBar: {},
    };
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.initChart();
    // });
    this.initData();
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  watch: {
    chartBar: {
      deep: true,
      handler(newVal, oldVal) {
        // this.chartData=newVal
        this.initChart(newVal);
      },
    },
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el);
      this.chart.setOption({
        tooltip: {
          trigger: "axis",
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
        },
        legend: {
          data: ["计划", "记录"],
          left: "right",
          textStyle: {
            color: "white",
          },
        },

        grid: {
          left: 30,
          right: 40,
          bottom: 20,
          top: 30,
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            name: "月份",

            data: this.chartBar.time,
            axisTick: {
              alignWithLabel: true,
            },
            axisLine: {
              lineStyle: {
                color: "white", // 修改x轴颜色为红色
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "次数",
            axisTick: {
              show: false,
            },
            axisLine: {
              lineStyle: {
                color: "white", // 修改x轴颜色为红色
              },
            },
          },
        ],
        series: [
          {
            name: "计划",
            type: "bar",

            data: this.chartBar.plan,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#EDD67A" },

                { offset: 1, color: "#F68D41" },
              ]),
            },
            animationDuration,
          },
          {
            name: "记录",
            type: "bar",

            data: this.chartBar.record,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#82EEFF" },

                { offset: 1, color: "#5E8EDA" },
              ]),
            },
            animationDuration,
          },
        ],
      });
    },
    initData() {
      getPlanNum().then((res) => {
        if (res.data !== null) {
          this.chartBar.time = res.data.items.map((item) => item.x);

          this.chartBar.record = res.data.items.map((item) => item.y1);

          this.chartBar.plan = res.data.items.map((item) => item.y2);
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.record-chart {
  margin-top: 20px;
}
</style>
