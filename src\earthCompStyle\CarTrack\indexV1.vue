<template>
  <div class="person-posi">
    <div class="person-posi-menu">
      <div class=" ">
        <div>人员定位 --- 微信小程序获取高德地图位置</div>
        <br />
        <!-- 情况一：返回一个完整路线（回放），直接数据给数组赋值即可 -->
        <!-- 情况二：返回实时数据路线 ，-->

        <ul>
          <li>
            <el-button @click="drawEntity(personsData[0])">加载人员1</el-button>
            <el-button @click="moveEntity(personsData[0])">移动</el-button>
            <el-button @click="deleteEntity(personsData[0])">移除</el-button>
          </li>
          <el-divider />

          <li>
            <el-button @click="drawEntity(personsData[1])">加载人员2</el-button>
            <el-button @click="moveEntity(personsData[1])">移动</el-button>
            <el-button @click="deleteEntity(personsData[1])">移除</el-button>
          </li>
          <el-divider />
          <!-- <el-button @click="testLine">测试</el-button> -->
        </ul>
      </div>
      <el-divider />
    </div>
  </div>
</template>


<script setup>
import { el } from "element-plus/es/locales.mjs";
import { nextTick, ref, reactive, computed, onMounted } from "vue";

const personsData = reactive([
  {
    id: "wu2722",
    coor: { lon: 116.397428, lat: 32.90923 },
    name: "张华",
    lineArr: [119.397428, 36.90923, 119.397428, 36.90923],
  },
  {
    id: "uq2143",
    coor: { lon: 117.397428, lat: 33.90923 },
    name: "李明",
    lineArr: [11.2233, 44.5566, 11.2233, 44.5566],
    // lineArr: [],
  },
]);

var customDS = null;

function czInitFn() {
  customDS = new Cesium.CustomDataSource("cds-data");
  window.viewer.dataSources.add(customDS);
  // customDS.entities.add(tempPoint);
  // customDS.entities.removeAll();
}

function drawEntity(item) {
  const e = addIconAndLine(item);
  customDS.entities.add(e);
  // item.lineArr = [];
  // viewer.flyTo(e);
}
function moveEntity(item) {
  const tempArr = [];
  setInterval(() => {
    item.coor.lat += 0.1001;
    item.coor.lon += 0.1001;
    /* personsData[0].coor.lat += 0.0011;
    personsData[0].coor.lon += 0.0011; */
    customDS.entities.getById(item.id).position._value =
      Cesium.Cartesian3.fromDegrees(item.coor.lon, item.coor.lat);

    if (tempArr.length < 4) {
      tempArr.push(item.coor.lon, item.coor.lat);
    } else if (tempArr.length === 4) {
      item.lineArr = [...tempArr];
      tempArr.push(item.coor.lon, item.coor.lat);
    } else {
      item.lineArr.push(item.coor.lon, item.coor.lat);
      tempArr = [];
    }
  }, 300);
}

function deleteEntity(item) {
  customDS.entities.removeById(item.id);
  // item.lineArr = [];
}

/* const line_update = function () {
  return Cesium.Cartesian3.fromDegreesArray(item.lineArr);
}; */
function addIconAndLine(item) {
  const line_update = function () {
    return Cesium.Cartesian3.fromDegreesArray(item.lineArr);
  };
  const e = new Cesium.Entity({
    id: item.id,
    position: Cesium.Cartesian3.fromDegrees(item.coor.lon, item.coor.lat, 1),
    billboard: {
      image: `${window.xtmapConfig.publicPath}/billboard/aa.png`, // default: undefined
      show: true, // default
      scale: 1, // default: 1.0
      width: 46,
      height: 46,
      // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
    },
    label: {
      text: item.name,
      font: "14pt sans-serif",
      // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.BASELINE,
      fillColor: Cesium.Color.BLACK,
      showBackground: true,
      backgroundColor: new Cesium.Color(1, 1, 1, 0.7),
      backgroundPadding: new Cesium.Cartesian2(8, 4),
      pixelOffset: new Cesium.Cartesian2(-45, -52),
      // scaleByDistance: new Cesium.NearFarScalar(150000, 0.8, 250000, 0),
    },
    polyline: {
      show: true,
      // 定义线条的 Cartesian3 位置的数组
      positions: new Cesium.CallbackProperty(line_update, false),
      // positions: Cesium.Cartesian3.fromDegreesArray(peopleLineArr),
      width: 5,
      material: Cesium.Color.CORNFLOWERBLUE,
      clampToGround: true, // 是否贴地
      // shadows: Cesium.ShadowMode.DISABLED, // 折线是投射还是接收光源的阴影
      // classificationType: Cesium.ClassificationType.BOTH,
      // 指定用于订购地面几何形状的z索引。仅在多边形为常数且未指定高度或拉伸高度的情况下才有效  type:ConstantProperty
      // zIndex: 0,
    },
  });
  return e;
}

onMounted(() => {
  emitter.on("viewerLoad", () => {});
  czInitFn();
});
</script>


<style scoped lang="scss">
.person-posi {
  padding: 10px;
  width: 300px;
  height: 280px;
  background-color: rgba(100, 148, 237, 0.534);
  .class-person-posi-point {
    // display: flex;
    // flex-direction: column;
  }
}
</style>

