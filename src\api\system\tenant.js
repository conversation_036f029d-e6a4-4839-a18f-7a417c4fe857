import request from "@/utils/request";

// 查询租户
export function listTenant(query) {
  return request({
    url: "/system/tenant",
    method: "get",
    params: query,
  });
}

// 查询租户详细
export function getTenant(id) {
  return request({
    url: "/system/tenant/" + id,
    method: "get",
  });
}

// 新增租户
export function addTenant(data) {
  return request({
    url: "/system/tenant",
    method: "post",
    data: data,
  });
}

// 修改租户
export function updateTenant(data) {
  return request({
    url: "/system/tenant",
    method: "post",
    data: data,
  });
}

// 删除租户
export function delTenant(data) {
  return request({
    url: "/system/tenant",
    method: "delete",
    data: data,
  });
}

// 租户配置参数
export function tenantProperties() {
  return request({
    url: "/system/tenant/properties",
    method: "get",
  });
}
