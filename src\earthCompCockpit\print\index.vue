<script setup>
import html2canvas from "html2canvas";

import { ElMessage, ElMessageBox } from "element-plus";
import { nextTick, ref, reactive, computed, onMounted } from "vue";
const inputName = ref("");
const isDownload = computed(() => {
  return !inputName.value;
});

function printMap() {
  const mapDom = document.getElementById("cesiumContainer");
  html2canvas(mapDom, {
    backgroundColor: null,
    useCORS: true,
    logging: false,
  }).then((canvas) => {
    if (!inputName.value) inputName.value = "地图截图";
    // console.log(canvas, "dom");
    // console.log(canvas.toDataURL());
    const picLink = document.createElement("a");
    picLink.href = canvas.toDataURL(); //下载链接
    picLink.setAttribute("download", `${inputName.value}.png`);
    picLink.style.display = "none";
    document.body.appendChild(picLink);
    picLink.click();
    inputName.value = "";
    document.body.removeChild(picLink);
    /* canvas.toBlob((blob) => {
      const filename = `${new Date().getTime()}.jpg`;
      // 转换canvas图片数据格式为formData
      const file = new File([blob], filename, { type: "image/jpg" });
      console.log(file);
    }); */
    open();
  });
}

const open = () => {
  ElMessageBox.alert("已截图并下载，请至浏览器右上角下载文件列表中查看", "提示", {
    confirmButtonText: "确定",
    /* callback: (action) => {
      ElMessage({
        type: "info",
        message: `action: ${action}`,
      });
    }, */
  });
};
onMounted(() => {});

//也可以通过canvas2image来设置,好处在于动态调节图幅纵横比
// var imageWidth = 750;
// var ximg = Canvas2Image.Canvas2Image.convertToImage(canvas, imageWidth, imageWidth * canvas.height / canvas.width,'png');

function printMap3() {
  // console.log(window.viewer.scene.canvas, "window.viewer.scene.canvas");
  const picLink = document.createElement("a");
  picLink.href = window.viewer.scene.canvas.toDataURL(); //下载链接
  picLink.setAttribute("download", `${inputName.value}.png`);
  picLink.style.display = "none";
  document.body.appendChild(picLink);
  picLink.click();
  inputName.value = "";
  document.body.removeChild(picLink);
}

function printMap2() {
  const mapCanvas = window.viewer.scene.canvas;

  const genImg = Canvas2Image.convertToImage(
    mapCanvas,
    mapCanvas.width,
    mapCanvas.height,
    "png"
  );
  const picLink = document.createElement("a");
  picLink.href = genImg.src; //下载链接
}
</script>

<template>
  <div class="print">
    <div class="print-header">
      <span>地图打印</span>
    </div>
    <div style="padding: 5px">缩放地图视角至指定位置后，点击下载即可</div>
    <div class="print-input">
      <el-input
        v-model="inputName"
        style="width: 200px"
        placeholder="请先输入图片保存名称"
      />
      <el-button type="primary" :disabled="isDownload" plain @click="printMap"
        >下载</el-button
      >
    </div>
  </div>
</template>

<style scoped lang="scss">
.print {
  width: 300px;
  height: 180px;
  // background: url("@/assets/xtui/tools/dialogback.png");
  // background-size: 100% 100%;
  // display: flex;
  // flex-direction: column;
  // color: antiquewhite;
  // font-family: ysbthzt;
  background: rgba(8, 76, 124, 0.5);
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%) 2;
  backdrop-filter: blur(10px);
  padding: 8px;
  .print-header {
    background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center center;
    background-size: 100% 100%;
    height: 32px;
    width: 100%;
    margin: 5px 0px 15px 0px;
    padding: 5px 30px;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0px;
    color: aliceblue;
  }
  .print-input {
    display: flex;
    flex-direction: row;
    padding: 10px;
    justify-content: space-between;
  }
  .measure-menu {
    padding: 10px 10px;
  }
}
</style>

<!-- https://www.jianshu.com/p/b0203deb43fe -->
