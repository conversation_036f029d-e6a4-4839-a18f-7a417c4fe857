function _arrayLikeToArray(r, a) {
  (null == a || a > r.length) && (a = r.length);
  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];
  return n;
}
function _arrayWithoutHoles(r) {
  if (Array.isArray(r)) return _arrayLikeToArray(r);
}
function _classCallCheck(a, n) {
  if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function");
}
function _defineProperties(e, r) {
  for (var t = 0; t < r.length; t++) {
    var o = r[t];
    o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o);
  }
}
function _createClass(e, r, t) {
  return r && _defineProperties(e.prototype, r), Object.defineProperty(e, "prototype", {
    writable: !1
  }), e;
}
function _defineProperty(e, r, t) {
  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
    value: t,
    enumerable: !0,
    configurable: !0,
    writable: !0
  }) : e[r] = t, e;
}
function _iterableToArray(r) {
  if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r);
}
function _nonIterableSpread() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _toConsumableArray(r) {
  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();
}
function _toPrimitive(t, r) {
  if ("object" != typeof t || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r );
    if ("object" != typeof i) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (String )(t);
}
function _toPropertyKey(t) {
  var i = _toPrimitive(t, "string");
  return "symbol" == typeof i ? i : i + "";
}
function _unsupportedIterableToArray(r, a) {
  if (r) {
    if ("string" == typeof r) return _arrayLikeToArray(r, a);
    var t = {}.toString.call(r).slice(8, -1);
    return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;
  }
}

/**
 * @description: 获取地球坐标
 * @param {obj} viewer viewer对象
 * @param {e} pxCoor e.position
 * @param {string} resultType 设置返回值的显示格式是经纬度还是xyz(cartesian)
 * @return { obj } 返回的默认格式：{ lon: lon, lat: lat, height: height }
 */

// 外层必须传 viewer和e ,所以没有设置fn默认值
function getCoorFromPx(_ref) {
  var _ref$resultType = _ref.resultType,
    resultType = _ref$resultType === void 0 ? 'jwd' : _ref$resultType,
    viewer = _ref.viewer,
    pxCoor = _ref.pxCoor;
  //判定标识
  var isOnOsgb;
  var isTerrainOpen;
  var cartesian = null;
  var jwdCoor = null;
  var xyzCoor = null;
  var pick = viewer.scene.pick(pxCoor);

  // 应该用drill pick,获取所有通过点击捕获到的实体,如果有模型,就获取模型，
  // 否则，当模型如果处在地面以下一部分时，pick是会直接判定为点击到地面

  // 有无模型
  if (pick && pick.primitive instanceof Cesium.Cesium3DTileFeature || pick && pick.primitive instanceof Cesium.Cesium3DTileset || pick && pick.primitive instanceof Cesium.Model) {
    isOnOsgb = true;
  }
  // 有无地形,Cesium.EllipsoidTerrainProvider是用户不加载地形时，cz默认的空地形,所以t时无 , f时有
  if (viewer.terrainProvider instanceof Cesium.EllipsoidTerrainProvider) {
    isTerrainOpen = false;
  } else {
    isTerrainOpen = true;
  }
  if (isOnOsgb) {
    cartesian = viewer.scene.pickPosition(pxCoor);
  } else {
    if (isTerrainOpen) {
      var ray = viewer.scene.camera.getPickRay(pxCoor);
      if (!ray) return;
      cartesian = viewer.scene.globe.pick(ray, viewer.scene);
    } else {
      cartesian = viewer.scene.camera.pickEllipsoid(pxCoor, viewer.scene.globe.ellipsoid);
    }
  }
  if (cartesian) {
    var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    var lon = parseFloat(Cesium.Math.toDegrees(cartographic.longitude).toFixed(6));
    var lat = parseFloat(Cesium.Math.toDegrees(cartographic.latitude).toFixed(6));
    var height = cartographic.height > 0 ? cartographic.height : 0.1; // 模型高度

    jwdCoor = {
      lon: lon,
      lat: lat,
      height: height
    };
    xyzCoor = cartesian;
    return resultType === 'xyz' ? xyzCoor : jwdCoor;

    /* let position = transformCartesianToWGS84(viewer, cartesian);
    if (position.alt < 0) {
    	coor = transformWGS84ToCartesian(viewer, position, 0.1);
    } */
  }
  return null;
}

/* 
const handler = new Cesium.ScreenSpaceEventHandler(viewer.canvas);
return new Promise((resolve, reject) => {
	handler.setInputAction((e) => {
		let posi = e.position;
		let pickedObj = scene.pick(e.position);

		let coor = getCatesian3FromPX(viewer, endPos);

		resolve(coor);
		reject('--err--');
	}, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}); 
 
handler.setInputAction(function (movement) {
    let endPos = movement.endPosition;
    CreateRemindertip(toolTip, endPos, true);
    if (Cesium.defined(polyline)) {
    anchorpoints.pop();
    let cartesian = getCatesian3FromPX(viewer, endPos);
    anchorpoints.push(cartesian);
    }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE); 
*/

var PlotBillBoard = /*#__PURE__*/function () {
  function PlotBillBoard(viewer) {
    _classCallCheck(this, PlotBillBoard);
    this.myCollection = null;
    this.yourCollection = null;
    this.entity = null;
    this.plotHandler = null;
    this.viewer = viewer;
    this.init(this.viewer);
    this.jsonCollection = []; // 用json组织数据  json结构需要统一
    // class extend 形式 写个绘制基类  其他的线、面都去extend
  }
  return _createClass(PlotBillBoard, [{
    key: "init",
    value: function init(viewer) {
      this.myCollection = new Cesium.CustomDataSource('myplotdata');
      this.yourCollection = new Cesium.CustomDataSource('yourplotdata');
      this.viewer.dataSources.add(this.myCollection);
      this.viewer.dataSources.add(this.yourCollection);
      /* this.plotHandler = new Cesium.ScreenSpaceEventHandler(
      	viewer.scene.canvas
      ); */
    }
  }, {
    key: "draw",
    value: function draw() {
      var _this = this;
      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {
        picType: 'p1111',
        czType: '',
        busiType: ''
      };
      return new Promise(function (resolve, reject) {
        _this.plotHandler = new Cesium.ScreenSpaceEventHandler(_this.viewer.scene.canvas);
        _this.plotHandler.setInputAction(function (e) {
          var coor = getCoorFromPx({
            viewer: _this.viewer,
            pxCoor: e.position
          });
          if (Cesium.defined(coor)) {
            var _e = _this.addBillboard(coor, options);
            _this.myCollection.entities.add(_e);
            /* this.jsonCollection.push({
            	name: 'xxx',
            	czType: type,
            	position: coor,
            	picType: options.picType,
            	entityOptions: options,
            }); */
            var plotInfo = {
              name: 'xxx',
              czType: options.czType,
              position: coor,
              picType: options.picType,
              busiType: options.busiType,
              entityOptions: options
            };
            _this.destroyHandler();
            resolve(plotInfo);
          }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      });
    }
  }, {
    key: "reDraw",
    value: function reDraw(options) {
      var e = this.addBillboard(options.position, options.entityOptions);
      this.yourCollection.entities.add(e);
    }
  }, {
    key: "addBillboard",
    value: function addBillboard(position, options) {
      return new Cesium.Entity({
        position: Cesium.Cartesian3.fromDegrees(position.lon, position.lat, position.height),
        billboard: {
          // image: `${window.xtmapConfig.plot.path}data/image/emergIcon/${parentUrl}/${url}.png`, // default: undefined
          image: "".concat(window.xtmapConfig.plot.path, "data/image/emergIcon/").concat(options.busiType, "/").concat(options.picType, ".png"),
          // default: undefined
          scale: 1,
          // default: 1.0
          width: 64,
          height: 64,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM //相对于对象的原点的垂直位置，BOTTOM时锚点在下，对象在上
        }
      });
    }
  }, {
    key: "getPlotData",
    value: function getPlotData() {
      return this.jsonCollection;
    }
  }, {
    key: "setPlotData",
    value: function setPlotData(data) {
      this.reDraw(data);

      /* for (const i of data) {
      	this.reDraw(i);
      } */
    }
  }, {
    key: "clearData",
    value: function clearData() {
      this.myCollection.entities.removeAll();
      this.yourCollection.entities.removeAll();
    }
  }, {
    key: "destroyHandler",
    value: function destroyHandler() {
      this.plotHandler.destroy();
      this.plotHandler = null;
      // this.plotHandler.removeInputAction(
      // 	Cesium.ScreenSpaceEventType.LEFT_CLICK
      // );
    }
  }]);
}();

/*
 * @Description: 飞线效果
 * @Version: 1.0
 * @Author: as
 * @Date: 2022-03-05 16:13:21
 * @LastEditors: as
 * @LastEditTime: 2022-03-05 17:39:38
 */
var LineFlowMaterialProperty = /*#__PURE__*/function () {
  function LineFlowMaterialProperty(options) {
    _classCallCheck(this, LineFlowMaterialProperty);
    this._definitionChanged = new Cesium.Event();
    this._color = undefined;
    this._speed = undefined;
    this._percent = undefined;
    this._gradient = undefined;
    this.color = options.color;
    this.speed = options.speed;
    this.percent = options.percent;
    this.gradient = options.gradient;
  }
  return _createClass(LineFlowMaterialProperty, [{
    key: "isConstant",
    get: function get() {
      return false;
    }
  }, {
    key: "definitionChanged",
    get: function get() {
      return this._definitionChanged;
    }
  }, {
    key: "getType",
    value: function getType(time) {
      return Cesium.Material.LineFlowMaterialType;
    }
  }, {
    key: "getValue",
    value: function getValue(time, result) {
      if (!Cesium.defined(result)) {
        result = {};
      }
      result.color = Cesium.Property.getValueOrDefault(this._color, time, Cesium.Color.RED, result.color);
      result.speed = Cesium.Property.getValueOrDefault(this._speed, time, 5.0, result.speed);
      result.percent = Cesium.Property.getValueOrDefault(this._percent, time, 0.1, result.percent);
      result.gradient = Cesium.Property.getValueOrDefault(this._gradient, time, 0.01, result.gradient);
      return result;
    }
  }, {
    key: "equals",
    value: function equals(other) {
      return this === other || other instanceof LineFlowMaterialProperty && Cesium.Property.equals(this._color, other._color) && Cesium.Property.equals(this._speed, other._speed) && Cesium.Property.equals(this._percent, other._percent) && Cesium.Property.equals(this._gradient, other._gradient);
    }
  }]);
}();
Object.defineProperties(LineFlowMaterialProperty.prototype, {
  color: Cesium.createPropertyDescriptor('color'),
  speed: Cesium.createPropertyDescriptor('speed'),
  percent: Cesium.createPropertyDescriptor('percent'),
  gradient: Cesium.createPropertyDescriptor('gradient')
});
Cesium.LineFlowMaterialProperty = LineFlowMaterialProperty;
Cesium.Material.LineFlowMaterialProperty = 'LineFlowMaterialProperty';
Cesium.Material.LineFlowMaterialType = 'LineFlowMaterialType';
Cesium.Material.LineFlowMaterialSource = "\n    uniform vec4 color;\n    uniform float speed;\n    uniform float percent;\n    uniform float gradient;\n    \n    czm_material czm_getMaterial(czm_materialInput materialInput){\n      czm_material material = czm_getDefaultMaterial(materialInput);\n      vec2 st = materialInput.st;\n      float t =fract(czm_frameNumber * speed / 1000.0);\n      t *= (1.0 + percent);\n      float alpha = smoothstep(t- percent, t, st.s) * step(-t, -st.s);\n      alpha += gradient;\n      material.diffuse = color.rgb;\n      material.alpha = alpha;\n      return material;\n    }\n    ";
Cesium.Material._materialCache.addMaterial(Cesium.Material.LineFlowMaterialType, {
  fabric: {
    type: Cesium.Material.LineFlowMaterialType,
    uniforms: {
      color: new Cesium.Color(1.0, 0.0, 0.0, 1.0),
      speed: 10.0,
      percent: 0.1,
      gradient: 0.01
    },
    source: Cesium.Material.LineFlowMaterialSource
  },
  translucent: function translucent(material) {
    return true;
  }
});

// import './material.js';
// 有三种type : cztype（哪种entity）,pictype(哪张图),busitype(图是哪一类）
// 					)
var PlotLine = /*#__PURE__*/function () {
  function PlotLine(viewer) {
    var _this = this;
    _classCallCheck(this, PlotLine);
    _defineProperty(this, "moveEvent", function (e) {
      var newPosition = getCoorFromPx({
        viewer: _this.viewer,
        pxCoor: e.endPosition,
        resultType: 'xyz'
      });
      if (Cesium.defined(_this.floatingPoint) && Cesium.defined(newPosition)) {
        _this.floatingPoint.position.setValue(newPosition);
        _this.poiArr.pop();
        _this.poiArr.push(newPosition);
      }
    });
    this.viewer = viewer;
    this.plotHandler = null;
    this.myCollection = null;
    this.yourCollection = null;

    // 点集和线是分开绘制的，所以为左键点集合声明一个CustomDataSource
    this.tempPointArr;
    this.floatingPoint = undefined;
    this.poiArr = [];
    this.lineEntity = undefined; //当前正在绘制的  随着鼠标移动变化的线
    this.init(this.viewer);
    this.jsonCollection = []; // 用json组织数据  json结构需要统一
    // class extend 形式 写个绘制基类  其他的线、面都去extend
  }
  return _createClass(PlotLine, [{
    key: "init",
    value: function init(viewer) {
      this.myCollection = new Cesium.CustomDataSource('myplotdata');
      this.yourCollection = new Cesium.CustomDataSource('yourplotdata');
      this.viewer.dataSources.add(this.myCollection);
      this.viewer.dataSources.add(this.yourCollection);
      this.tempPointArr = new Cesium.CustomDataSource('poidata');
      this.viewer.dataSources.add(this.tempPointArr);
    }
  }, {
    key: "draw",
    value: function draw() {
      var _this2 = this;
      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {
        picType: 'p1111',
        czType: '',
        busiType: ''
      };
      this.plotHandler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
      this.plotHandler.setInputAction(this.leftEvent(options), Cesium.ScreenSpaceEventType.LEFT_CLICK);
      this.plotHandler.setInputAction(this.moveEvent, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      return new Promise(function (resolve, reject) {
        _this2.plotHandler.setInputAction(_this2.rightEvent(resolve, reject, options), Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      });
    }
  }, {
    key: "leftEvent",
    value: function leftEvent(options) {
      var _this3 = this;
      var r = function r(e) {
        var earthPosition = getCoorFromPx({
          viewer: _this3.viewer,
          pxCoor: e.position,
          resultType: 'xyz'
        });
        if (Cesium.defined(earthPosition)) {
          var tempPoint;
          if (_this3.poiArr.length === 0) {
            // console.log(this.poiArr, this.poiArr.length, 'okokokok22222');
            _this3.floatingPoint = _this3.createPoint(earthPosition, '左键绘制，右键结束');
            _this3.viewer.entities.add(_this3.floatingPoint);
            var dynamicPositions = new Cesium.CallbackProperty(function () {
              return _this3.poiArr;
            }, false);
            _this3.lineEntity = _this3.addPolyline(dynamicPositions, options);
            _this3.myCollection.entities.add(_this3.lineEntity);
            _this3.poiArr.push(earthPosition);
            tempPoint = _this3.createPoint(earthPosition);
            _this3.tempPointArr.entities.add(tempPoint);
            _this3.poiArr.push(earthPosition);
          } else {
            tempPoint = _this3.createPoint(earthPosition);
            _this3.tempPointArr.entities.add(tempPoint);
            _this3.poiArr.push(earthPosition);
          }

          // this.tempPointArr.entities.add(tempPoint);
          // this.poiArr.push(earthPosition);
        }
      };
      return r;
    }
  }, {
    key: "rightEvent",
    value: function rightEvent(resolve, reject, options) {
      var _this4 = this;
      var r = function r(e) {
        if (_this4.poiArr.length === 0) {
          return;
        }
        var Position = getCoorFromPx({
          viewer: _this4.viewer,
          pxCoor: e.position,
          resultType: 'xyz'
        });
        if (Cesium.defined(Position)) {
          _this4.poiArr.pop();
          _this4.poiArr.push(Position);
          var endPoint = _this4.createPoint(Position, '');
          _this4.tempPointArr.entities.add(endPoint);
          _this4.viewer.entities.remove(_this4.floatingPoint);
          _this4.floatingPoint = null;
          var singleArr = _toConsumableArray(_this4.poiArr);
          var singleEntity = _this4.addPolyline(singleArr, options);
          _this4.myCollection.entities.add(singleEntity);
          _this4.myCollection.entities.remove(_this4.lineEntity);
          _this4.lineEntity = null;
          _this4.tempPointArr.entities.removeAll();
          _this4.poiArr.length = 0;
          var plotInfo = {
            name: 'xxx',
            position: singleArr,
            czType: options.czType,
            busiType: options.busiType,
            picType: options.picType,
            entityOptions: options
          };
          _this4.destroyHandler();
          resolve(plotInfo);
          reject('err----');
        }
      };
      return r;
    }
  }, {
    key: "reDraw",
    value: function reDraw(options) {
      var e = this.addPolyline(options.position, options.entityOptions);
      this.yourCollection.entities.add(e);
    }
  }, {
    key: "addPolyline",
    value: function addPolyline(position, options) {
      var material;
      switch (options.picType) {
        case 'l111':
          material = new Cesium.PolylineDashMaterialProperty({
            color: Cesium.Color.fromCssColorString('rgb(20, 120, 255)').withAlpha(0.83)
          });
          break;
        case 'l112':
          material = Cesium.Color.RED;
          break;
        case 'l113':
          material = new Cesium.LineFlowMaterialProperty({
            color: new Cesium.Color.fromCssColorString('rgba(39, 125, 195, 1)'),
            speed: 6,
            // speed: 10 * Math.random(),
            percent: 0.75,
            gradient: 0.91
          });
          break;
      }
      return new Cesium.Entity({
        polyline: {
          positions: position,
          clampToGround: true,
          width: 3,
          material: material
        }
      });
    }
  }, {
    key: "createPoint",
    value: function createPoint(poi, txt) {
      var point = new Cesium.Entity({
        position: poi,
        point: {
          color: Cesium.Color.YELLOW.withAlpha(0),
          pixelSize: 6
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        label: {
          text: txt || '',
          font: 'bold 12px MicroSoft YaHei',
          outlineWidth: 2,
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          verticalOrigin: Cesium.VerticalOrigin.TOP,
          pixelOffset: new Cesium.Cartesian2(15, 0),
          // pixelOffset: new Cesium.Cartesian2(-15, 16), //偏移量
          showBackground: true,
          backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
          backgroundPadding: new Cesium.Cartesian2(6, 3),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      });
      return point;
    }
  }, {
    key: "getPlotData",
    value: function getPlotData() {
      return this.jsonCollection;
    }
  }, {
    key: "setPlotData",
    value: function setPlotData(data) {
      this.reDraw(data);

      /* for (const i of data) {
      	this.reDraw(i);
      } */
    }
  }, {
    key: "clearData",
    value: function clearData() {
      this.myCollection.entities.removeAll();
      this.yourCollection.entities.removeAll();
    }
  }, {
    key: "destroyHandler",
    value: function destroyHandler() {
      // this.plotHandler.removeInputAction(
      // 	Cesium.ScreenSpaceEventType.LEFT_CLICK
      // );

      this.plotHandler.destroy();
      this.plotHandler = null;
    }
  }]);
}();

// 有三种type : cztype（哪种entity）,pictype(哪张图),busitype(图是哪一类）
// 					)
var PlotPolygon = /*#__PURE__*/function () {
  function PlotPolygon(viewer) {
    var _this = this;
    _classCallCheck(this, PlotPolygon);
    _defineProperty(this, "moveEvent", function (e) {
      var newPosition = getCoorFromPx({
        viewer: _this.viewer,
        pxCoor: e.endPosition,
        resultType: 'xyz'
      });
      if (Cesium.defined(_this.floatingPoint) && Cesium.defined(newPosition)) {
        _this.floatingPoint.position.setValue(newPosition);
        _this.poiArr.pop();
        _this.poiArr.push(newPosition);
      }
    });
    this.viewer = viewer;
    this.plotHandler = null;
    this.myCollection = null;
    this.yourCollection = null;

    // 点集和线是分开绘制的，所以为左键点集合声明一个CustomDataSource
    this.tempPointArr;
    this.floatingPoint = undefined;
    this.poiArr = [];
    this.polygonEntity = undefined; //当前正在绘制的  随着鼠标移动变化的线
    this.init(this.viewer);
    this.jsonCollection = []; // 用json组织数据  json结构需要统一
    // class extend 形式 写个绘制基类  其他的线、面都去extend
  }
  return _createClass(PlotPolygon, [{
    key: "init",
    value: function init(viewer) {
      this.myCollection = new Cesium.CustomDataSource('myplotdata');
      this.yourCollection = new Cesium.CustomDataSource('yourplotdata');
      this.viewer.dataSources.add(this.myCollection);
      this.viewer.dataSources.add(this.yourCollection);
      this.tempPointArr = new Cesium.CustomDataSource('poiPolygonData');
      this.viewer.dataSources.add(this.tempPointArr);
    }
  }, {
    key: "draw",
    value: function draw() {
      var _this2 = this;
      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {
        picType: 'p1111',
        czType: '',
        busiType: ''
      };
      this.plotHandler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
      this.plotHandler.setInputAction(this.leftEvent(options), Cesium.ScreenSpaceEventType.LEFT_CLICK);
      this.plotHandler.setInputAction(this.moveEvent, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      return new Promise(function (resolve, reject) {
        _this2.plotHandler.setInputAction(_this2.rightEvent(resolve, reject, options), Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      });
    }
  }, {
    key: "leftEvent",
    value: function leftEvent(options) {
      var _this3 = this;
      var r = function r(e) {
        var earthPosition = getCoorFromPx({
          viewer: _this3.viewer,
          pxCoor: e.position,
          resultType: 'xyz'
        });
        if (Cesium.defined(earthPosition)) {
          var tempPoint;
          if (_this3.poiArr.length === 0) {
            _this3.floatingPoint = _this3.createPoint(earthPosition, '左键绘制，右键结束');
            _this3.viewer.entities.add(_this3.floatingPoint);
            var dynamicPositions = new Cesium.CallbackProperty(function () {
              return new Cesium.PolygonHierarchy(_this3.poiArr);
            }, false);
            _this3.polygonEntity = _this3.createArea(dynamicPositions, options);
            _this3.myCollection.entities.add(_this3.polygonEntity);
            _this3.poiArr.push(earthPosition);
            tempPoint = _this3.createPoint(earthPosition);
          } else {
            tempPoint = _this3.createPoint(earthPosition);
          }
          _this3.tempPointArr.entities.add(tempPoint);
          _this3.poiArr.push(earthPosition);
        }
      };
      return r;
    }
  }, {
    key: "rightEvent",
    value: function rightEvent(resolve, reject, options) {
      var _this4 = this;
      var r = function r(e) {
        if (_this4.poiArr.length === 0) {
          return;
        }
        var Position = getCoorFromPx({
          viewer: _this4.viewer,
          pxCoor: e.position,
          resultType: 'xyz'
        });
        if (Cesium.defined(Position)) {
          _this4.poiArr.pop();
          _this4.poiArr.push(Position);
          var endPoint = _this4.createPoint(Position, '');
          _this4.tempPointArr.entities.add(endPoint);
          _this4.viewer.entities.remove(_this4.floatingPoint);
          _this4.floatingPoint = null;
          var singleArr = _toConsumableArray(_this4.poiArr);
          var singleEntity = _this4.createArea(singleArr, options);
          _this4.myCollection.entities.add(singleEntity);
          _this4.myCollection.entities.remove(_this4.polygonEntity);
          _this4.polygonEntity = null;
          _this4.tempPointArr.entities.removeAll();
          _this4.poiArr.length = 0;
          var plotInfo = {
            name: 'xxx',
            position: singleArr,
            czType: options.czType,
            busiType: options.busiType,
            picType: options.picType,
            entityOptions: options
          };
          _this4.destroyHandler();
          resolve(plotInfo);
          reject('err----');
        }
      };
      return r;
    }
  }, {
    key: "reDraw",
    value: function reDraw(options) {
      var e = this.createArea(options.position, options.entityOptions);
      this.yourCollection.entities.add(e);
    }
  }, {
    key: "addPolyline",
    value: function addPolyline(position, options) {
      var material;
      switch (options.picType) {
        case 'l111':
          material = new Cesium.PolylineDashMaterialProperty({
            color: Cesium.Color.fromCssColorString('rgb(20, 120, 255)').withAlpha(0.83)
          });
          break;
        case 'l112':
          material = Cesium.Color.RED;
          break;
        case 'l113':
          material = new Cesium.LineFlowMaterialProperty({
            color: new Cesium.Color.fromCssColorString('rgba(39, 125, 195, 1)'),
            speed: 6,
            // speed: 10 * Math.random(),
            percent: 0.75,
            gradient: 0.91
          });
          break;
      }
      return new Cesium.Entity({
        polyline: {
          positions: position,
          clampToGround: true,
          width: 3,
          material: material
        }
      });
    }
  }, {
    key: "createArea",
    value: function createArea(position, options) {
      var shape = new Cesium.Entity({
        polygon: {
          hierarchy: position,
          //positions : Array.<Cartesian3>
          // height: 20, // 多边形相对于椭球面的高度
          material: Cesium.Color.fromCssColorString('rgb(20, 120, 255)').withAlpha(0.33),
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH
          /* material: new Cesium.PolylineDashMaterialProperty({
          	color: Cesium.Color.fromCssColorString('rgb(20, 120, 255)').withAlpha(0.83),
          }), */
          // outline: true,
          // outlineColor: Cesium.Color.YELLOW,
          // outlineWidth: 2.0,
        }
      });
      return shape;
    }
  }, {
    key: "createPoint",
    value: function createPoint(poi, txt) {
      var point = new Cesium.Entity({
        position: poi,
        point: {
          color: Cesium.Color.YELLOW.withAlpha(0.6),
          pixelSize: 6
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        label: {
          text: txt || '',
          font: 'bold 12px MicroSoft YaHei',
          outlineWidth: 2,
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          verticalOrigin: Cesium.VerticalOrigin.TOP,
          pixelOffset: new Cesium.Cartesian2(15, 0),
          // pixelOffset: new Cesium.Cartesian2(-15, 16), //偏移量
          showBackground: true,
          backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
          backgroundPadding: new Cesium.Cartesian2(6, 3),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      });
      return point;
    }
  }, {
    key: "getPlotData",
    value: function getPlotData() {
      return this.jsonCollection;
    }
  }, {
    key: "setPlotData",
    value: function setPlotData(data) {
      this.reDraw(data);

      /* for (const i of data) {
      	this.reDraw(i);
      } */
    }
  }, {
    key: "clearData",
    value: function clearData() {
      this.myCollection.entities.removeAll();
      this.yourCollection.entities.removeAll();
    }
  }, {
    key: "destroyHandler",
    value: function destroyHandler() {
      // this.plotHandler.removeInputAction(
      // 	Cesium.ScreenSpaceEventType.LEFT_CLICK
      // );

      this.plotHandler.destroy();
      this.plotHandler = null;
    }
  }]);
}();

// 有三种type : cztype（哪种entity）,pictype(哪张图),busitype(图是哪一类）
// 					)
var PlotRectangle = /*#__PURE__*/function () {
  function PlotRectangle(viewer) {
    var _this = this;
    _classCallCheck(this, PlotRectangle);
    _defineProperty(this, "moveEvent", function (e) {
      var newPosition = getCoorFromPx({
        viewer: _this.viewer,
        pxCoor: e.endPosition,
        resultType: 'xyz'
      });
      if (Cesium.defined(_this.floatingPoint) && Cesium.defined(newPosition)) {
        _this.floatingPoint.position.setValue(newPosition);
        _this.poiArr.pop();
        _this.poiArr.push(newPosition);
      }
    });
    this.viewer = viewer;
    this.plotHandler = null;
    this.myCollection = null;
    this.yourCollection = null;

    // 点集和线是分开绘制的，所以为左键点集合声明一个CustomDataSource
    this.tempPointArr;
    this.floatingPoint = undefined;
    this.poiArr = [];
    this.rectangleEntity = undefined; //当前正在绘制的  随着鼠标移动变化的线
    this.init(this.viewer);
    this.jsonCollection = []; // 用json组织数据  json结构需要统一
    // class extend 形式 写个绘制基类  其他的线、面都去extend
  }
  return _createClass(PlotRectangle, [{
    key: "init",
    value: function init(viewer) {
      this.myCollection = new Cesium.CustomDataSource('myplotdata');
      this.yourCollection = new Cesium.CustomDataSource('yourplotdata');
      this.viewer.dataSources.add(this.myCollection);
      this.viewer.dataSources.add(this.yourCollection);
      this.tempPointArr = new Cesium.CustomDataSource('poiPolygonData');
      this.viewer.dataSources.add(this.tempPointArr);
    }
  }, {
    key: "draw",
    value: function draw() {
      var _this2 = this;
      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {
        picType: 'p1111',
        czType: '',
        busiType: ''
      };
      this.plotHandler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
      this.plotHandler.setInputAction(this.leftEvent(options), Cesium.ScreenSpaceEventType.LEFT_CLICK);
      this.plotHandler.setInputAction(this.moveEvent, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      return new Promise(function (resolve, reject) {
        _this2.plotHandler.setInputAction(_this2.rightEvent(resolve, reject, options), Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      });
    }
  }, {
    key: "leftEvent",
    value: function leftEvent(options) {
      var _this3 = this;
      var r = function r(e) {
        var earthPosition = getCoorFromPx({
          viewer: _this3.viewer,
          pxCoor: e.position,
          resultType: 'xyz'
        });
        if (Cesium.defined(earthPosition)) {
          var tempPoint;
          if (_this3.poiArr.length === 0) {
            _this3.floatingPoint = _this3.createPoint(earthPosition, '左键绘制，右键结束');
            _this3.viewer.entities.add(_this3.floatingPoint);

            /* let dynamicPositions = new Cesium.CallbackProperty(() => {
            	// return this.poiArr;
            	return Cesium.Rectangle.fromCartesianArray(this.poiArr);
            }, false); */

            _this3.rectangleEntity = _this3.createRectangle(_this3.poiArr, options);
            _this3.myCollection.entities.add(_this3.rectangleEntity);
            _this3.poiArr.push(earthPosition);
            tempPoint = _this3.createPoint(earthPosition);
          } else {
            tempPoint = _this3.createPoint(earthPosition);
          }
          _this3.tempPointArr.entities.add(tempPoint);
          _this3.poiArr.push(earthPosition);
        }
      };
      return r;
    }
  }, {
    key: "rightEvent",
    value: function rightEvent(resolve, reject, options) {
      var _this4 = this;
      var r = function r(e) {
        if (_this4.poiArr.length === 0) {
          return;
        }
        var Position = getCoorFromPx({
          viewer: _this4.viewer,
          pxCoor: e.position,
          resultType: 'xyz'
        });
        if (Cesium.defined(Position)) {
          _this4.poiArr.pop();
          _this4.poiArr.push(Position);
          var endPoint = _this4.createPoint(Position, '');
          _this4.tempPointArr.entities.add(endPoint);
          _this4.viewer.entities.remove(_this4.floatingPoint);
          _this4.floatingPoint = null;
          var singleArr = _toConsumableArray(_this4.poiArr);
          var singleEntity = _this4.createRectangle(singleArr, options);
          _this4.myCollection.entities.add(singleEntity);
          _this4.myCollection.entities.remove(_this4.rectangleEntity);
          _this4.rectangleEntity = null;
          _this4.tempPointArr.entities.removeAll();
          _this4.poiArr.length = 0;
          var plotInfo = {
            name: 'xxx',
            position: singleArr,
            czType: options.czType,
            busiType: options.busiType,
            picType: options.picType,
            entityOptions: options
          };
          _this4.destroyHandler();
          resolve(plotInfo);
          reject('err----');
        }
      };
      return r;
    }
  }, {
    key: "reDraw",
    value: function reDraw(options) {
      var e = this.createRectangle(options.position, options.entityOptions);
      this.yourCollection.entities.add(e);
    }
  }, {
    key: "createRectangle",
    value: function createRectangle(position, options) {
      var shape = new Cesium.Entity({
        rectangle: {
          coordinates: new Cesium.CallbackProperty(function () {
            return Cesium.Rectangle.fromCartesianArray(position);
          }, false),
          // Cesium.Rectangle.fromDegrees(120.0, 40, 125, 45), // west, south, east, north
          material: Cesium.Color.fromCssColorString('rgb(20, 120, 255)').withAlpha(0.33),
          classificationType: Cesium.ClassificationType.BOTH

          // outline: true, // height must be set for outline to display
          // outlineColor: Cesium.Color.RED,
          // extrudedHeight: 10000,
        }
      });
      return shape;
    }
  }, {
    key: "createPoint",
    value: function createPoint(poi, txt) {
      var point = new Cesium.Entity({
        position: poi,
        point: {
          color: Cesium.Color.YELLOW.withAlpha(0.6),
          pixelSize: 6
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        label: {
          text: txt || '',
          font: 'bold 12px MicroSoft YaHei',
          outlineWidth: 2,
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          verticalOrigin: Cesium.VerticalOrigin.TOP,
          pixelOffset: new Cesium.Cartesian2(15, 0),
          // pixelOffset: new Cesium.Cartesian2(-15, 16), //偏移量
          showBackground: true,
          backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
          backgroundPadding: new Cesium.Cartesian2(6, 3),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      });
      return point;
    }
  }, {
    key: "getPlotData",
    value: function getPlotData() {
      return this.jsonCollection;
    }
  }, {
    key: "setPlotData",
    value: function setPlotData(data) {
      this.reDraw(data);

      /* for (const i of data) {
      	this.reDraw(i);
      } */
    }
  }, {
    key: "clearData",
    value: function clearData() {
      this.myCollection.entities.removeAll();
      this.yourCollection.entities.removeAll();
    }
  }, {
    key: "destroyHandler",
    value: function destroyHandler() {
      // this.plotHandler.removeInputAction(
      // 	Cesium.ScreenSpaceEventType.LEFT_CLICK
      // );

      this.plotHandler.destroy();
      this.plotHandler = null;
    }
  }]);
}();

// 有三种type : cztype（哪种entity）,pictype(哪张图),busitype(图是哪一类）
// 					)
var PlotCircle = /*#__PURE__*/function () {
  function PlotCircle(viewer) {
    var _this = this;
    _classCallCheck(this, PlotCircle);
    _defineProperty(this, "moveEvent", function (e) {
      var newPosition = getCoorFromPx({
        viewer: _this.viewer,
        pxCoor: e.endPosition,
        resultType: 'xyz'
      });
      if (Cesium.defined(_this.floatingPoint) && Cesium.defined(newPosition)) {
        _this.floatingPoint.position.setValue(newPosition);
        var centreCoor = Cesium.Cartographic.fromCartesian(_this.centre);
        var endCoor = Cesium.Cartographic.fromCartesian(newPosition);
        var geodesic = new Cesium.EllipsoidGeodesic();
        geodesic.setEndPoints(centreCoor, endCoor);
        var distance = geodesic.surfaceDistance;
        _this.radius = parseFloat(distance.toFixed(3));
        console.log(_this.radius, 'iiiiiiiiiiiiiiii');
      }
    });
    this.viewer = viewer;
    this.plotHandler = null;
    this.myCollection = null;
    this.yourCollection = null;

    // 点集和线是分开绘制的，所以为左键点集合声明一个CustomDataSource
    this.tempPointArr;
    this.floatingPoint = undefined;
    // this.poiArr = [];
    this.radius = 1;
    this.centre = null;
    this.circleEntity = undefined; //当前正在绘制的  随着鼠标移动变化的线
    this.init(this.viewer);
    this.jsonCollection = []; // 用json组织数据  json结构需要统一
    // class extend 形式 写个绘制基类  其他的线、面都去extend
  }
  return _createClass(PlotCircle, [{
    key: "init",
    value: function init(viewer) {
      this.myCollection = new Cesium.CustomDataSource('myplotdata');
      this.yourCollection = new Cesium.CustomDataSource('yourplotdata');
      this.viewer.dataSources.add(this.myCollection);
      this.viewer.dataSources.add(this.yourCollection);
      this.tempPointArr = new Cesium.CustomDataSource('poiPolygonData');
      this.viewer.dataSources.add(this.tempPointArr);
    }
  }, {
    key: "draw",
    value: function draw() {
      var _this2 = this;
      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {
        picType: 'p1111',
        czType: '',
        busiType: ''
      };
      this.plotHandler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
      this.plotHandler.setInputAction(this.leftEvent(options), Cesium.ScreenSpaceEventType.LEFT_CLICK);
      this.plotHandler.setInputAction(this.moveEvent, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      return new Promise(function (resolve, reject) {
        _this2.plotHandler.setInputAction(_this2.rightEvent(resolve, reject, options), Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      });
    }
  }, {
    key: "leftEvent",
    value: function leftEvent(options) {
      var _this3 = this;
      var r = function r(e) {
        var earthPosition = getCoorFromPx({
          viewer: _this3.viewer,
          pxCoor: e.position,
          resultType: 'xyz'
        });
        if (Cesium.defined(earthPosition)) {
          if (_this3.centre === null) {
            _this3.floatingPoint = _this3.createPoint(earthPosition, '左键绘制，右键结束');
            _this3.viewer.entities.add(_this3.floatingPoint);
            _this3.centre = earthPosition;
            var dynamicPositions = new Cesium.CallbackProperty(function () {
              return _this3.radius;
            }, false);
            // circle的这个cb回调要写在外面
            _this3.circleEntity = _this3.createCircle(_this3.centre, dynamicPositions, options);
            _this3.myCollection.entities.add(_this3.circleEntity);
            var tempPoint = _this3.createPoint(earthPosition);
            _this3.tempPointArr.entities.add(tempPoint);
          }
        }
      };
      return r;
    }
  }, {
    key: "rightEvent",
    value: function rightEvent(resolve, reject, options) {
      var _this4 = this;
      var r = function r(e) {
        if (_this4.centre === null) {
          return;
        }
        var Position = getCoorFromPx({
          viewer: _this4.viewer,
          pxCoor: e.position,
          resultType: 'xyz'
        });
        if (Cesium.defined(Position)) {
          var endPoint = _this4.createPoint(Position, '');
          _this4.tempPointArr.entities.add(endPoint);
          _this4.viewer.entities.remove(_this4.floatingPoint);
          _this4.floatingPoint = null;
          var singleC = _this4.centre;
          var singleR = _this4.radius;
          var singleEntity = _this4.createCircle(singleC, singleR, options);
          _this4.myCollection.entities.add(singleEntity);
          _this4.myCollection.entities.remove(_this4.circleEntity);
          _this4.circleEntity = null;
          _this4.centre = null;
          _this4.radius = null;
          _this4.tempPointArr.entities.removeAll();
          var plotInfo = {
            name: 'xxx',
            position: singleC,
            radius: singleR,
            czType: options.czType,
            busiType: options.busiType,
            picType: options.picType,
            entityOptions: options
          };
          _this4.destroyHandler();
          resolve(plotInfo);
          reject('err----');
        }
      };
      return r;
    }
  }, {
    key: "reDraw",
    value: function reDraw(options) {
      var e = this.createCircle(options.position, options.radius, options.entityOptions);
      this.yourCollection.entities.add(e);
    }
  }, {
    key: "createCircle",
    value: function createCircle(position, r, options) {
      /* let semi = new Cesium.CallbackProperty(() => {
      	return radius;
      }, false); */
      var shape = new Cesium.Entity({
        position: position,
        // position: Cesium.Cartesian3.fromDegrees(113.0, 25.0),
        ellipse: {
          semiMinorAxis: r,
          semiMajorAxis: r,
          /* semiMinorAxis: new Cesium.CallbackProperty(() => {
          	return r;
          }, false),
          semiMajorAxis: new Cesium.CallbackProperty(() => {
          	return r; 
          }, false),*/
          fill: true,
          material: Cesium.Color.fromCssColorString('rgb(20, 120, 255)').withAlpha(0.33)
        }
      });
      return shape;
    }
  }, {
    key: "createPoint",
    value: function createPoint(poi, txt) {
      var point = new Cesium.Entity({
        position: poi,
        point: {
          color: Cesium.Color.YELLOW.withAlpha(0.6),
          pixelSize: 6
          // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        },
        label: {
          text: txt || '',
          font: 'bold 12px MicroSoft YaHei',
          outlineWidth: 2,
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          verticalOrigin: Cesium.VerticalOrigin.TOP,
          pixelOffset: new Cesium.Cartesian2(15, 0),
          // pixelOffset: new Cesium.Cartesian2(-15, 16), //偏移量
          showBackground: true,
          backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
          backgroundPadding: new Cesium.Cartesian2(6, 3),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      });
      return point;
    }
  }, {
    key: "getPlotData",
    value: function getPlotData() {
      return this.jsonCollection;
    }
  }, {
    key: "setPlotData",
    value: function setPlotData(data) {
      this.reDraw(data);

      /* for (const i of data) {
      	this.reDraw(i);
      } */
    }
  }, {
    key: "clearData",
    value: function clearData() {
      this.myCollection.entities.removeAll();
      this.yourCollection.entities.removeAll();
    }
  }, {
    key: "destroyHandler",
    value: function destroyHandler() {
      // this.plotHandler.removeInputAction(
      // 	Cesium.ScreenSpaceEventType.LEFT_CLICK
      // );

      this.plotHandler.destroy();
      this.plotHandler = null;
    }
  }]);
}();

export { PlotBillBoard, PlotCircle, PlotLine, PlotPolygon, PlotRectangle };
