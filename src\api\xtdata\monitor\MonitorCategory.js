import request from '@/utils/request'

// 查询监测大类
export function getMonitorCategory(query) {
  return request({
    url: '/mine/monitoringMajorCategories',
    method: 'get',
    params: query
  })
}



export function addMonitorCategory(data) {
  return request({
    url: '/mine/monitoringMajorCategories',
    method: 'post',
    data: data
  })
}


export function updateMonitorCategory(data) {
  return request({
    url: '/mine/monitoringMajorCategories',
    method: 'post',
    data: data
  })
}


export function delMonitorCategory(ids) {
  return request({
    url: '/mine/monitoringMajorCategories',
    method: 'delete',
    data: ids
  })
}
