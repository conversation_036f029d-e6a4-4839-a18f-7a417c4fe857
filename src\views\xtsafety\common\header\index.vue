<template>
  <div class="header">
    <div class="name">{{ name }}</div>
    <slot name="menu" class="menu"></slot>
    <slot name="userInfo" class="userinfo"></slot>
  </div>
</template>

<script setup>
const props = defineProps({
  name: {
    type: String,
    required: true,
    default: "应急综合平台",
  },
});

onMounted(() => {});
</script>

<style scoped lang="scss">
.header {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  width: 100%;
  height: 90px;
  background: url("@/assets/xtui/layout/headernewbg.png");
  background-size: 100% 100%;
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  column-gap: 20px;
  align-items: center;
  .name {
    margin-left: 20px;
    font-family: ysbthzt;
    font-size: 45px;

    letter-spacing: 0em;

    background: linear-gradient(90deg, #ffffff 58%, #97add4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    text-shadow: 0px 2px 4px #78a1c496;
  }
}
</style>
