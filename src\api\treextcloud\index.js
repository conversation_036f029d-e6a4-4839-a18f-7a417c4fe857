import request from "@/utils/request";
// 摄像头列表
export async function getVideoTree() {
  return request({
    url:  (`/monitorVideo/tree`),
    method: "get",
  });
}

// 视频地址
export async function getVideoUrl(params) {
  return request({
    url:  (`/monitorVideo/pass`),
    params:params,
    method: "get",
  });
}
// 应急企业树
export async function getCompanyTree() {
  return request({
    url: (`/resources/secure-base/enterprise?userId=1&name=`),
    method: "get",
  });
}
// 应急资源树
export async function getResourceTree() {
  return request({
    url: (`/resources/tree`),
    method: "get",
  });
}
// 人力资源查询
export async function getResourcesExpert(id) {
  return request({
    url: (`/resources/expert/${id}`),
    method: "get",
  });
}

// 队伍资源查询
export async function getResourcesTeam(id) {
  return request({
    url: (`/resources/team/${id}`),
    method: "get",
  });
}

// 物资资源请求
export async function getResourcesGoods(id) {
  return request({
    url: (`/resources/goods/${id}`),
    method: "get",
  });
}
// 避难场所请求
export async function getResourcesRefuge(id) {
  return request({
    url: (`/resources/refuge/${id}`),
    method: "get",
  });
}
// 医疗机构请求
export async function getResourcesMedical(id) {
  return request({
    url: (`/resources/medical/${id}`),
    method: "get",
  });
}


// 仓库请求
export async function getResourcesStore(id) {
  return request({
    url: (`/resources/store/${id}`),
    method: "get",
  });
}



