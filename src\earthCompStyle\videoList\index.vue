<template>
  <div class="cz_layers">
    <!-- 搜索框 -->
    <el-input v-model="filterText" style="width: 100%" placeholder="搜索" />
    <div class="czlc_tree">
      <!-- 树形结构 -->
      <el-tree
        :data="layerTreeData"
        show-checkbox
        default-expand-all
        node-key="id"
        ref="tree"
        highlight-current
        :props="defaultProps"
        @check-change="handleCheckChange"
        :filter-node-method="filterNode"
        :render-content="renderContent"
        :check-strictly="true"
        :node-disabled="isNodeDisabled"
      >
      </el-tree>
    </div>

    <!-- 弹出框：摄像头详情 -->
    <PopupSlot
      v-if="isPopShow"
      ref="popupRef"
      id="popup-info-camera"
      @closePopup="closePopup"
      popupTitle="在线监控"
    >
      <template #content>
        <div class="video-player">
          <easy-player
            v-if="isPopShow"
            :video-url="videoUrl"
            :live="true"
            style="width: 100%; height: 100%"
          />
        </div>
      </template>
    </PopupSlot>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { getVideoTree, getVideoUrl } from "@/api/tree/index";
import PoiUtils from "./PoiUtils";
import PopupSlot from "./PopupSlot";
const videoElement = ref(null);
const EasyPlayerplayer = ref();
const videoUrl = ref("");
const filterText = ref("");
const tree = ref(null);
const layerTreeData = ref([]);
const isPopShow = ref(false);
const popupItem = ref({});
let hls = null;
let postRenderingFn = null; // 不再用reactive，直接用简单函数存储
let poiCollectionArr = reactive([]);

const defaultProps = {
  children: "children",
  label: "name",
};

// 自定义渲染节点内容
const renderContent = (h, { node, data }) => {
  const maxLength = 8; // 最大长度
  const text = node.label;
  const truncatedText = text.length > maxLength ? text.slice(0, maxLength) + "..." : text;

  return h("span", [
    h(
      "span",
      {
        style: {
          overflow: "hidden",
          whiteSpace: "nowrap",
          textOverflow: "ellipsis",
        },
      },
      truncatedText
    ), // 显示截断后的文本
    node.isLeaf // 只有叶子节点才显示在线状态
      ? data.status === "online"
        ? h("span", { style: { color: "#0fff0f", marginLeft: "10px" } }, "在线")
        : h("span", { style: { color: "red", marginLeft: "10px" } }, "离线")
      : null, // 非叶子节点不显示在线状态
  ]);
};

// 动态控制节点是否禁用勾选框
const isNodeDisabled = (data, node) => {
  console.log(data, "data");
  // 如果节点是叶子节点并且离线（online 为 false），则禁用复选框
  if (node.isLeaf && data.status != "online") {
    return true;
  }
  return false;
};
// 监听过滤文本变化
watch(filterText, (val) => tree.value.filter(val));

// 过滤节点
const filterNode = (value, data) => !value || data.name.includes(value);

// 初始化树形数据
const initTree = async () => {
  try {
    const res = await getVideoTree();
    if (res.code === 200) layerTreeData.value = res.data;
  } catch (err) {
    console.error("Error fetching video tree:", err);
  }
};

// 处理复选框变化
const handleCheckChange = async (data, checked) => {
  if (checked && !data.children.length) {
    const tempObj = createPoiSource(data);
    poiCollectionArr.push(tempObj);
    await getAndAddPoi(data, tempObj[data.id]);
    window.viewer.dataSources.add(tempObj[data.id]);
    setTimeout(() => PoiUtils.addListener(window.viewer, eventCallback), 200);
  } else if (!checked && !data.children.length) {
    handlePoiRemoval(data.id);
  }
};

// 创建并添加POI
const createPoiSource = (data) => ({
  [data.id]: new Cesium.CustomDataSource(data.id),
  onlyID: data.id,
});

// 获取并添加POI
const getAndAddPoi = async (data, collection) => {
  const e_poi = PoiUtils.createPoi({
    center: [data.longitude, data.latitude],
    pickName: "camerapick",
    ptype: "camera",
    labelName: data.name,
    properties: data,
  });
  collection.entities.add(e_poi);
  flyToLocation(data.longitude, data.latitude);
};

// 移动到目标位置
const flyToLocation = (longitude, latitude) => {
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, 2000.0),
    orientation: {
      heading: Cesium.Math.toRadians(20.0),
      pitch: Cesium.Math.toRadians(-90.0),
      roll: 0,
    },
    duration: 2,
  });
};

// 处理POI移除
const handlePoiRemoval = (id) => {
  const poiC = poiCollectionArr.find((item) => item.onlyID === id);
  if (!poiC) return;
  poiC[id].entities.removeAll();
  window.viewer.dataSources.remove(toRaw(poiC[id]), true);
  poiCollectionArr.splice(poiCollectionArr.indexOf(poiC), 1);
  if (isPopShow.value) isPopShow.value = false;
  postRenderingFn?.(); // 调用后销毁回调
};

// 关闭弹窗
const closePopup = () => {
  isPopShow.value = false;
  hls?.destroy();
};

// 事件回调处理
const eventCallback = (viewer, pick) => {
  postRenderingFn?.(); // 调用并销毁
  if (pick.id.name === "camerapick" && pick.id.position) {
    popupItem.value = pick.id.properties;
    getHLSURL(popupItem.value, viewer, pick);
  }
};
// 获取HLS视频流地址
const getHLSURL = async (item, viewer, pick) => {
  try {
    const params = {
      devicePath: `${item.device_path._value}/${item.uid._value}`,
      disableAudio: true,
      request: "open.video.HLS",
      videoQuality: 1,
    };
    const res = await getVideoUrl(params);
    if (res.code === 200) {
      nextTick(() => {
        videoUrl.value = res.data.url;
        isPopShow.value = true;
        setupPopupPosition(viewer, pick.id.position._value);
      });
    }
  } catch (err) {
    ElMessage({
      message: "设备离线！",
      type: "error",
      plain: true,
    });
  }
};
// 设置弹窗位置
const setupPopupPosition = (viewer, position) => {
  nextTick(() => {
    const popDom = document.querySelector("#popup-info-camera");
    const height = popDom?.offsetHeight;
    postRenderingFn = viewer.scene.postRender.addEventListener(() => {
      const screenC = viewer.scene.cartesianToCanvasCoordinates(position);
      if (screenC && popDom?.style) {
        popDom.style.left = `${screenC.x - 10}px`;
        popDom.style.top = `${screenC.y - height - 25}px`;
      }
    });
  });
};
onMounted(initTree);
// onBeforeUnmount(() => EasyPlayerplayer.value?.destroy());
</script>

<style scoped lang="scss">
.cz_layers {
  width: 260px;
  height: max-content;
  padding: 10px 15px;

  :deep(.el-input__wrapper) {
    background-color: transparent;
    border: 1px solid rgb(90, 174, 226);
    box-shadow: none;
    border-radius: 0;
  }

  :deep(.el-input__inner) {
    color: #fff;
  }
}

.czlc_tree {
  .el-tree {
    padding: 10px 0px;
    width: 100%;
    min-height: 500px;
    color: #cce3ff;
    background: rgba(26, 69, 86, 0);
    max-height: 18rem;
    overflow: auto;
    border: 0px solid rgb(53, 183, 234);
    font-size: 12px;
    font-weight: bold;
  }

  :deep(.el-tree-node__content:hover) {
    background: transparent !important;
    color: rgb(96, 167, 230);
  }

  :deep(.el-tree > .el-tree-node > .el-tree-node__content) {
    background: transparent !important;
    height: 30px;
  }

  :deep(.el-tree > .el-tree-node:focus > .el-tree-node__content) {
    background: transparent !important;
  }

  :deep(.el-tree
      > .el-tree-node
      > .el-tree-node__children
      > .el-tree-node
      > .el-tree-node__content) {
    background: transparent !important;
    height: 30px;
  }

  :deep(.el-tree
      > .el-tree-node
      > .el-tree-node__children
      > .el-tree-node
      > .el-tree-node__children
      > .el-tree-node
      > .el-tree-node__content) {
    background: transparent !important;
    padding: 5px;
    height: 30px;
    :hover {
      background: transparent !important;
      color: rgb(96, 167, 230);
    }
  }
}

.video-player {
  width: 375px;
  height: 160px;
}

video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
