<template>
  <div class="left-base">
    <div>left</div>
    <div>left</div>
    <div>left</div>
    <div>left</div>
    <div>left</div>
    <div>left</div>
    <el-button type="primary" size="small">222</el-button>
  </div>
</template>

<script setup>
import { nextTick, ref, reactive, computed, onMounted } from "vue";
const state = reactive({ count: 0 });

function increment() {
  setTimeout(() => {}, 1000);
  state.count += 3;

  nextTick(() => {
    // 访问更新后的 DOM
    let z = document.querySelector("#btn");
  });
}
const obj = reactive({
  nested: { count: 0 },
  arr: ["foo", "bar"],
});

const firstName = ref("John");
const lastName = ref("Doe");

const fullName = computed({
  // getter
  get() {
    return firstName.value + " " + lastName.value;
  },
  // setter
  set(newValue) {
    [firstName.value, lastName.value] = newValue.split(" ");
  },
});

const isActive = ref(true);
const hasError = ref(false);
const classObject = reactive({
  active: true,
  "text-danger": false,
});

const isActiveX = ref(true);
const error = ref(null);
// 我们也可以绑定一个返回对象的计算属性。这是一个常见且很有用的技巧：
const classObjectX = computed(() => ({
  active: isActive.value && !error.value,
  "text-danger": error.value && error.value.type === "fatal",
}));

const activeColor = ref("red");

// ==================================== calendar ==== menu ==============================================

const monthValue = ref("");
const monthData = reactive([
  {
    value: 10,
    label: "label-10",
  },
  {
    value: 11,
    label: "label-11",
  },
  {
    value: 12,
    label: "label-12",
  },
]);
monthValue.value = monthData[1].value;

onMounted(() => {});
</script>

<style scoped lang="scss">
.left-base {
  background-color: rgba(205, 163, 127, 0.315);
  height: 100vh;
  width: 450px;

  background: url("@/assets/xtui/layout/leftbg.png");
  background-size: 100% 100%;
}
</style>

