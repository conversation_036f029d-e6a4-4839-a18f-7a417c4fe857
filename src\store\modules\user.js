import { login, logout, getInfo } from '@/api/login'
import { tenantProperties, listTenant } from "@/api/system/tenant";
import { getToken, setToken, removeToken, getTenantId, setTenantId, removeTenantId } from '@/utils/auth'
import defAva from '@/assets/images/profile.jpg'

const useUserStore = defineStore(
  'user',
  {
    state: () => ({
      token: getToken(),
      id: '',
      name: '',
      avatar: '',
      roles: [],
      permissions: [],
      tenantId: getTenantId(),
      tenantPropertiesData: {},
    }),
    actions: {
      // 登录
      login(userInfo) {
        const username = userInfo.username.trim()
        const password = userInfo.password
        const code = userInfo.code
        const uuid = userInfo.uuid
        const tenantId = userInfo.tenantId
        return new Promise((resolve, reject) => {
          login(username, password, code, uuid, tenantId).then(res => {
            let data = res.data
            setToken(data.access_token)
            this.token = data.access_token
            setTenantId(tenantId)
            this.tenantId = tenantId
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 获取用户信息
      getInfo() {
        return new Promise((resolve, reject) => {
          getInfo().then(res => {
            const user = res.user
            const avatar = (user.avatar == "" || user.avatar == null) ? defAva : user.avatar;

            if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
              this.roles = res.roles
              this.permissions = res.permissions
            } else {
              this.roles = ['ROLE_DEFAULT']
            }
            this.id = user.userId
            this.name = user.userName
            this.avatar = avatar
            resolve(res)
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 获取租户信息
      getTenantInfo(islogin = true) {
        return new Promise((resolve, reject) => {
          tenantProperties().then((res) => {
            this.tenantPropertiesData = res.data;
            if(this.tenantPropertiesData.enable) {
              const queryParams = {
                status: "1",
                pageNum: -1,
                pageSize: -1,
                userId: islogin ? this.id : undefined
              };
              return listTenant(queryParams).then((res) => {
                this.tenantPropertiesData.tenantList = res.rows;
                resolve(this.tenantPropertiesData); // 返回完整的数据
              });
            } else {
              resolve(this.tenantPropertiesData);
            }
          }).catch(error => {
            reject(error)
          });
        });
      },
      // 切换租户
      switchTenant(tenantId) {
        setTenantId(tenantId)
        this.tenantId = tenantId
      },
      // 退出系统
      logOut() {
        return new Promise((resolve, reject) => {
          logout(this.token).then(() => {
            this.token = ''
            this.roles = []
            this.permissions = []
            removeToken()
            removeTenantId()
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      }
    }
  })

export default useUserStore
