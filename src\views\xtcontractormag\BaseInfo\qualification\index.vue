<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="auto">
            <el-form-item label="资质名称" prop="qualificationName">
                <el-input v-model="queryParams.qualificationName" placeholder="请输入资质名称" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="资质等级" prop="qualificationLevel">
                <el-select v-model="queryParams.qualificationLevel" placeholder="请选择资质等级" clearable
                    style="width: 214px;">
                    <el-option v-for="dict in cbs_qualification_level" :key="dict.value" :label="dict.label"
                        :value="dict.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="证书编号" prop="certificateNumber">
                <el-input v-model="queryParams.certificateNumber" placeholder="请输入证书编号" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证书有效期起" prop="certificateValidityStart">
                <el-date-picker clearable v-model="queryParams.certificateValidityStart" type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" placeholder="请选择证书有效期起">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="证书有效期止" prop="certificateValidityEnd">
                <el-date-picker clearable v-model="queryParams.certificateValidityEnd" type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" placeholder="请选择证书有效期止">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="承包商名称" prop="basicId">
                <el-select v-model="queryParams.basicId" placeholder="请选择承包商" clearable>
                    <el-option v-for="item in contractorList" :key="item.id" :label="item.contractorName"
                        :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['contractor:qualification:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['contractor:qualification:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="Upload" @click="handleImport"
                    v-hasPermi="['contractor:qualification:import']">导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport"
                    v-hasPermi="['contractor:qualification:export']">导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="importTemplate">下载模板</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="qualificationList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="资质名称" align="center" prop="qualificationName" show-overflow-tooltip />
            <el-table-column label="承包商名称" align="center" prop="basicId" show-overflow-tooltip />
            <el-table-column label="资质等级" align="center" prop="qualificationLevel" show-overflow-tooltip>
                <template #default="scope">
                    <dict-tag :options="cbs_qualification_level" :value="scope.row.qualificationLevel" />
                </template>
            </el-table-column>
            <el-table-column label="证书编号" align="center" prop="certificateNumber" show-overflow-tooltip />
            <el-table-column label="证书有效期起" align="center" prop="certificateValidityStart" width="180"
                show-overflow-tooltip>
                <template #default="scope">
                    <span>{{ parseTime(scope.row.certificateValidityStart, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="证书有效期止" align="center" prop="certificateValidityEnd" width="180"
                show-overflow-tooltip>
                <template #default="scope">
                    <span>{{ parseTime(scope.row.certificateValidityEnd, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="附件" align="center" prop="attachmentUrl" show-overflow-tooltip />
            <el-table-column label="资质类型" align="center" prop="type" show-overflow-tooltip />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['contractor:qualification:edit']">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['contractor:qualification:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改承包商资质对话框 -->
        <el-dialog :title="title" v-model="open" width="800px" append-to-body>
            <el-form ref="qualificationRef" :model="form" :rules="rules" label-width="auto">
                <el-form-item label="资质名称" prop="qualificationName">
                    <el-input v-model="form.qualificationName" placeholder="请输入资质名称" />
                </el-form-item>
                <el-form-item label="承包商名称" prop="basicId">
                    <el-select v-model="form.basicId" placeholder="请选择承包商" filterable>
                        <el-option v-for="item in contractorList" :key="item.id" :label="item.contractorName"
                            :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="资质名称" prop="qualificationName">
                    <el-input v-model="form.qualificationName" placeholder="请输入资质名称" />
                </el-form-item>
                <el-form-item label="资质等级" prop="qualificationLevel">
                    <el-select v-model="form.qualificationLevel" placeholder="请选择资质等级">
                        <el-option v-for="dict in cbs_qualification_level" :key="dict.value" :label="dict.label"
                            :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="证书编号" prop="certificateNumber">
                    <el-input v-model="form.certificateNumber" placeholder="请输入证书编号" />
                </el-form-item>
                <el-form-item label="证书有效期起" prop="certificateValidityStart">
                    <el-date-picker clearable v-model="form.certificateValidityStart" type="datetime"
                        value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" placeholder="请选择证书有效期起">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="证书有效期止" prop="certificateValidityEnd">
                    <el-date-picker clearable v-model="form.certificateValidityEnd" type="datetime"
                        value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" placeholder="请选择证书有效期止">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="附件" prop="attachmentUrl">
                    <file-upload v-model="form.attachmentUrl" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 导入对话框 -->
        <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
            <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
                :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
                :auto-upload="false" drag>
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitFileForm">确 定</el-button>
                    <el-button @click="upload.open = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Qualification">
import { listBasic } from "@/api/xtcontractormag/BaseInfo/basic";
import { addQualification, delQualification, getQualification, listQualification, updateQualification } from "@/api/xtcontractormag/BaseInfo/qualification";

    import useUserStore from "@/store/modules/user";
import { deepClone, formatDate } from "@/utils";
import { getToken } from "@/utils/auth";

    const userStore = useUserStore();

    const { proxy } = getCurrentInstance();
        const { cbs_qualification_level } = proxy.useDict('cbs_qualification_level');

    const qualificationList = ref([]);
    const open = ref(false);
    const loading = ref(true);
    const showSearch = ref(true);
    const ids = ref([]);
    const itemsArr = ref([]);
    const multiple = ref(true);
    const total = ref(0);
    const title = ref("");
const contractorList = ref([]);
// 获取承包商列表
function getContractorList() {
    listBasic().then(response => {
        contractorList.value = response.rows;
    });
}

    /*** 用户导入参数 */
    const upload = reactive({
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: window.xtmapConfig.xtBaseUrl + "/contractor/qualification/importData",
    });

    const data = reactive({
        form: {},
        queryParams: {
            pageNum: 1,
            pageSize: 10,
                        qualificationName: null,
                        qualificationLevel: null,
                        certificateNumber: null,
                        certificateValidityStart: null,
                        certificateValidityEnd: null,
                        attachmentUrl: null,
                        basicId: null,
                        type: null
        },
        rules: {
                        qualificationLevel: [
                        { required: true, message: "资质等级不能为空", trigger: "change" }
                    ],
                        certificateNumber: [
                        { required: true, message: "证书编号不能为空", trigger: "blur" }
                    ],
                        certificateValidityStart: [
                        { required: true, message: "证书有效期起不能为空", trigger: "blur" }
                    ],
                        certificateValidityEnd: [
                        { required: true, message: "证书有效期止不能为空", trigger: "blur" }
                    ],
                        attachmentUrl: [
                        { required: true, message: "附件url不能为空", trigger: "blur" }
                    ],
                        basicId: [
                        { required: true, message: "承包商基本信息id不能为空", trigger: "blur" }
                    ],
                        type: [
                        { required: true, message: "资质类型不能为空", trigger: "change" }
                    ]
        }
    });

    const { queryParams, form, rules } = toRefs(data);

    /** 查询承包商资质列表 */
    function getList() {
        loading.value = true;
        listQualification(queryParams.value).then(response => {
                qualificationList.value = response.rows;
            total.value = response.total;
            loading.value = false;
        });
    }

    // 取消按钮
    function cancel() {
        open.value = false;
        reset();
    }

    // 表单重置
    function reset() {
        form.value = {
                        id: null,
                        tenantId: null,
                        createBy: null,
                        createTime: null,
                        updateBy: null,
                        updateTime: null,
                        qualificationName: null,
                        qualificationLevel: null,
                        certificateNumber: null,
                        certificateValidityStart: null,
                        certificateValidityEnd: null,
                        attachmentUrl: null,
                        basicId: null,
                        deleted: null,
                        type: null
        };
        proxy.resetForm("qualificationRef");
    }

    /** 搜索按钮操作 */
    function handleQuery() {
        queryParams.value.pageNum = 1;
        getList();
    }

    /** 重置按钮操作 */
    function resetQuery() {
        proxy.resetForm("queryRef");
        handleQuery();
    }

    // 多选框选中数据
    function handleSelectionChange(selection) {
        ids.value = selection.map(item => item.id);
        itemsArr.value = deepClone(selection);
        multiple.value = !selection.length;
    }

    /** 导入按钮操作 */
    function handleImport() {
        upload.title = "导入";
        upload.open = true;
    }

    /** 下载模板操作 */
    function importTemplate() {
        proxy.download(
                "contractor/qualification/export/template",
                {},
                `qualification_${new Date().getTime()}.xlsx`
        );
    }

    /**文件上传中处理 */
    const handleFileUploadProgress = (event, file, fileList) => {
        upload.isUploading = true;
    };

    /** 文件上传成功处理 */
    const handleFileSuccess = (response, file, fileList) => {
        upload.open = false;
        upload.isUploading = false;
        proxy.$refs["uploadRef"].handleRemove(file);
        proxy.$alert(
                "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
                response.msg +
                "</div>",
                "导入结果",
                { dangerouslyUseHTMLString: true }
        );
        getList();
    };

    /** 提交上传文件 */
    function submitFileForm() {
        upload.url = `${window.xtmapConfig.xtBaseUrl}/contractor/qualification/importData?userId=${userStore.id}`;
        proxy.$refs["uploadRef"].submit();
    }

    /** 新增按钮操作 */
    function handleAdd() {
        reset();
        open.value = true;
        title.value = "添加承包商资质";
    }

    /** 修改按钮操作 */
    function handleUpdate(row) {
        reset();
        const _id = row.id || ids.value
        getQualification(_id).then(response => {
            form.value = response.data;
            open.value = true;
            title.value = "修改承包商资质";
        });
    }

    /** 提交按钮 */
    function submitForm() {
        proxy.$refs["qualificationRef"].validate(valid => {
            if (valid) {
                if (form.value.id != null) {
                    const data = {
                        ...form.value,
                        tenantId: userStore.tenantId,
                        updateBy: userStore.name,
                        updateTime: formatDate(new Date()),
                    };
                    updateQualification(data).then(response => {
                        proxy.$modal.msgSuccess("修改成功");
                        open.value = false;
                        getList();
                    });
                } else {
                    const data = {
                        ...form.value,
                        tenantId: userStore.tenantId,
                        createBy: userStore.name,
                        createTime: formatDate(new Date()),
                        updateBy: userStore.name,
                        updateTime: formatDate(new Date()),
                        deleted: '0'
                    };
                    addQualification(data).then(response => {
                        proxy.$modal.msgSuccess("新增成功");
                        open.value = false;
                        getList();
                    });
                }
            }
        });
    }

    /** 删除按钮操作 */
    function handleDelete(row) {
        const _ids = row.id ? [row.id] : ids.value;
        proxy.$modal.confirm('是否确认删除承包商资质编号为"' + _ids + '"的数据项？').then(function() {
            return delQualification(_ids);
        }).then(() => {
            getList();
            proxy.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    }

    /** 导出按钮操作 */
    function handleExport() {
        const params = itemsArr.value.length > 0 ? itemsArr.value : undefined;
        proxy.codeDownload(
                'contractor/qualification/export',
                params,
                `qualification_${new Date().getTime()}.xlsx`
        );
    }
getContractorList();
    getList();
</script>
