<template>
  <div class="news-list">
    <!-- style="width: 100%;   border -->
    <el-table
      ref="tableRef"
      :data="newsData"
      style="width: 95%; height: 86%"
      @row-click="rowclick"
      :row-style="tableRowStyle"
      :cell-style="cellStyle"
      :header-cell-style="tableHeaderColor"
    >
      <el-table-column
        prop="title"
        label="标题"
        align="center"
        width="90"
        :show-overflow-tooltip="true"
      />
      <el-table-column prop="sources" label="来源" align="center" width="90" />
      <el-table-column
        prop="publishTime"
        label="发布时间"
        align="center"
        :show-overflow-tooltip="true"
        width="90"
      />

      <el-table-column
        prop="url"
        label="地址"
        align="center"
        width="90"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <!-- <el-table-column prop="status" label="地址" align="center">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.status == '0'" size="small"
            >维修
          </el-tag>
          <el-tag type="info" v-else-if="scope.row.status == '1'" size="small"
            >正常</el-tag
          >
       
        </template>
      </el-table-column> -->
    </el-table>
  </div>
</template>
<script setup>
import { getNewsList, getResourceChart } from "@/api/command/index";
import { color } from "echarts";

const tableRef = ref(null); //表格实例

const newsData = ref([
  {
    title: "qq",
    sources: "qq",
    publishTime: "qqq",
    url: "qqq",
  },
]); //数据

const tagToName = (e) => {
  switch (e) {
    case "0":
      return "政府发布";
    case "1":
      return "社交媒体";
    case "2":
      return "新闻媒体";
    case "3":
      return "在线论坛";
    case "6":
      return "在线论坛";
    case "4":
      break;
  }
};
onMounted(() => {
  getNewsList().then((res) => {
    newsData.value = res.rows;
    for (const i of newsData.value) {
      i.sources = tagToName(i.sources);
    }
  });
  // scroll(tableRef.value.$refs.bodyWrapper); //设置滚动
});
const scroll = (tableBody) => {
  let isScroll = true; //滚动
  const tableDom = tableBody.getElementsByClassName("el-scrollbar__wrap")[0];
  //鼠标放上去，停止滚动；移开，继续滚动
  tableDom.addEventListener("mouseover", () => {
    isScroll = false;
  });
  tableDom.addEventListener("mouseout", () => {
    isScroll = true;
  });
  setInterval(() => {
    if (isScroll) {
      tableDom.scrollTop += 3; //设置滚动速度
      if (tableDom.clientHeight + tableDom.scrollTop == tableDom.scrollHeight) {
        tableDom.scrollTop = 0;
      }
    }
  }, 100);
};
const rowclick = (row, column, event) => {};

// ----------------------------表格样式------------------------
const tableRowStyle = (row) => {
  return row.rowIndex % 2 === 0
    ? { background: "rgba(14, 95, 255, 0)", height: "45px" }
    : // : { background: "rgba(14, 95, 255, 0.3)" };
      { background: "rgba(64, 158, 255, 0.1)", height: "45px" };
};
const cellStyle = {
  // background: "transparent",
  color: "#ffffff",
};
const tableHeaderColor = {
  background: "rgba(64, 158, 255, 0.3)",
  // background: "rgba(14, 95, 255, 0.2)",
  color: "#ffffff",
  fontSize: "14px",
  textAlign: "center",
};
</script>
<style lang="scss" scoped>
// 表格定制
.news-list {
  padding: 10px;
  height: 280px;
  :deep(.el-table) {
    background-color: transparent;

    // border-spacing: 0;
    // border-collapse: collapse;
  }
  :deep(.el-table tr) {
    background-color: transparent;
    background: rgba(14, 95, 255, 0.2);
  }
  :deep(.el-table th) {
    // background-color: transparent;
    // background-color: rgba(255, 106, 14, 0.2);
  }

  :deep(.el-table td) {
    // background-color: transparent !important;
    // border: oldlace 1px solid;
    // border-spacing: 0;
    // border-collapse: collapse;
  }

  :deep(.el-table__row) {
    --el-table-row-hover-bg-color: rgba(14, 95, 255, 0.4);
    // 表格边框的颜色，可以通过这个变量来设置表格的边框颜色。
    // --el-table-border-color: rgba(204, 206, 209, 0.4);
    // 表格边框的样式，一般为实线或虚线，可以通过这个变量来设置表格的边框样式。
    // --el-table-border: 1px solid rgba(239, 182, 182, 0.4);

    // 表格的背景色，可以通过这个变量来设置表格的背景色。
    // --el-table-bg-color: rgba(255, 255, 255, 0);
    // 表格行的背景色，可以通过这个变量来设置表格行的背景色。
    // --el-table-tr-bg-color: rgba(255, 255, 255, 0);
  }
}
</style>
