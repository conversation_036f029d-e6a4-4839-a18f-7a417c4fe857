import request from '@/utils/request'

// 查询监测大类
export function getMineDepartment(query) {
  return request({
    url: '/mine/mineDepartment',
    method: 'get',
    params: query
  })
}



export function addMineDepartment(data) {
  return request({
    url: '/mine/mineDepartment',
    method: 'post',
    data: data
  })
}


export function updateMineDepartment(data) {
  return request({
    url: '/mine/mineDepartment',
    method: 'post',
    data: data
  })
}


export function delMineDepartment(ids) {
  return request({
    url: '/mine/mineDepartment',
    method: 'delete',
    data: ids
  })
}
