import { dayjs } from 'element-plus';
import request from '@/utils/request';
import useUserStore from "@/store/modules/user";
const store = useUserStore();
export function addTimeEventNode(id,times,flow_timeline_data,nodeName, nodeObj, isSave = true) {
    // 保存节点
    const time = times;
    if (isSave) {
      if(nodeObj) {
        nodeObj.remark = nodeName
      } else {
        nodeObj = {
          remark: nodeName
        }
      }
      saveTimeEventNode(id,nodeObj, time,nodeObj.groupName);
    }
    _setTimeEventNodeNormal(flow_timeline_data);
    const node = {
      content: nodeName,
      timestamp: time,
      color: "#3CD5FF",
      icon: "",
    };
    flow_timeline_data.push(node);
}
export function saveTimeEventNode(task_id,content, curTime,groupName) {
	let mapContent = ""
    if(content.map) {
        mapContent = content.map
        delete content.map
    }
    //获取用户信息 通过store
	return request({
		url: `/drill/history`,
		method: 'post',
		data: {
            taskId: task_id,
            userId: store.name,
            userName: store.name,
            groupName: groupName != undefined ? groupName : 'ALL',
            textContent: JSON.stringify(content),
            mapContent,
            t0: curTime,
            time: 0,
        }
	});
}
export function _setTimeEventNodeNormal(data) {
    data.forEach((item) => {
        item.color = "#e4e7ed";
    });
}
export function getCurrentTime(time){
    const currentDate = dayjs().format("YYYY-MM-DD");
    return dayjs(`${currentDate} ${time}`).format(
      "YYYY-MM-DD HH:mm:ss"
    );
}