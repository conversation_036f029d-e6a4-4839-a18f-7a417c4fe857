<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from "echarts";
// require("echarts/theme/macarons"); // echarts theme
import resize from "../mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "380px",
    },
    height: {
      type: String,
      default: "226px",
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el);
      // this.chart = echarts.init(this.$el, "macarons");

      this.chart.setOption({
        tooltip: {
          trigger: "item",
        },
        legend: {
          // top: '1%',
          left: "center",
          show: false,
        },
        color: ["#FFAD60", "#FCDA99", "#AFE4E0", "#3D93EA", "#74C0FF"],
        series: [
          {
            name: "接入数据",
            type: "pie",
            radius: ["30%", "60%"],
            center: ["47%", "50%"],
            // avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              // label: {
              //   show: true,
              //   fontSize: 20,
              //   fontWeight: "bold",
              // },
            },
            labelLine: {
              show: true,
            },
            label: {
              show: true, // 显示标签
              position: "outside", // 在饼图内部显示标签
              formatter: "{b}:{d}%",
              color: "white", // 这里设置字体颜色为红色
              // textStyle: {},
            },
            data: [
              { value: 5, name: "消防监测" },
              { value: 3, name: "地质灾害" },
              { value: 2, name: "气象服务" },
              { value: 23, name: "企业信息" },
              { value: 6, name: "矿山信息" },
            ],
          },
        ],
      });
    },
  },
};
</script>
