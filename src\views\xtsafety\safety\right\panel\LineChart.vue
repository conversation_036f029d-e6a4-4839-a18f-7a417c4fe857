<template>
  <div
    :class="className"
    class="line-chart"
    :style="{ height: height, width: width }"
  ></div>
</template>

<script>
import * as echarts from "echarts";
// require("echarts/theme/macarons"); // echarts theme
import resize from "../mixins/resize";
import { getMonitorNum } from "@/api/cockpit/index";

/* //按月查询
export function getMonitorNum(params) {
    return request({
        url: '/bus/monitorWarning/countByTime',
        method: "get",
        params,
    });
} */

/*  */

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "95%",
    },
    height: {
      type: String,
      default: "150px",
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
    /* chartData: {
      type: Object,
      required: true,
    }, */
  },
  data() {
    return {
      chart: null,
      chartData: {},
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(newVal, oldVal) {
        this.initChart(newVal);
      },
    },
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.initChart();
    // });
    this.initData();
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el);
      this.chart.setOption({
        xAxis: {
          name: "月份",
          data: this.chartData.month,
          boundaryGap: true,
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: "white", // 修改x轴颜色为红色
            },
          },
        },
        yAxis: {
          axisTick: {
            show: false,
          },
          name: "次数",
          splitLine: {
            lineStyle: {
              color: "#1dabe2",
              type: "dashed", // solid dashed dotted
            },
          },
          axisLine: {
            lineStyle: {
              color: "white", // 修改x轴颜色为红色
            },
          },
        },
        grid: {
          left: 30,
          right: 40,
          bottom: 5,
          top: 30,
          containLabel: true,
        },
        tooltip: {
          show: false,
          trigger: "axis",
          axisPointer: {
            type: "cross",
          },
          textStyle: {
            color: "black",
          },
          padding: [5, 10],
        },

        series: [
          {
            name: "预警数量",
            /* label: {
              show: true,
              position: "top",
              textStyle: {
                color: "red", // 设置浮动标签的颜色为红色
              },
            }, */
            itemStyle: {
              normal: {
                color: "white",
                lineStyle: {
                  color: "white",
                  width: 2,
                },
              },
            },
            // smooth: true, //平滑
            type: "line",
            data: this.chartData.total_count,
            // animationDuration: 1000, //动画时间
            // animationEasing: "cubicInOut", //曲线
          },
        ],
      });
    },
    initData() {
      getMonitorNum().then((res) => {
        if (res.data !== null) {
          this.chartData.month = res.data.map((item) => item.month);
          this.chartData.total_count = res.data.map((item) => item.total_count);
        }
      });
    },
  },
};
</script>
<style scoped>
.line-chart {
  margin-top: 15px;
}
</style>
