<template>
  <div class="header">
    <div class="name">xxx综合平台</div>
    <div class="menu-main">
      <div
        class="menu-main-item"
        v-for="(item, index) in leftMenuData"
        @click="linkTo(item.path, index)"
        :key="index"
        :class="{ active: activeIndex === index }"
        @mouseenter="handleMouseEnter(index)"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="menu-right">
      <div class="menu-right-item" @click="linkToMid">业务中台</div>
    </div>
    <!-- <div class="menu-right">
      <div class="menu-right-item" v-hasPermi="['system:cockpit:sat']">sat</div>
      <div class="menu-right-item" v-hasPermi="['system:cockpit:uav']">uav</div>
      <div class="menu-right-item" v-hasPermi="['system:cockpit:ground']">ground111</div>
      <div class="menu-right-item" v-hasPermi="['system:cockpit:ground']">ground222</div>
    </div> -->
    <!-- <slot name="menu" class="menu"></slot> -->
    <!-- <slot name="userInfo" class="userinfo"></slot> -->
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
const router = useRouter();
const activeIndex = ref(0);
const leftMenuData = ref([
  {
    id: 1,
    name: "xx监测",
    path: "/xtopenpit/uav",
  },
  {
    id: 2,
    name: "xx监测",
    path: "/xtopenpit/sat",
  },
  {
    id: 3,
    name: "地基监测",
    path: "/xtopenpit/ground",
  },
  {
    id: 4,
    name: "监测预警",
    path: "/xtopenpit/uav",
  },
  {
    id: 5,
    name: "工程管理",
    path: "/xtopenpit/sat",
  },
  {
    id: 6,
    name: "数据管理",
    path: "/xtopenpit/ground",
  },
]);

const linkTo = (path, index) => {
  router.push(path);
};
const handleMouseEnter = (index) => {
  activeIndex.value = index; // 鼠标悬停时改变字体颜色
};

function linkToMid() {
  window.open(window.xtmapConfig.xtMidUrl, "_blank");
}
onMounted(() => {});
</script>

<style scoped lang="scss">
.header {
  z-index: 999;
  box-sizing: border-box;
  position: absolute;
  left: 0;
  top: 0;
  padding: 0;
  margin: 0;
  width: 100%;
  height: 103px;
  background: url("@/assets/xtui/layout/headernewbg.png");
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-items: center;
  // flex-direction: column;
  .name {
    height: 65px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-family: ysbthzt;
    font-size: 45px;
    letter-spacing: 0em;
    background: linear-gradient(90deg, #ffffff 58%, #97add4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    text-shadow: 0px 2px 4px #78a1c496;
  }

  .menu-main {
    width: 808px;
    height: 56px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    // background: rgba(26, 90, 153, 0.4);
    // backdrop-filter: blur(8.16px);
    border-radius: 4px;
    .menu-main-item {
      color: rgb(164, 166, 168);
      cursor: pointer;
      font-family: Alibaba PuHuiTi 2;
      font-size: 20px;
      font-weight: 700;
    }
    .menu-main-item:hover {
      color: white;
    }

    .active {
      color: white;
    }
  }
  .menu-right {
    position: absolute;
    right: 100px;
    top: 10px;
    background-color: #97add4;
    height: 40px;
    display: flex;
    align-items: center;
  }
}
</style>
