import MD5 from "md5";

export function getSign(url) {
  let time = Date.parse(new Date());
  let userId = 1;
  let secretkey = "kQwIOrYvnXmSDkwEiFngrKidMcdrgKor";
  let nonce = Math.random().toString(36).slice(-8);
  let sign = MD5(
    `loginId=${userId}&nonce=${nonce}&timestamp=${time}&key=${secretkey}`
  );
  let tag = url.indexOf("?") == -1 ? "?" : "&";
  const xtUrl = `${url}${tag}loginId=${userId}&nonce=${nonce}&timestamp=${time}&sign=${sign}`;
  return xtUrl
}
