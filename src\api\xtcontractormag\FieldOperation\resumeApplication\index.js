import request from '@/utils/request'

// 查询承包商复工申请列表
export function listResumeApplication(query) {
  return request({
    url: '/contractor/resumeApplication',
    method: 'get',
    params: query
  })
}

// 查询承包商复工申请详细
export function getResumeApplication(id) {
  return request({
    url: '/contractor/resumeApplication/' + id,
    method: 'get'
  })
}

// 新增承包商复工申请
export function addResumeApplication(data) {
  return request({
    url: '/contractor/resumeApplication',
    method: 'post',
    data: data
  })
}

// 修改承包商复工申请
export function updateResumeApplication(data) {
  return request({
    url: '/contractor/resumeApplication',
    method: 'post',
    data: data
  })
}

// 删除承包商复工申请
export function delResumeApplication(data) {
  return request({
    url: '/contractor/resumeApplication/',
    method: 'delete',
    data: data
  })
}
