class DrawRectangle {
	constructor() {
		this.activeShapePoints = [];
		this.activeShape = undefined;
		this.floatingPoint = undefined;
		this.finalEntityId = undefined;
		this.fourCoor = {};
		this.windowCoor = {};
		this.drawRectangle();
	}

	//绘制矩形
	drawRectangle() {
		let _this = this;
		let xlon;
		let xlat;
		let handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
		handler.setInputAction(function (event) {
			if (!Cesium.Entity.supportsPolylinesOnTerrain(viewer.scene)) {
				console.log(
					'This browser does not support polylgons on terrain.'
				);
				return;
			}
			let earthPosition = viewer.camera.pickEllipsoid(event.position);
			//在地形上绘制时
			// let earthPosition = viewer.scene.pickPosition(event.position);
			if (Cesium.defined(earthPosition)) {
				let cartographic =
					Cesium.Cartographic.fromCartesian(earthPosition);
				xlon = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
				xlat = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);

				if (_this.activeShapePoints.length === 0) {
					_this.fourCoor.minLon = parseFloat(xlon);
					_this.fourCoor.maxLat = parseFloat(xlat);
					_this.windowCoor.minX = parseFloat(event.position.x);
					_this.windowCoor.minY = parseFloat(event.position.y);

					_this.floatingPoint = _this.createPoint(earthPosition);
					_this.activeShapePoints.push(earthPosition);
					let dynamicPositions = new Cesium.CallbackProperty(
						function () {
							return Cesium.Rectangle.fromCartesianArray(
								_this.activeShapePoints
							);
						},
						false
					);
					_this.activeShape = _this.createRectangle(dynamicPositions);
					_this.activeShapePoints.push(earthPosition);
				} else {
					_this.createPoint(earthPosition);
					_this.fourCoor.maxLon = parseFloat(xlon);
					_this.fourCoor.minLat = parseFloat(xlat);
					_this.windowCoor.maxX = event.position.x;
					_this.windowCoor.maxY = event.position.y;

					_this.activeShapePoints.push(earthPosition);
					_this.terminateRectangle();
					viewer.entities.remove(_this.activeShape);
					viewer.entities.remove(_this.floatingPoint);
					handler.destroy();
					setTimeout(() => {
						/* PlotBus.$emit('DrawRectangle_finish', {
							windowCoor: _this.windowCoor,
							fourCoor: _this.fourCoor,
						}); */
						emitter.emit('DrawRectangle_finish', {
							windowCoor: _this.windowCoor,
							fourCoor: _this.fourCoor,
						});
					}, 100);
				}
			}
		}, Cesium.ScreenSpaceEventType.LEFT_CLICK);

		handler.setInputAction(function (event) {
			if (Cesium.defined(_this.floatingPoint)) {
				let newPosition = viewer.camera.pickEllipsoid(
					event.endPosition
				);
				//在地形上绘制时
				// let newPosition = viewer.scene.pickPosition(event.endPosition);
				if (Cesium.defined(newPosition)) {
					_this.floatingPoint.position.setValue(newPosition);
					_this.activeShapePoints.pop();
					_this.activeShapePoints.push(newPosition);
				}
			}
		}, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
	}

	//创建点
	createPoint(worldPosition) {
		let point = viewer.entities.add({
			position: worldPosition,
			point: {
				// color: Cesium.Color.WHITE,
				color: Cesium.Color.WHITE.withAlpha(0.01),
				pixelSize: 2,
				heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
			},
		});
		return point;
	}

	//创建矩形
	createRectangle(positionData) {
		let shape = viewer.entities.add({
			name: 'Plot_Rectangle',
			rectangle: {
				coordinates: positionData,
				fill: true,
				material: Cesium.Color.DEEPSKYBLUE.withAlpha(0.3),
				outline: true,
				outlineWidth: 1,
				outlineColor: Cesium.Color.DEEPSKYBLUE,
				height: 0,
			},
		});
		return shape;
	}

	//鼠标右击结束绘制
	terminateRectangle() {
		this.activeShapePoints.pop();
		// let entity = this.createRectangle(Cesium.Rectangle.fromCartesianArray(this.activeShapePoints));
		// this.finalEntityId = entity.id;
		viewer.entities.remove(this.floatingPoint);
		viewer.entities.remove(this.activeShape);
		this.floatingPoint = undefined;
		this.activeShape = undefined;
		// console.log('eeeeeeeeeeennnnnnnnnnnn');
		this.activeShapePoints = [];
	}
}

export default DrawRectangle;
