<template>
  <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="230"
      >
        <template slot-scope="scope" v-if="scope.row.roleId !== 1">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-link"
            :disabled="scope.row.status == '3'"
            @click="handleEnter(scope.row)"
            v-hasPermi="['xtcommond:command:handle']"
            >处置</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['xtcommond:command:view']"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['xtcommond:command:edit']"
            >编辑</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['xtcommond:command:delete']"
            >删除</el-button
          >
        </template>
      </el-table-column>
</template>

<script>
export default {

}
</script>

<style>

</style>