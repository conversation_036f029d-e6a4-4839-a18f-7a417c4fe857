<template>
  <div class="left-base">
    <div class="left-menu">
      <el-button @click="send">发消息</el-button>
    </div>
  </div>
</template>

<script setup>
import { nextTick, ref, reactive, computed, onMounted } from "vue";
const state = reactive({ count: 0 });

import { sendRmqMsg } from "@/api/drill/common/index.js";
import { getMyKey } from "../../common/js/tool";
// 获取xttid

const xttid = getMyKey("xttid");

function send() {
  const mapContent = JSON.stringify(window.plotPlugin.get());
  const msgData = reactive({
    exchange: "XTEGmember",
    routingKey: xttid,
    data: `{"from":"member","to":"leader","msg":{"map":${mapContent},"action":"这条是执行席位发出的消息，指挥席位接收"}}`,
  });
  sendRmqMsg(msgData);
}
</script>

<style scoped lang="scss">
.left-base {
  background-color: rgba(123, 213, 229, 0);
  height: 100vh;
  width: 350px;
  .left-menu {
    padding-top: 300px;
  }

  // background: url("@/assets/xtui/layout/leftbg.png");
  // background-size: 100% 100%;
}
</style>

