<!--
 * @Descripttion: santana
 * @LastEditTime: 2021-12-15 17:24:40
-->
<template>
  <div id="piont-cluster">
    <el-button @click="randomCoor">cls</el-button>
    <el-button @click="setSuper">cls_super</el-button>
  </div>
</template> 

<script>
let clusterDataSource;
let s_cls;
import Supercluster from "supercluster";
export default {
  name: "PoiCluster",
  data() {
    return {
      // data:"aa",
      poiArr: [],
    };
  },
  mounted() {
    // this.randomCoor();
    this.addSuper();
  },
  methods: {
    randomCoor() {
      for (let i = 0; i < 1000; i++) {
        let temp = {};
        temp.id = i;
        temp.lon = Math.random() * 30 + 80; // 80 - 110
        temp.lat = Math.random() * 20 + 20; // 20 - 40
        this.poiArr.push(temp);
      }
      console.log(this.poiArr, "this.poiArr");
      this.loadPois();
    },
    loadPois() {
      clusterDataSource = new Cesium.CustomDataSource("cls");
      window.viewer.dataSources.add(clusterDataSource);
      for (const i of this.poiArr) {
        let item = this.addItem(i);
        clusterDataSource.entities.add(item);
      }
      this.setCluster();
    },
    addItem(c) {
      let e = new Cesium.Entity({
        id: c.id,
        position: new Cesium.Cartesian3.fromDegrees(c.lon, c.lat),
        label: {
          text: "Park",
          font: "14pt monospace",
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlineWidth: 2,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -9),
        },

        billboard: {
          image: "data/images/zzzz.png",
          width: 32, // 宽高必须设置，否则首次无法聚合
          height: 32,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          // scaleByDis: xxx
        },
        properties: {
          isPoi: "mPoi_entity",
        },
      });
      return e;
    },
    setCluster() {
      clusterDataSource.clustering.enabled = true;
      clusterDataSource.clustering.pixelRange = 60;
      clusterDataSource.clustering.minimumClusterSize = 2;
      var pinBuilder = new Cesium.PinBuilder();
      function pin50(length) {
        return new Cesium.PinBuilder()
          .fromText(length, Cesium.Color.RED, 48)
          .toDataURL();
      }
      function pin40(length) {
        return new Cesium.PinBuilder()
          .fromText(length, Cesium.Color.ORANGE, 48)
          .toDataURL();
      }
      function pin30(length) {
        return new Cesium.PinBuilder()
          .fromText(length, Cesium.Color.YELLOW, 48)
          .toDataURL();
      }
      function pin20(length) {
        return new Cesium.PinBuilder()
          .fromText(length, Cesium.Color.GREEN, 48)
          .toDataURL();
      }
      function pin10(length) {
        return new Cesium.PinBuilder()
          .fromText(length, Cesium.Color.BLUE, 48)
          .toDataURL();
      }

      console.log(pin40, "---pin40---");

      let removeListener =
        clusterDataSource.clustering.clusterEvent.addEventListener(function (
          clusteredEntities,
          cluster
        ) {
          // console.log(cluster, 'cluster----------');
          cluster.label.show = false;
          cluster.billboard.show = true;
          cluster.billboard.id = cluster.label.id;
          cluster.billboard.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;

          if (clusteredEntities.length >= 50) {
            cluster.billboard.image = pin50(clusteredEntities.length);
          } else if (clusteredEntities.length >= 40) {
            cluster.billboard.image = pin40(clusteredEntities.length);
          } else if (clusteredEntities.length >= 30) {
            cluster.billboard.image = pin30(clusteredEntities.length);
          } else if (clusteredEntities.length >= 20) {
            cluster.billboard.image = pin20(clusteredEntities.length);
          } else if (clusteredEntities.length >= 10) {
            cluster.billboard.image = pin10(clusteredEntities.length);
          }
          // else {
          // 	cluster.billboard.image = singleDigitPins[clusteredEntities.length - 2];
          // }
        });
    },

    // ---------------------------------------------------------------------------

    addSuper() {
      this.axios.get("data/geojson/usepoi.json").then((res) => {
        // console.log(res.data.features, 'rrr');
        s_cls = new Supercluster({
          radius: 40,
          maxZoom: 16,
        });
        console.log(s_cls, "s_cls---");
        s_cls.load(res.data.features);
      });
    },
    setSuper() {
      let out1 = s_cls.getClusters([80, 20, 130, 45], 2);
      console.log(out1, "out1-----------");
      let out2 = s_cls.getClusters([100, 20, 110, 30], 2);
      console.log(out2, "out2-----------");
      let out3 = s_cls.getClusters([120, 30, 125, 35], 15);
      console.log(out3, "out3-----------");

      var handler = new Cesium.ScreenSpaceEventHandler(
        window.viewer.scene.canvas
      );

      handler.setInputAction(function (wheelment) {
        console.log("滚轮事件：", wheelment);
      }, Cesium.ScreenSpaceEventType.WHEEL);
    },
  },
};
</script>

<style scoped>
#piont-cluster {
  width: 220px;
  height: 80px;
  padding: 20px 0;
  display: flex;
  justify-content: space-evenly;
  background-color: beige;
}
</style>
