<template>
  <div class="notice">
    <div class="notice-ref" ref="listRef">
      <div class="notice-items" v-for="item in noticeList" :key="item.id">
        <div class="notice-item-time">{{ item.date }}</div>
        <div class="notice-item-des">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
const listRef = ref(null);
const noticeList = ref([
  {
    id: 1,
    name: "某化工园区计划扩建厂区规模，引起关注和争议。",
    date: "2024-07-01 00:00:00",
  },
  {
    id: 2,
    name: "化工园区环保整治工作取得了一定的进展，空气质量有所改善。",
    date: "2024-07-02 00:00:00",
  },
  {
    id: 3,
    name: "化工园区组织了一次化学品泄漏应急演练活动，提升了应急处置能力。",
    date: "2024-07-03 00:00:00",
  },
  {
    id: 4,
    name: "某化工企业在环保方面表现突出，获得了一项环保奖项。",
    date: "2024-07-04 00:00:00",
  },
  {
    id: 5,
    name: "化工园区组织了一次化学品泄漏应急演练活动，提升了应急处置能力。",
    date: "2024-07-03 00:00:00",
  },
  {
    id: 6,
    name: "某化工企业在环保方面表现突出，获得了一项环保奖项。",
    date: "2024-07-04 00:00:00",
  },
]);
onMounted(() => {
  const listDom = listRef.value;
  let isScroll = true;
  const scrollContent = () => {
    if (isScroll) {
      listDom.scrollTop += 1;
      if (listDom.scrollTop + listDom.clientHeight >= listDom.scrollHeight) {
        listDom.scrollTop = 0;
      }
    }
  };
  const intervalId = setInterval(scrollContent, 100);
  listDom.addEventListener("mouseover", () => {
    isScroll = false;
  });
  listDom.addEventListener("mouseout", () => {
    isScroll = true;
  });
  // Cleanup interval on component unmount
  onUnmounted(() => {
    clearInterval(intervalId);
  });
});
</script>

<style scoped lang="scss">
.notice {
  width: 400px;
  height: 200px;
  font-size: 12px;
  color: #ffffff;
  padding: 10px;
  font-family: sans-serif;
  font-weight: 700;
}
.notice-ref {
  width: 380px;
  height: 180px;
  overflow-y: auto;
}
.notice-items {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-bottom: 10px;
}
.notice-item-time {
  width: 90px;
  color: rgb(60, 213, 255);
  text-align: center;
}
.notice-item-des {
  width: 300px;
  word-break: break-all;
  line-height: 17px;
}
</style>
