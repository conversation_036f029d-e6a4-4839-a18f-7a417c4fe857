<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="auto">
            <el-form-item label="姓名" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="承包商名称" prop="contractorBasicId">
                <el-input v-model="queryParams.contractorBasicId" placeholder="请输入承包商名称" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="承包商项目" prop="contractorProjectId">
                <el-input v-model="queryParams.contractorProjectId" placeholder="请输入承包商项目" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="人员类型" prop="personType">
                <el-select v-model="queryParams.personType" placeholder="请选择人员类型" clearable style="width: 214px;">
                    <el-option v-for="dict in cbs_personnel_type" :key="dict.value" :label="dict.label"
                        :value="dict.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['contractor:person:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['contractor:person:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="Upload" @click="handleImport"
                    v-hasPermi="['contractor:person:import']">导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport"
                    v-hasPermi="['contractor:person:export']">导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="importTemplate">下载模板</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="personList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="姓名" align="center" prop="name" />
            <el-table-column label="联系方式" align="center" prop="phone" />
            <el-table-column label="承包商名称" align="center" prop="contractorBasicId" />
            <el-table-column label="承包商项目" align="center" prop="contractorProjectId" />
            <el-table-column label="人员类型" align="center" prop="personType">
                <template #default="scope">
                    <dict-tag :options="cbs_personnel_type" :value="scope.row.personType" />
                </template>
            </el-table-column>
            <el-table-column label="登记时间" align="center" prop="registrationTime" width="180">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="身份证正" align="center" prop="idCardFrontUrl" width="100">
                <template #default="scope">
                    <image-preview :src="scope.row.idCardFrontUrl" :width="50" :height="50" />
                </template>
            </el-table-column>
            <el-table-column label="身份证反" align="center" prop="idCardBackUrl" width="100">
                <template #default="scope">
                    <image-preview :src="scope.row.idCardBackUrl" :width="50" :height="50" />
                </template>
            </el-table-column>
            <el-table-column label="入场许可期限" align="center" prop="permitDate" width="180">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.permitDate, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="保险" align="center" prop="insuranceUrl" />
            <el-table-column label="体检记录" align="center" prop="medicalRecordUrl" />
            <el-table-column label="从业资格证书" align="center" prop="qualificationUrl" />
            <el-table-column label="从业资格证书有效期起" align="center" prop="qualificationValidityStart" width="180">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.qualificationValidityStart, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="从业资格证书有效期止" align="center" prop="qualificationValidityEnd" width="180">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.qualificationValidityEnd, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['contractor:person:edit']">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['contractor:person:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改承包商人员对话框 -->
        <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
            <el-form ref="personRef" :model="form" :rules="rules" label-width="120px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="姓名" prop="name">
                            <el-input v-model="form.name" placeholder="请输入姓名" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系方式" prop="phone">
                            <el-input v-model="form.phone" placeholder="请输入联系方式" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="承包商名称" prop="contractorBasicId">
                            <el-select v-model="form.contractorBasicId" placeholder="请选择承包商" filterable
                                style="width: 100%">
                                <el-option v-for="item in contractorList" :key="item.id" :label="item.contractorName"
                                    :value="item.id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="承包商项目" prop="contractorProjectId">
                            <el-select v-model="form.contractorProjectId" placeholder="请选择项目" filterable
                                style="width: 100%">
                                <el-option v-for="item in projectList" :key="item.id" :label="item.projectName"
                                    :value="item.id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="人员类型" prop="personType">
                            <el-select v-model="form.personType" placeholder="请选择人员类型" style="width: 100%">
                                <el-option v-for="dict in cbs_personnel_type" :key="dict.value" :label="dict.label"
                                    :value="dict.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="登记时间" prop="registrationTime">
                            <el-date-picker v-model="form.registrationTime" type="datetime" placeholder="请选择登记时间"
                                style="width: 100%" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="身份证正面" prop="idCardFrontUrl">
                            <image-upload v-model="form.idCardFrontUrl" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="身份证反面" prop="idCardBackUrl">
                            <image-upload v-model="form.idCardBackUrl" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="入场许可期限" prop="permitDate">
                            <el-date-picker v-model="form.permitDate" type="datetime" placeholder="请选择入场许可期限"
                                style="width: 100%" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="保险" prop="insuranceUrl">
                            <file-upload v-model="form.insuranceUrl" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="体检记录" prop="medicalRecordUrl">
                            <file-upload v-model="form.medicalRecordUrl" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="从业资格证书" prop="qualificationUrl">
                            <file-upload v-model="form.qualificationUrl" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="证书有效期起" prop="qualificationValidityStart">
                            <el-date-picker v-model="form.qualificationValidityStart" type="datetime"
                                placeholder="请选择证书有效期起" style="width: 100%" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="证书有效期止" prop="qualificationValidityEnd">
                            <el-date-picker v-model="form.qualificationValidityEnd" type="datetime"
                                placeholder="请选择证书有效期止" style="width: 100%" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 导入对话框 -->
        <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
            <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
                :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
                :auto-upload="false" drag>
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitFileForm">确 定</el-button>
                    <el-button @click="upload.open = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Person">
import { addPerson, delPerson, getPerson, listPerson, updatePerson } from "@/api/xtcontractormag/BaseInfo/person";
import useUserStore from "@/store/modules/user";
import { deepClone, formatDate } from "@/utils";
import { getToken } from "@/utils/auth";

    const userStore = useUserStore();

    const { proxy } = getCurrentInstance();
        const { cbs_personnel_type } = proxy.useDict('cbs_personnel_type');

    const personList = ref([]);
    const open = ref(false);
    const loading = ref(true);
    const showSearch = ref(true);
    const ids = ref([]);
    const itemsArr = ref([]);
    const multiple = ref(true);
    const total = ref(0);
    const title = ref("");

    /*** 用户导入参数 */
    const upload = reactive({
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: window.xtmapConfig.xtBaseUrl + "/contractor/person/importData",
    });

    const data = reactive({
        form: {},
        queryParams: {
            pageNum: 1,
            pageSize: 10,
                        name: null,
                        phone: null,
                        contractorBasicId: null,
                        contractorProjectId: null,
                        personType: null,
                        registrationTime: null,
                        idCardFrontUrl: null,
                        idCardBackUrl: null,
                        permitDate: null,
                        insuranceUrl: null,
                        medicalRecordUrl: null,
                        qualificationUrl: null,
                        qualificationValidityStart: null,
                        qualificationValidityEnd: null,
        },
        rules: {
                        phone: [
                        { required: true, message: "联系方式不能为空", trigger: "blur" }
                    ],
                        contractorProjectId: [
                        { required: true, message: "承包商项目id不能为空", trigger: "blur" }
                    ],
                        personType: [
                        { required: true, message: "人员类型不能为空", trigger: "change" }
                    ],
                        idCardFrontUrl: [
                        { required: true, message: "身份证正url不能为空", trigger: "blur" }
                    ],
                        idCardBackUrl: [
                        { required: true, message: "身份证反url不能为空", trigger: "blur" }
                    ],
                        permitDate: [
                        { required: true, message: "入场许可期限不能为空", trigger: "blur" }
                    ],
                        insuranceUrl: [
                        { required: true, message: "保险url不能为空", trigger: "blur" }
                    ],
                        medicalRecordUrl: [
                        { required: true, message: "体检记录url不能为空", trigger: "blur" }
                    ],
                        qualificationUrl: [
                        { required: true, message: "从业资格证书url不能为空", trigger: "blur" }
                    ],
                        qualificationValidityStart: [
                        { required: true, message: "从业资格证书有效期起不能为空", trigger: "blur" }
                    ],
                        qualificationValidityEnd: [
                        { required: true, message: "从业资格证书有效期止不能为空", trigger: "blur" }
                    ],
        }
    });

    const { queryParams, form, rules } = toRefs(data);

    /** 查询承包商人员列表 */
    function getList() {
        loading.value = true;
        listPerson(queryParams.value).then(response => {
                personList.value = response.rows;
            total.value = response.total;
            loading.value = false;
        });
    }

    // 取消按钮
    function cancel() {
        open.value = false;
        reset();
    }

    // 表单重置
    function reset() {
        form.value = {
                        id: null,
                        tenantId: null,
                        createBy: null,
                        createTime: null,
                        updateBy: null,
                        updateTime: null,
                        name: null,
                        phone: null,
                        contractorBasicId: null,
                        contractorProjectId: null,
                        personType: null,
                        registrationTime: null,
                        idCardFrontUrl: null,
                        idCardBackUrl: null,
                        permitDate: null,
                        insuranceUrl: null,
                        medicalRecordUrl: null,
                        qualificationUrl: null,
                        qualificationValidityStart: null,
                        qualificationValidityEnd: null,
                        deleted: null
        };
        proxy.resetForm("personRef");
    }

    /** 搜索按钮操作 */
    function handleQuery() {
        queryParams.value.pageNum = 1;
        getList();
    }

    /** 重置按钮操作 */
    function resetQuery() {
        proxy.resetForm("queryRef");
        handleQuery();
    }

    // 多选框选中数据
    function handleSelectionChange(selection) {
        ids.value = selection.map(item => item.id);
        itemsArr.value = deepClone(selection);
        multiple.value = !selection.length;
    }

    /** 导入按钮操作 */
    function handleImport() {
        upload.title = "导入";
        upload.open = true;
    }

    /** 下载模板操作 */
    function importTemplate() {
        proxy.download(
                "contractor/person/export/template",
                {},
                `person_${new Date().getTime()}.xlsx`
        );
    }

    /**文件上传中处理 */
    const handleFileUploadProgress = (event, file, fileList) => {
        upload.isUploading = true;
    };

    /** 文件上传成功处理 */
    const handleFileSuccess = (response, file, fileList) => {
        upload.open = false;
        upload.isUploading = false;
        proxy.$refs["uploadRef"].handleRemove(file);
        proxy.$alert(
                "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
                response.msg +
                "</div>",
                "导入结果",
                { dangerouslyUseHTMLString: true }
        );
        getList();
    };

    /** 提交上传文件 */
    function submitFileForm() {
        upload.url = `${window.xtmapConfig.xtBaseUrl}/contractor/person/importData?userId=${userStore.id}`;
        proxy.$refs["uploadRef"].submit();
    }

    /** 新增按钮操作 */
    function handleAdd() {
        reset();
        open.value = true;
        title.value = "添加承包商人员";
    }

    /** 修改按钮操作 */
    function handleUpdate(row) {
        reset();
        const _id = row.id || ids.value
        getPerson(_id).then(response => {
            form.value = response.data;
            open.value = true;
            title.value = "修改承包商人员";
        });
    }

    /** 提交按钮 */
    function submitForm() {
        proxy.$refs["personRef"].validate(valid => {
            if (valid) {
                if (form.value.id != null) {
                    const data = {
                        ...form.value,
                        tenantId: userStore.tenantId,
                        updateBy: userStore.name,
                        updateTime: formatDate(new Date()),
                    };
                    updatePerson(data).then(response => {
                        proxy.$modal.msgSuccess("修改成功");
                        open.value = false;
                        getList();
                    });
                } else {
                    const data = {
                        ...form.value,
                        tenantId: userStore.tenantId,
                        createBy: userStore.name,
                        createTime: formatDate(new Date()),
                        updateBy: userStore.name,
                        updateTime: formatDate(new Date()),
                        deleted: '0'
                    };
                    addPerson(data).then(response => {
                        proxy.$modal.msgSuccess("新增成功");
                        open.value = false;
                        getList();
                    });
                }
            }
        });
    }

    /** 删除按钮操作 */
    function handleDelete(row) {
        const _ids = row.id ? [row.id] : ids.value;
        proxy.$modal.confirm('是否确认删除承包商人员编号为"' + _ids + '"的数据项？').then(function() {
            return delPerson(_ids);
        }).then(() => {
            getList();
            proxy.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    }

    /** 导出按钮操作 */
    function handleExport() {
        const params = itemsArr.value.length > 0 ? itemsArr.value : undefined;
        proxy.codeDownload(
                'contractor/person/export',
                params,
                `person_${new Date().getTime()}.xlsx`
        );
    }

    getList();
</script>
