<template>
  <div class="duty-person">
    <div class="dutyback" v-for="item in dutyList" :key="item.id">
      <div class="dutybackTitle">{{ titleName(item.isLeader) }}</div>
      <div class="">{{ item.employeeName }}</div>
      <div class="">{{ item.phone }}</div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  dutyList: {
    type: Array,
    required: true,
    default: [],
  },
});
const titleName = (val) => {
  switch (val) {
    case 1:
      return "值班领导";
    case 2:
      return "带班领导";
    case 0:
      return "值班人员";
      break;
  }
};
onMounted(() => {});
</script>

<style scoped lang="scss">
.duty-person {
  width: 400px;
  height: 100px;
  margin-top: 10px;
  //   font-family: ysbthzt;
  font-size: 12px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-content: space-around;
  justify-content: center;
  font-family: Source <PERSON>;
  .dutyback {
    background: url("@/assets/xtui/command/onduty/dutyback.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 40px;
    margin: 0 10px;
    color: azure;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    width: 170px;
    flex-wrap: wrap;
    .dutybackTitle {
      font-size: 12px;
      color: #b2f9ff;
      font-weight: bold;
      text-shadow: 0px 0px 4px #3cd5ff;
      width: 35px;
      margin-left: 5px;
    }
  }
}
</style>
