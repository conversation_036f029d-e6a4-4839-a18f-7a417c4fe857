<template>
  <div class="router">
    <div class="router-path">
      <div class="router-header">路径规划</div>
      <div class="router-menu">
        <LocationInput label="起点" :point="startPoint" @pick="positionPick('start')" />
        <LocationInput label="终点" :point="endPoint" @pick="positionPick('end')" />
      </div>
    </div>
    <div class="router-position">
      <div class="header-with-add">
        <div class="router-header">必经点</div>
        <div class="positionPick-add" @click="positionPick('center')"></div>
      </div>
      <div class="router-menu">
        <el-table :data="tableData" style="width: 100%" max-height="150">
          <el-table-column prop="longitude" align="center" label="经度" />
          <el-table-column prop="latitude" align="center" label="纬度" />
          <el-table-column align="center" label="操作">
            <template #default="scope">
              <div class="deleteIcon" @click="handleDeleteClick(scope.$index)"></div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="router-region">
      <div class="header-with-add">
        <div class="router-header">绕行区域</div>
        <div class="positionPick-add" @click="positionPickPolygon()"></div>
      </div>
      <div class="router-menu">
        <el-table :data="tableDataRegion" style="width: 100%" max-height="150">
          <el-table-column
            prop="name"
            label="绕行区域"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column align="center" label="操作">
            <template #default="scope">
              <div
                class="deleteIcon"
                @click="handleDeleteRegionClick(scope.$index)"
              ></div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="dialogbutton">
      <div class="masbutton cancer" @click="resetForm()">
        <span>清空规划</span>
      </div>
      <div class="masbutton submit" @click="submitForm()">
        <span>开始规划</span>
      </div>
    </div>
  </div>
</template>
<script setup>
import LocationInput from "@/earthCompStyle/router/LocationInput.vue"; // 假设我们创建了这个组件
const tableData = ref([]);
const tableDataRegion = ref([]);
const labelMarkers = ref([]); // 用于存储所有标注
let pointpick_handler = reactive({});
let polygonHandler = ref(null);
let positions = [];
let polygonEntity = ref(null);

const initEntity = () => {
  // 初始化其他实体的代码
};

const startPoint = reactive({ longitude: null, latitude: null });
const endPoint = reactive({ longitude: null, latitude: null });

onMounted(() => {
  emitter.on("viewerLoad", () => {
    initEntity();
  });
  if (window.viewer) {
    initEntity();
  }
});

const createLabelMarker = (position, text) => {
  return viewer.entities.add({
    position: position,
    label: {
      text: text,
      font: "14px sans-serif",
      fillColor: Cesium.Color.YELLOW,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    },
    point: {
      pixelSize: 5,
      color: Cesium.Color.RED,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
    },
  });
};

const removeLabelMarker = (marker) => {
  viewer.entities.remove(marker);
};

const handleDeleteClick = (index) => {
  const marker = labelMarkers.value[index];
  if (marker) {
    removeLabelMarker(marker);
  }
  tableData.value.splice(index, 1);
  labelMarkers.value.splice(index, 1);
};

const handleDeleteRegionClick = (index) => {
  const region = tableDataRegion.value[index];

  if (region && region.entity) {
    viewer.entities.remove(region.entity);
  }

  // 移除与这个区域相关的所有标记
  const labelsToRemove = labelMarkers.value.filter((marker) =>
    region.positions.some((pos, idx) =>
      Cesium.Cartesian3.equals(
        marker.position,
        Cesium.Cartesian3.fromDegrees(parseFloat(pos[0]), parseFloat(pos[1]))
      )
    )
  );

  labelsToRemove.forEach((label) => viewer.entities.remove(label));
  labelMarkers.value = labelMarkers.value.filter(
    (label) => !labelsToRemove.includes(label)
  );

  tableDataRegion.value.splice(index, 1);
};

const submitForm = () => {};

const resetForm = () => {
  tableData.value = [];
  tableDataRegion.value.forEach((region) => {
    if (region.entity) {
      viewer.entities.remove(region.entity);
    }
  });
  tableDataRegion.value = [];
  labelMarkers.value.forEach(removeLabelMarker);
  labelMarkers.value = [];
};

const positionPick = (val) => {
  pointpick_handler = new Cesium.ScreenSpaceEventHandler(window.viewer.scene.canvas);
  pointpick_handler.setInputAction((click) => {
    const cartesian = window.viewer.camera.pickEllipsoid(
      click.position,
      viewer.scene.globe.ellipsoid
    );
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    const longitude = Cesium.Math.toDegrees(cartographic.longitude);
    const latitude = Cesium.Math.toDegrees(cartographic.latitude);

    // Convert to fixed decimal places
    const longitudeFixed = parseFloat(longitude.toFixed(6));
    const latitudeFixed = parseFloat(latitude.toFixed(6));

    let position = Cesium.Cartesian3.fromDegrees(longitudeFixed, latitudeFixed);

    if (val === "start") {
      startPoint.longitude = longitudeFixed;
      startPoint.latitude = latitudeFixed;
      createLabelMarker(position, "起点");
    } else if (val === "end") {
      endPoint.longitude = longitudeFixed;
      endPoint.latitude = latitudeFixed;
      createLabelMarker(position, "终点");
    } else if (val === "center") {
      tableData.value.push({ longitude: longitudeFixed, latitude: latitudeFixed });
      // createLabelMarker(position, "必经点");
      labelMarkers.value.push(createLabelMarker(position, "必经点"));
    }

    removeHandler();
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};

const createPolygon = (positions) => {
  return new Cesium.PolygonHierarchy(Cesium.Cartesian3.fromDegreesArray(positions));
};

const positionPickPolygon = () => {
  positions = [];
  labelMarkers.value.forEach((marker) => viewer.entities.remove(marker));
  labelMarkers.value = [];

  if (polygonHandler.value) {
    polygonHandler.value.destroy();
    polygonHandler.value = null;
  }
  if (polygonEntity.value) {
    viewer.entities.remove(polygonEntity.value);
    polygonEntity.value = null;
  }

  polygonHandler.value = new Cesium.ScreenSpaceEventHandler(window.viewer.scene.canvas);
  polygonHandler.value.setInputAction((click) => {
    const cartesian = window.viewer.camera.pickEllipsoid(
      click.position,
      viewer.scene.globe.ellipsoid
    );
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian);

    const longitude = Cesium.Math.toDegrees(cartographic.longitude);
    const latitude = Cesium.Math.toDegrees(cartographic.latitude);

    const longitudeFixed = parseFloat(longitude.toFixed(6));
    const latitudeFixed = parseFloat(latitude.toFixed(6));

    positions.push(longitudeFixed, latitudeFixed);

    const markerPosition = Cesium.Cartesian3.fromDegrees(longitudeFixed, latitudeFixed);
    const label = createLabelMarker(markerPosition, `顶点 ${positions.length / 2}`);
    labelMarkers.value.push(label);

    if (positions.length >= 6) {
      if (polygonEntity.value) {
        viewer.entities.remove(polygonEntity.value);
      }
      const tempPositions = positions.concat([positions[0], positions[1]]);
      polygonEntity.value = viewer.entities.add({
        polygon: {
          hierarchy: createPolygon(tempPositions),
          material: Cesium.Color.RED.withAlpha(0.5),
        },
      });
    } else if (positions.length >= 2) {
      if (polygonEntity.value) {
        viewer.entities.remove(polygonEntity.value);
      }
      polygonEntity.value = viewer.entities.add({
        polygon: {
          hierarchy: createPolygon(positions),
          material: Cesium.Color.RED.withAlpha(0.5),
        },
      });
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  polygonHandler.value.setInputAction(() => {
    if (positions.length >= 6) {
      const tempPositions = positions.concat([positions[0], positions[1]]);
      const polygonHierarchy = createPolygon(tempPositions);

      polygonEntity.value = viewer.entities.add({
        polygon: {
          hierarchy: polygonHierarchy,
          material: Cesium.Color.RED.withAlpha(0.5),
        },
      });
    }
    tableDataRegion.value.push({
      name: "绕行区域",
      positions: positions,
      entity: polygonEntity.value,
    });

    polygonHandler.value.destroy();
    polygonHandler.value = null;
  }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
};

const removeHandler = () => {
  // entity.show = false;
  pointpick_handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
</script>
<style scoped lang="scss">
.router::before {
  content: "";
  position: absolute;
  width: 358px;
  min-height: 628px;
  height: max-content;
  z-index: -1;
  background: rgba(8, 76, 124, 0.5);
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%) 2;
  backdrop-filter: blur(10px);
  top: -10px;
}

.router {
  width: 358px;
  min-height: 628px;
  height: max-content;

  .router-header {
    background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center center;
    background-size: 100% 100%;
    width: 184.04px;
    height: 32px;
    padding: 5px 30px;
    margin: 5px 10px;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: bold;
    color: aliceblue;
  }

  .router-menu {
    padding: 10px 10px;

    .deleteIcon {
      width: 100%;
      height: 12px;
      background: url("@/assets/xtui/tools/deleteicon.png") no-repeat center center;
    }

    .router-menu-point {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      width: 100%;
      align-items: center;
      margin: 5px 0px;

      span {
        font-family: 思源黑体;
        font-size: 12px;
        font-weight: normal;
        line-height: 20px;
        text-align: right;
        color: #ffffff;
      }
    }
  }

  .header-with-add {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .positionPick-add {
      cursor: pointer;
      width: 32px;
      height: 32px;
      background-repeat: no-repeat;
      background-image: url("@/assets/xtui/tools/addimg.png");
    }
  }
}

.dialogbutton {
  width: 100%;
  display: flex;
  justify-content: space-evenly;

  .masbutton {
    width: 100px;
    height: 32px;
    color: #ffffff;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    text-align: center;
    display: flex;
    align-content: center;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &.submit {
      background-image: url("@/assets/xtui/command/onduty/sunmitback.png");
    }
    &.cancer {
      background-image: url("@/assets/xtui/command/onduty/cancleback.png");
    }
  }
}

.positionPick {
  cursor: pointer;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
  background-image: url("@/assets/xtui/tools/pickimg.png");
}
</style>
