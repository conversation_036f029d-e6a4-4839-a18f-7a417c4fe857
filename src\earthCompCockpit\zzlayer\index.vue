<template>
  <div class="zzlayer">
    <!-- <div class="zzlayer-header">
      <span>边界图层</span>

      <CircleClose @click="closePanel" style="width: 1.5em; height: 1.5em" />
    </div> -->
    <!-- <el-icon class="sssss">
      <Delete />
    </el-icon>
    <CircleClose
      @click="closePanel"
      style="width: 1.5em; height: 1.5em; margin-right: 8px"
    /> -->
    <div class="zzlayer-body">
      <div class="menus">
        <!-- <el-switch
          @change="changeGeoJson"
          v-model="valueXian"
          class="mb-2"
          active-text="行政边界"
        /> -->

        <!-- <el-switch
          v-model="valueBf"
          @change="changeGeoJson3"
          class="mb-2"
          active-text="监测分析范围"
        />
        <el-switch
          v-model="valueQu"
          @change="changeGeoJson2"
          class="mb-2"
          active-text="山火高发区域"
        /> -->
        <el-switch
          v-model="valueXz"
          @change="changeGeoJson6"
          class="mb-2"
          active-text="乡镇政府"
        />
        <el-switch
          v-model="valueCwh"
          @change="changeGeoJson4"
          class="mb-2"
          active-text="村委会"
        />
        <!-- <el-switch
          v-model="valueCj"
          @change="changeGeoJson5"
          class="mb-2"
          active-text="村界"
        /> -->

        <!-- <el-switch
        v-model="valueTer"
        @change="changeTer"
        class="mb-2"
        active-text="地形图层"
      /> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { nextTick, ref, reactive, computed, onMounted } from "vue";

const valueXian = ref(true);
const valueQu = ref(true);
const valueBf = ref(true);
const valueCwh = ref(false);
const valueCj = ref(false);
const valueXz = ref(false);
const valueTer = ref(false);

// cesium json对象
const jsonXian = ref(null);
const jsonQu = ref(null);
const jsonBf = ref(null);
const jsonCwh = ref(null);
const jsonCj = ref(null);
const jsonXz = ref(null);

function changeGeoJson(val) {
  if (!jsonXian.value && val) {
    jsonXian.value = Cesium.GeoJsonDataSource.load(
      `${window.xtmapConfig.publicPath}/geojson/zzxianold.geojson`,
      {
        stroke: Cesium.Color.RED, // 边框颜色
        // fill: Cesium.Color.RED.withAlpha(1), // 填充颜色
        fill: Cesium.Color.RED.withAlpha(0.0), // 填充颜色
        strokeWidth: 10, // 边框宽度
        // zIndex: 1,
      }
    );

    jsonXian.value.then((dataSource) => {
      viewer.dataSources.add(jsonXian.value);
      dataSource.name = "geojson_xian";
      // viewer.flyTo(dataSource.entities.values);
      dataSource.entities.values.forEach((entity) => {
        if (entity) {
          // console.log(entity, "555");

          entity.polygon.zIndex = 5;
        }
      });
    });

    // --------------
  } else {
    viewer.dataSources.remove(viewer.dataSources.getByName("geojson_xian")[0]);
    jsonXian.value = null;
  }
}
function changeGeoJson2(val) {
  if (!jsonQu.value && val) {
    jsonQu.value = Cesium.GeoJsonDataSource.load(
      `${window.xtmapConfig.publicPath}/geojson/highv2.geojson`,
      {
        stroke: Cesium.Color.ORANGE, // 边框颜色
        // fill: Cesium.Color.ORANGE.withAlpha(0), // 填充颜色
        fill: Cesium.Color.ORANGE.withAlpha(0.36), // 填充颜色
        strokeWidth: 10, // 边框宽度
        // zIndex: 3,
      }
    );

    jsonQu.value.then((dataSource) => {
      viewer.dataSources.add(jsonQu.value);
      dataSource.name = "geojson_qu";
      // viewer.flyTo(dataSource.entities.values);
    });
  } else {
    viewer.dataSources.remove(viewer.dataSources.getByName("geojson_qu")[0]);
    jsonQu.value = null;
  }
}
function changeGeoJson3(val) {
  if (!jsonBf.value && val) {
    jsonBf.value = Cesium.GeoJsonDataSource.load(
      `${window.xtmapConfig.publicPath}/geojson/bf.geojson`,
      {
        stroke: Cesium.Color.DODGERBLUE, // 边框颜色
        // fill: Cesium.Color.DODGERBLUE.withAlpha(1), // 填充颜色
        fill: Cesium.Color.DODGERBLUE.withAlpha(0.2), // 填充颜色
        strokeWidth: 10, // 边框宽度
        // zIndex: 2,
      }
    );

    jsonBf.value.then((dataSource) => {
      viewer.dataSources.add(jsonBf.value);
      dataSource.name = "geojson_bf";
      dataSource.entities.values.forEach((entity) => {
        if (entity) {
          entity.polygon.zIndex = 10;
        }
      });
      // viewer.flyTo(dataSource.entities.values);
    });
  } else {
    viewer.dataSources.remove(viewer.dataSources.getByName("geojson_bf")[0]);
    jsonBf.value = null;
  }
}
// 村委会点
function changeGeoJson4(val) {
  if (!jsonCwh.value && val) {
    jsonCwh.value = Cesium.GeoJsonDataSource.load(
      `${window.xtmapConfig.publicPath}/geojson/zzcwhpoint.geojson`
      // {
      // stroke: Cesium.Color.DODGERBLUE, // 边框颜色
      // fill: Cesium.Color.DODGERBLUE.withAlpha(0.2), // 填充颜色
      // strokeWidth: 10, // 边框宽度
      // zIndex: 2,
      // }
    );

    jsonCwh.value.then((dataSource) => {
      viewer.dataSources.add(jsonCwh.value);
      dataSource.name = "geojson_cwh";
      // viewer.flyTo(dataSource.entities.values);
      dataSource.entities.values.forEach((entity) => {
        if (entity.properties) {
          // console.log(entity.properties);

          entity.billboard = new Cesium.BillboardGraphics({
            image: `${window.xtmapConfig.billboard.path}cwh.png`, // 使用项目中的图标或标记图片
            width: 32,
            height: 32,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 150000),
          });
          entity.label = new Cesium.LabelGraphics({
            // text: '',
            text: entity.properties.村,
            font: "bold 14px MicroSoft YaHei",
            outlineWidth: 2,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            pixelOffset: new Cesium.Cartesian2(0, -45), //偏移量
            showBackground: true,
            backgroundColor: new Cesium.Color(0, 0, 0, 0.5),
            backgroundPadding: new Cesium.Cartesian2(6, 3),
            scaleByDistance: new Cesium.NearFarScalar(3000, 1, 12000, 0),
          });
        }
      });

      // 调整视角到数据范围
      viewer.flyTo(dataSource.value);
    });
  } else {
    viewer.dataSources.remove(viewer.dataSources.getByName("geojson_cwh")[0]);
    jsonCwh.value = null;
  }
}

// 村界
function changeGeoJson5(val) {
  if (!jsonCj.value && val) {
    jsonCj.value = Cesium.GeoJsonDataSource.load(
      `${window.xtmapConfig.publicPath}/geojson/zzxian.geojson`,
      {
        stroke: Cesium.Color.DODGERBLUE, // 边框颜色
        fill: Cesium.Color.RED.withAlpha(0.1), // 填充颜色
        strokeWidth: 10, // 边框宽度
        zIndex: 2,
      }
    );

    jsonCj.value.then((dataSource) => {
      viewer.dataSources.add(jsonCj.value);
      dataSource.name = "geojson_cj";
      // viewer.flyTo(dataSource.entities.values);
    });
  } else {
    viewer.dataSources.remove(viewer.dataSources.getByName("geojson_cj")[0]);
    jsonCj.value = null;
  }
}

// 乡镇点
function changeGeoJson6(val) {
  if (!jsonXz.value && val) {
    jsonXz.value = Cesium.GeoJsonDataSource.load(
      `${window.xtmapConfig.publicPath}/geojson/zzxz.geojson`
      // {
      // stroke: Cesium.Color.DODGERBLUE, // 边框颜色
      // fill: Cesium.Color.DODGERBLUE.withAlpha(0.2), // 填充颜色
      // strokeWidth: 10, // 边框宽度
      // zIndex: 2,
      // }
    );

    jsonXz.value.then((dataSource) => {
      viewer.dataSources.add(jsonXz.value);
      dataSource.name = "geojson_xz";
      // viewer.flyTo(dataSource.entities.values);
      dataSource.entities.values.forEach((entity) => {
        if (entity.properties) {
          // console.log(entity.properties);

          entity.billboard = new Cesium.BillboardGraphics({
            image: `${window.xtmapConfig.billboard.path}flag.png`, // 使用项目中的图标或标记图片
            width: 32,
            height: 32,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 150000),
          });
          entity.label = new Cesium.LabelGraphics({
            // text: '',
            text: entity.properties.name,
            font: "bold 14px MicroSoft YaHei",
            outlineWidth: 2,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            pixelOffset: new Cesium.Cartesian2(0, -45), //偏移量
            showBackground: true,
            backgroundColor: new Cesium.Color(0, 0, 0, 0.5),
            backgroundPadding: new Cesium.Cartesian2(6, 3),
            scaleByDistance: new Cesium.NearFarScalar(3000, 1, 12000, 0),
          });
        }
      });

      // 调整视角到数据范围
      viewer.flyTo(dataSource.value);
    });
  } else {
    viewer.dataSources.remove(viewer.dataSources.getByName("geojson_xz")[0]);
    jsonXz.value = null;
  }
}

// 地形

function changeTer(val) {
  if (val) {
    // viewer.imageryLayers.addImageryProvider();
    viewer.terrainProvider = new Cesium.CesiumTerrainProvider({
      url: `https://tiles1.geovisearth.com/base/v1/terrain`,
      // url: `https://tiles1.geovisearth.com/base/v1/terrain/{z}/{x}/{y}.terrain?v=1.1.0&token=${window.xtmapConfig.map.XTY_Key}`,
    });
  } else {
    // viewer.terrainProvider = null;
    viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider();
  }
}

emitter.on("viewerLoad", () => {
  // changeGeoJson(valueXian.value);
  // changeGeoJson2(valueQu.value);
  // changeGeoJson3(valueBf.value);
  // changeGeoJson4(valueCwh.value);
});
const emits = defineEmits(["closePanel"]); //参数为数组
function closePanel() {
  emits("closePanel");
}

onMounted(() => {
  // emitter.on("viewerLoad", () => {
  //   poiChange();
  //   viewerChange();
  // });
});
</script>

<style scoped lang="scss">
.zzlayer {
  height: max-content;
  // background: url("@/assets/xtui/tools/dialogback.png");
  // background-size: 100% 100%;
  // display: flex;
  // flex-direction: column;
  // color: antiquewhite;
  // font-family: ysbthzt;

  padding: 8px;
  .zzlayer-header {
    background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center center;
    background-size: 96% 100%;
    height: 32px;
    width: 100%;
    margin: 5px 0px 15px 0px;
    padding: 5px 30px;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0px;
    color: aliceblue;
    display: flex;
    justify-content: space-between;
  }
  .zzlayer-body {
    // margin: 0 auto;
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
    .menus {
      width: 180px;
      // height: 160px;
      background-color: #11509b5c;

      padding: 10px 10px 10px 20px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-evenly;
      border-radius: 5px;
      pointer-events: auto;
      color: white;
      :deep(.el-switch__label) {
        color: white;
      }
      :deep(.el-switch) {
        // margin-bottom: 10px;
      }
    }
  }
}
</style>
