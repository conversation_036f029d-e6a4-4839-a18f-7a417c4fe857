<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="auto"
    >
      <el-form-item label="推流设备名称" prop="deviceName">
        <el-input
          v-model="queryParams.deviceName"
          placeholder="请输入推流设备名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="协议类型" prop="protocol">
        <el-input
          v-model="queryParams.protocol"
          placeholder="请输入协议类型"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="推流状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择推流状态" clearable>
          <el-option
            v-for="dict in cbs_flvstatus"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['contractor:videoStream:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['contractor:videoStream:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['contractor:videoStream:import']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['contractor:videoStream:export']"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="importTemplate"
          >下载模板</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="videoStreamList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column show-overflow-tooltip type="selection" width="55" align="center" />
      <el-table-column
        show-overflow-tooltip
        label="视频流名称"
        align="center"
        prop="streamName"
      />
      <el-table-column
        show-overflow-tooltip
        label="推流设备名称"
        align="center"
        prop="deviceName"
      />
      <el-table-column
        show-overflow-tooltip
        label="视频流地址"
        align="center"
        prop="streamUrl"
      />
      <!-- <el-table-column
        show-overflow-tooltip
        label="协议类型"
        align="center"
        prop="protocol"
      >
        <template #default="scope">
          <dict-tag :options="cbs_protocoltype" :value="scope.row.protocol * 1" />
        </template>
      </el-table-column> -->
      <el-table-column
        show-overflow-tooltip
        label="推流开始时间"
        align="center"
        prop="startTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        label="推流结束时间"
        align="center"
        prop="endTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        label="推流状态"
        align="center"
        prop="status"
      >
        <template #default="scope">
          <dict-tag :options="cbs_flvstatus" :value="scope.row.status * 1" />
        </template>
      </el-table-column>
      <!-- <el-table-column
        show-overflow-tooltip
        label="实时码率"
        align="center"
        prop="bitrateKbps"
      />
      <el-table-column
        show-overflow-tooltip
        label="分辨率"
        align="center"
        prop="resolution"
      />
      <el-table-column
        show-overflow-tooltip
        label="帧率"
        align="center"
        prop="frameRate"
      />
      <el-table-column
        show-overflow-tooltip
        label="推流端IP地址"
        align="center"
        prop="uploadIp"
      />
      <el-table-column
        show-overflow-tooltip
        label="位置名称"
        align="center"
        prop="locationName"
      />
      <el-table-column
        show-overflow-tooltip
        label="经度"
        align="center"
        prop="longitude"
      />
      <el-table-column
        show-overflow-tooltip
        label="纬度"
        align="center"
        prop="latitude"
      />
      <el-table-column
        show-overflow-tooltip
        label="海拔高度"
        align="center"
        prop="altitude"
      />
      <el-table-column
        show-overflow-tooltip
        label="位置/区域编码"
        align="center"
        prop="locationCode"
      />
      <el-table-column
        show-overflow-tooltip
        label="备注信息"
        align="center"
        prop="remarks"
      /> -->
      <el-table-column
        width="180"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['contractor:videoStream:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['contractor:videoStream:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改视频监控流对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="videoStreamRef" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="视频流名称" prop="streamName">
          <el-input v-model="form.streamName" placeholder="请输入视频流名称" />
        </el-form-item>
        <el-form-item label="推流设备ID" prop="deviceId">
          <el-input v-model="form.deviceId" placeholder="请输入推流设备ID" />
        </el-form-item>
        <el-form-item label="推流设备名称" prop="deviceName">
          <el-input v-model="form.deviceName" placeholder="请输入推流设备名称" />
        </el-form-item>
        <el-form-item label="视频流地址" prop="streamUrl">
          <el-input v-model="form.streamUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="协议类型" prop="protocol">
          <el-select v-model="form.protocol" placeholder="请选择协议类型">
            <el-option
              v-for="dict in cbs_protocoltype"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="推流开始时间" prop="startTime">
          <el-date-picker
            clearable
            v-model="form.startTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            placeholder="请选择推流开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="推流结束时间" prop="endTime">
          <el-date-picker
            clearable
            v-model="form.endTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            placeholder="请选择推流结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="推流状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in cbs_flvstatus"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="实时码率" prop="bitrateKbps">
          <el-input v-model="form.bitrateKbps" placeholder="请输入实时码率" />
        </el-form-item>
        <el-form-item label="分辨率" prop="resolution">
          <el-input v-model="form.resolution" placeholder="请输入分辨率" />
        </el-form-item>
        <el-form-item label="帧率" prop="frameRate">
          <el-input v-model="form.frameRate" placeholder="请输入帧率" />
        </el-form-item>
        <el-form-item label="推流端IP地址" prop="uploadIp">
          <el-input v-model="form.uploadIp" placeholder="请输入推流端IP地址" />
        </el-form-item>
        <el-form-item label="位置名称" prop="locationName">
          <el-input v-model="form.locationName" placeholder="请输入位置名称" />
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="请输入纬度" />
        </el-form-item>
        <el-form-item label="海拔高度" prop="altitude">
          <el-input v-model="form.altitude" placeholder="请输入海拔高度" />
        </el-form-item>
        <el-form-item label="位置/区域编码" prop="locationCode">
          <el-input v-model="form.locationCode" placeholder="请输入位置/区域编码" />
        </el-form-item>
        <el-form-item label="备注信息" prop="remarks">
          <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="VideoStream">
import {
  listVideoStream,
  getVideoStream,
  delVideoStream,
  addVideoStream,
  updateVideoStream,
} from "@/api/xtcontractormag/FieldOperation/videoStream";
import { deepClone, formatDate } from "@/utils";
import { getToken } from "@/utils/auth";
import useUserStore from "@/store/modules/user";

const userStore = useUserStore();

const { proxy } = getCurrentInstance();

const { cbs_protocoltype } = proxy.useDict("cbs_protocoltype");
const { cbs_flvstatus } = proxy.useDict("cbs_flvstatus");

const videoStreamList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const itemsArr = ref([]);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层
  open: false,
  // 弹出层标题
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: window.xtmapConfig.xtBaseUrl + "/contractor/videoStream/importData",
});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    deviceName: null,
    protocol: null,
    endTime: null,
    status: null,
    bitrateKbps: null,
    resolution: null,
    frameRate: null,
    uploadIp: null,
    locationName: null,
    longitude: null,
    latitude: null,
    altitude: null,
    locationCode: null,
    remarks: null,
  },
  rules: {
    streamId: [{ required: true, message: "视频流唯一标识不能为空", trigger: "blur" }],
    streamName: [{ required: true, message: "视频流名称不能为空", trigger: "blur" }],
    deviceId: [{ required: true, message: "推流设备ID不能为空", trigger: "blur" }],
    deviceName: [{ required: true, message: "推流设备名称不能为空", trigger: "blur" }],
    streamUrl: [{ required: true, message: "视频流地址不能为空", trigger: "blur" }],
    protocol: [{ required: true, message: "协议类型不能为空", trigger: "blur" }],
    startTime: [{ required: true, message: "推流开始时间不能为空", trigger: "blur" }],
    endTime: [{ required: true, message: "推流结束时间不能为空", trigger: "blur" }],
    status: [{ required: true, message: "推流状态不能为空", trigger: "change" }],
    bitrateKbps: [{ required: true, message: "实时码率不能为空", trigger: "blur" }],
    resolution: [{ required: true, message: "分辨率不能为空", trigger: "blur" }],
    frameRate: [{ required: true, message: "帧率不能为空", trigger: "blur" }],
    uploadIp: [{ required: true, message: "推流端IP地址不能为空", trigger: "blur" }],
    locationName: [{ required: true, message: "位置名称不能为空", trigger: "blur" }],
    longitude: [{ required: true, message: "经度不能为空", trigger: "blur" }],
    latitude: [{ required: true, message: "纬度不能为空", trigger: "blur" }],
    altitude: [{ required: true, message: "海拔高度不能为空", trigger: "blur" }],
    locationCode: [{ required: true, message: "位置/区域编码不能为空", trigger: "blur" }],
    remarks: [{ required: true, message: "备注信息不能为空", trigger: "blur" }],
    createBy: [{ required: true, message: "创建人不能为空", trigger: "blur" }],
    createTime: [{ required: true, message: "创建时间不能为空", trigger: "blur" }],
    updateBy: [{ required: true, message: "更新人不能为空", trigger: "blur" }],
    updateTime: [{ required: true, message: "更新时间不能为空", trigger: "blur" }],
    deleted: [{ required: true, message: "是否删除不能为空", trigger: "blur" }],
    tenantId: [{ required: true, message: "租户ID不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询视频监控流列表 */
function getList() {
  loading.value = true;
  listVideoStream(queryParams.value).then((response) => {
    videoStreamList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    streamId: null,
    streamName: null,
    deviceId: null,
    deviceName: null,
    streamUrl: null,
    protocol: null,
    startTime: null,
    endTime: null,
    status: null,
    bitrateKbps: null,
    resolution: null,
    frameRate: null,
    uploadIp: null,
    locationName: null,
    longitude: null,
    latitude: null,
    altitude: null,
    locationCode: null,
    remarks: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    deleted: null,
    tenantId: null,
  };
  proxy.resetForm("videoStreamRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  itemsArr.value = deepClone(selection);
  multiple.value = !selection.length;
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "导入";
  upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download(
    "contractor/videoStream/export/template",
    {},
    `videoStream_${new Date().getTime()}.xlsx`
  );
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
      response.msg +
      "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  upload.url = `${window.xtmapConfig.xtBaseUrl}/contractor/videoStream/importData?userId=${userStore.id}`;
  proxy.$refs["uploadRef"].submit();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加视频监控流";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getVideoStream(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改视频监控流";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["videoStreamRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        const data = {
          ...form.value,
          tenantId: userStore.tenantId,
          updateBy: userStore.name,
          updateTime: formatDate(new Date()),
        };
        updateVideoStream(data).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        const data = {
          ...form.value,
          tenantId: userStore.tenantId,
          createBy: userStore.name,
          createTime: formatDate(new Date()),
          updateBy: userStore.name,
          updateTime: formatDate(new Date()),
          deleted: "0",
        };
        addVideoStream(data).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id ? [row.id] : ids.value;
  proxy.$modal
    .confirm('是否确认删除视频监控流编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delVideoStream(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  const params = itemsArr.value.length > 0 ? itemsArr.value : undefined;
  proxy.codeDownload(
    "contractor/videoStream/export",
    params,
    `videoStream_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
