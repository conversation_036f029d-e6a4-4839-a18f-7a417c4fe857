import { getCoorFromPx, cart3ToDegree } from './getCoorFromPx.js';
// 有三种type : cztype（哪种entity）,pictype(哪张图),busitype(图是哪一类）
// 					)
class PlotCircle {
	constructor(viewer) {
		this.viewer = viewer;

		this.plotHandler = null;

		this.myCollection = null;
		this.yourCollection = null;

		// 点集和线是分开绘制的，所以为左键点集合声明一个CustomDataSource
		this.tempPointArr;

		this.floatingPoint = undefined;
		// this.poiArr = [];
		this.radius = 1;
		this.centre = null;
		this.circleEntity = undefined; //当前正在绘制的  随着鼠标移动变化的线
		this.init(this.viewer);

		this.jsonCollection = []; // 用json组织数据  json结构需要统一
		// class extend 形式 写个绘制基类  其他的线、面都去extend
	}
	init(viewer) {
		this.myCollection = new Cesium.CustomDataSource('myplotdata');
		this.yourCollection = new Cesium.CustomDataSource('yourplotdata');
		this.viewer.dataSources.add(this.myCollection);
		this.viewer.dataSources.add(this.yourCollection);
		this.tempPointArr = new Cesium.CustomDataSource('poiPolygonData');
		this.viewer.dataSources.add(this.tempPointArr);
	}
	draw(options = { picType: 'p1111', czType: '', busiType: '' }) {
		this.plotHandler = new Cesium.ScreenSpaceEventHandler(
			this.viewer.scene.canvas
		);
		this.plotHandler.setInputAction(
			this.leftEvent(options),
			Cesium.ScreenSpaceEventType.LEFT_CLICK
		);
		this.plotHandler.setInputAction(
			this.moveEvent,
			Cesium.ScreenSpaceEventType.MOUSE_MOVE
		);
		return new Promise((resolve, reject) => {
			this.plotHandler.setInputAction(
				this.rightEvent(resolve, reject, options),
				Cesium.ScreenSpaceEventType.RIGHT_CLICK
			);
		});
	}
	leftEvent(options) {
		const r = (e) => {
			let earthPosition = getCoorFromPx({
				viewer: this.viewer,
				pxCoor: e.position,
				resultType: 'xyz',
			});

			if (Cesium.defined(earthPosition)) {
				if (this.centre === null) {
					this.floatingPoint = this.createPoint(
						earthPosition,
						'左键绘制，右键结束'
					);
					this.viewer.entities.add(this.floatingPoint);

					this.centre = earthPosition;

					let dynamicPositions = new Cesium.CallbackProperty(() => {
						return this.radius;
					}, false);
					// circle的这个cb回调要写在外面
					this.circleEntity = this.createCircle(
						this.centre,
						dynamicPositions,
						options
					);
					this.myCollection.entities.add(this.circleEntity);

					const tempPoint = this.createPoint(earthPosition);
					this.tempPointArr.entities.add(tempPoint);
				}
			}
		};
		return r;
	}
	moveEvent = (e) => {
		let newPosition = getCoorFromPx({
			viewer: this.viewer,
			pxCoor: e.endPosition,
			resultType: 'xyz',
		});

		if (Cesium.defined(this.floatingPoint) && Cesium.defined(newPosition)) {
			this.floatingPoint.position.setValue(newPosition);
			const centreCoor = Cesium.Cartographic.fromCartesian(this.centre);
			const endCoor = Cesium.Cartographic.fromCartesian(newPosition);
			const geodesic = new Cesium.EllipsoidGeodesic();
			geodesic.setEndPoints(centreCoor, endCoor);
			const distance = geodesic.surfaceDistance;
			this.radius = parseFloat(distance.toFixed(3));
		}
	};
	rightEvent(resolve, reject, options) {
		const r = (e) => {
			if (this.centre === null) {
				return;
			}
			let Position = getCoorFromPx({
				viewer: this.viewer,
				pxCoor: e.position,
				resultType: 'xyz',
			});
			if (Cesium.defined(Position)) {
				let endPoint = this.createPoint(Position, '');
				this.tempPointArr.entities.add(endPoint);

				this.viewer.entities.remove(this.floatingPoint);
				this.floatingPoint = null;

				const singleC = this.centre;
				const singleR = this.radius;
				const singleEntity = this.createCircle(
					singleC,
					singleR,
					options
				);
				this.myCollection.entities.add(singleEntity);

				this.myCollection.entities.remove(this.circleEntity);
				this.circleEntity = null;
				this.centre = null;
				this.radius = null;

				this.tempPointArr.entities.removeAll();

				let resultCenter = cart3ToDegree([singleC]); // 从cartesian到degree
				let resultRadius = singleR;
				console.log(resultCenter, 'resultCenter');

				const plotInfo = {
					name: 'xxx',
					params: {
						centerlon: resultCenter[0][0],
						centerlat: resultCenter[0][1],
						radius: resultRadius,
					},
					position: singleC,
					radius: singleR,
					czType: options.czType,
					busiType: options.busiType,
					picType: options.picType,
					entityOptions: options,
				};

				this.destroyHandler();
				resolve(plotInfo);
				reject('err----');
			}
		};
		return r;
	}
	reDraw(options) {
		const e = this.createCircle(
			options.position,
			options.radius,
			options.entityOptions
		);
		this.yourCollection.entities.add(e);
	}

	createCircle(position, r, options) {
		/* let semi = new Cesium.CallbackProperty(() => {
			return radius;
		}, false); */
		const shape = new Cesium.Entity({
			position: position,
			// position: Cesium.Cartesian3.fromDegrees(113.0, 25.0),
			ellipse: {
				semiMinorAxis: r,
				semiMajorAxis: r,
				/* semiMinorAxis: new Cesium.CallbackProperty(() => {
					return r;
				}, false),
				semiMajorAxis: new Cesium.CallbackProperty(() => {
					return r; 
				}, false),*/
				fill: true,
				material:
					Cesium.Color.fromCssColorString(
						'rgb(20, 120, 255)'
					).withAlpha(0.33),
			},
		});
		return shape;
	}
	createPoint(poi, txt) {
		let point = new Cesium.Entity({
			position: poi,
			point: {
				color: Cesium.Color.YELLOW.withAlpha(0.6),
				pixelSize: 6,
				// heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
			},
			label: {
				text: txt || '',
				font: 'bold 12px MicroSoft YaHei',
				outlineWidth: 2,

				horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
				verticalOrigin: Cesium.VerticalOrigin.TOP,
				pixelOffset: new Cesium.Cartesian2(15, 0),
				// pixelOffset: new Cesium.Cartesian2(-15, 16), //偏移量
				showBackground: true,
				backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
				backgroundPadding: new Cesium.Cartesian2(6, 3),
				heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
			},
		});
		return point;
	}
	getPlotData() {
		return this.jsonCollection;
	}
	setPlotData(data) {
		this.reDraw(data);

		/* for (const i of data) {
			this.reDraw(i);
		} */
	}
	clearData() {
		this.myCollection.entities.removeAll();
		this.yourCollection.entities.removeAll();
	}
	destroyHandler() {
		// this.plotHandler.removeInputAction(
		// 	Cesium.ScreenSpaceEventType.LEFT_CLICK
		// );

		if (this.plotHandler) {
			this.plotHandler.destroy();
		}
		this.plotHandler = null;
	}
}

export { PlotCircle };
