<template>
  <!-- 新版，以geojson的形式加载矢量交通要素 -->
  <div class="thematic">
    <div class="thematic-panel">
      <thematic-map
        ref="thematicMap"
        :fourCoor="zttCoor"
        :winCoor="winCoor"
        @closeZtMap="closeZtMap"
        v-if="ztMapShow"
      ></thematic-map>
    </div>
    <div class="thematic-menu">
      <el-button @click="toggleZTT"> 制图 </el-button>
    </div>
  </div>

  <!-- <CommonPlotComp v-show="commonEditPlotShow"></CommonPlotComp> -->
</template>

<script>
import DrawRectangle from "./DrawRectangleZTT.js";
import thematicMap from "./thematicMap.vue";

export default {
  name: "Thematic",
  components: {
    thematicMap,
  },
  data() {
    return {
      zttCoor: {},
      winCoor: {},
      ztMapShow: false,
    };
  },
  mounted() {
    // this.addBusEvent();
  },
  methods: {
    // 初始化viewer对象

    /*  addBusEvent() {
      Bus.$on("ThematicMapComp-makeZtMap", () => {
        this.ztMapShow = true;
        this.$nextTick(() => {
          this.$refs.thematicMap.initZT();
        });
      });
      Bus.$on("ThematicMapComp-closeDiv", () => {
        this.ztMapShow = false;
      });
    }, */

    closeZtMap() {
      this.ztMapShow = false;
    },

    toggleZTT() {
      // 手绘制图范围
      this.$message.success("请在地图上框选制图范围");
      new DrawRectangle();
      emitter.on("DrawRectangle_finish", (d) => {
        this.winCoor = d.windowCoor;
        this.zttCoor = d.fourCoor;
        console.log(this.winCoor, "this.winCoor");
        console.log(this.zttCoor, "this.zttCoor");
        this.ztMapShow = true;
        this.$nextTick(() => {
          this.$refs.thematicMap.initZT();
        });
      });
      /* PlotBus.$on("DrawRectangle_finish", (d) => {
        this.winCoor = d.windowCoor;
        this.zttCoor = d.fourCoor;
        console.log(this.winCoor, "this.winCoor");
        console.log(this.zttCoor, "this.zttCoor");
        this.ztMapShow = true;
        this.$nextTick(() => {
          this.$refs.thematicMap.initZT();
        });
      }); */

      // this.$nextTick(() => {
      //   this.$refs.thematicMap.initZT();
      // });
    },

    // 获取当前视角的经纬度范围
    getViewRectangle() {
      let rect = viewer.camera.computeViewRectangle();
      let minLon = Cesium.Math.toDegrees(rect.west);
      console.log(minLon, "minLon");
      let maxLon = Cesium.Math.toDegrees(rect.east);
      let minLat = Cesium.Math.toDegrees(rect.south);
      let maxLat = Cesium.Math.toDegrees(rect.north);
      return {
        minLon: minLon,
        maxLon: maxLon,
        minLat: minLat,
        maxLat: maxLat,
      };
    },
  },
};
</script>

<style scoped>
.thematic {
  position: absolute;
  width: 100%;
  height: 100%;
}

#cesiumContainer {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0px;
}

#slider {
  position: absolute;
  left: 50%;
  top: 90px;
  background-color: #ff0000;
  width: 5px;
  height: calc(100% - 90px);
  z-index: 9999;
}

#slider:hover {
  cursor: ew-resize;
}

/* 大理 */

#dl-title {
  position: absolute;
  top: 0px;
  left: 0;
  width: 100%;
  height: 75px;
  /* background-color: rgba(35, 118, 202, 0.774); */
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: url(../assets/a.gif);
}
.dl-title-txt {
  font-size: 37px;
  color: rgb(248, 248, 248);
}

#dl-tool {
  position: absolute;
  bottom: 0px;
  right: 0;
  height: 50px;
  /* width: 500px; */
  background-color: rgba(6, 44, 44, 0.705);
  display: flex;
  flex-direction: row;
}

.dl-item {
  border: solid 1px rgb(26, 233, 233);
  padding: 5px 20px;
  display: flex;
  align-items: center;
  color: whitesmoke;
  font-size: 18px;
}

.dl-item:hover {
  cursor: pointer;
}

.dl-item :deep(.el-radio__label)  {
  font-size: 18px;
}

.dl-item :deep(.el-radio.is-bordered)  {
  height: 32px;
  padding: 5px 10px 0 10px;
  margin-right: 0;
}

.dl-basemap {
}

#celiangxt {
  position: absolute;
  bottom: 300px;
  right: 0;
  width: 200px;
}

#biaohuixt {
  position: absolute;
  top: 535px;
  /* top: -50px; */
  left: 525px;
}

.dl-item :deep(.el-switch__label--right)  {
  color: rgb(43, 156, 255);
  /* color: rgb(255, 255, 255); */
  font-size: 18px !important;
}

.dl-item :deep(.el-switch__label *)  {
  color: rgb(255, 255, 255);
  font-size: 18px !important;
}

#tuli {
  width: 230px;
  height: 168px;
  background-color: rgba(118, 139, 134, 0.252);
  position: absolute;
  bottom: 100px;
  right: 30px;
  background-image: url("../assets/ss.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-top: 12px;
}

.tili-li {
  display: flex;
  color: white;
  height: 30px;
  list-style-type: none;
  padding-left: 10px;
  align-items: center;
  justify-content: space-evenly;
}
</style>
