<template>
  <div class="router">
    <div class="router-path">
      <!-- <div class="router-header">路径规划</div> -->
      <div class="router-menu">
        <LocationInput
          label="起点"
          :point="startPoint"
          @pick="positionPick('start')"
        />
        <LocationInput
          label="终点"
          :point="endPoint"
          @pick="positionPick('end')"
        />
      </div>
    </div>
    <!-- <div class="router-position">
      <div class="header-with-add">
        <div class="router-header">必经点</div>
        <div class="positionPick-add" @click="positionPick('center')"></div>
      </div>
      <div class="router-menu">
        <el-table :data="tableData" style="width: 100%" max-height="150">
          <el-table-column prop="longitude" align="center" label="经度" />
          <el-table-column prop="latitude" align="center" label="纬度" />
          <el-table-column align="center" label="操作">
            <template #default="scope">
              <div
                class="deleteIcon"
                @click="handleDeleteClick(scope.$index)"
              ></div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div> -->

    <!-- <div class="router-region">
      <div class="header-with-add">
        <div class="router-header">绕行区域</div>
        <div class="positionPick-add" @click="positionPickPolygon()"></div>
      </div>
      <div class="router-menu">
        <el-table :data="tableDataRegion" style="width: 100%" max-height="150">
          <el-table-column
            prop="name"
            label="绕行区域"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column align="center" label="操作">
            <template #default="scope">
              <div
                class="deleteIcon"
                @click="handleDeleteRegionClick(scope.$index)"
              ></div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div> -->

    <!-- <div class="router-setting">
      <el-form-item label="算法类型" prop="">
        <el-radio
          v-for="option in radioTypeOptions"
          :key="option.id"
          v-model="plaing_form.algorithmType"
          :label="option.value"
        >
          {{ option.label }}
        </el-radio>
      </el-form-item>

      <el-form-item label="规划策略" prop="">
        <el-radio
          v-for="option in radioOptions"
          :key="option.id"
          v-model="plaing_form.policy"
          :label="option.value"
        >
          {{ option.label }}
        </el-radio>
      </el-form-item>
    </div> -->
    <div class="router-button">
      <div class="masbutton cancer" @click="resetForm()">
        <span>清空规划</span>
      </div>
      <div class="masbutton submit" @click="submitForm()">
        <span>开始规划</span>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ElMessage } from "element-plus";
import { lineRouterService } from "@/api/command/index";
import LocationInput from "@/earthCompStyle/RoutePlan/LocationInput.vue"; // 假设我们创建了这个组件
const radioTypeOptions = [
  { id: 1, value: true, label: "最短路径" },
  { id: 2, value: false, label: "最优路径" },
];
const radioOptions = [
  { id: 1, value: 0, label: "徒步" },
  { id: 2, value: 1, label: "高速优先" },
  { id: 3, value: 2, label: "不走高速" },
  // { id: 3, value: 3, label: "铁路优先" },
];
const plaing_form = reactive({
  avoidRegions: [],
  policy: 2,
  lnglats: [],
  level: 2,
  algorithmType: false,
  bufferRadius: 50,
});
const tableData = ref([]);
const tableDataRegion = ref([]);
const labelMarkers = ref([]); // 用于存储所有标注
const bywaylabelMarkers = ref([]); // 用于存储所有标注
const startlabelMarkers = ref([]); // 用于存储所有标注
const endlabelMarkers = ref([]); // 用于存储所有标注
let pointpick_handler = reactive();

let polygonHandler = ref(null);
let positions = [];
let polygonEntity = ref(null);
let polygonEntityFINAL = ref(null);
const initEntity = () => {
  // 初始化其他实体的代码
};

const startPoint = reactive({ longitude: null, latitude: null });
const endPoint = reactive({ longitude: null, latitude: null });

onMounted(() => {
  emitter.on("viewerLoad", () => {
    initEntity();
  });
  if (window.viewer) {
    initEntity();
  }
});

const createLabelMarker = (position, text) => {
  return viewer.entities.add({
    position: position,
    label: {
      text: text,
      font: "16px sans-serif",
      fillColor: Cesium.Color.fromCssColorString("#00FDF7"),
      outlineColor: Cesium.Color.fromCssColorString("#00FDF7"),
      outlineWidth: 1,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(0, -10),
    },
    point: {
      pixelSize: 4,
      color: Cesium.Color.RED,
    },
  });
};

const removeLabelMarker = (marker) => {
  viewer.entities.remove(marker);
};
const handleDeleteClick = (index) => {
  const marker = bywaylabelMarkers.value[index];
  if (marker) {
    removeLabelMarker(marker);
  }
  tableData.value.splice(index, 1);
  bywaylabelMarkers.value.splice(index, 1);
};
const handleDeleteRegionClick = (index) => {
  const region = tableDataRegion.value[index];

  if (region && region.entity) {
    viewer.entities.remove(region.entity);
  }

  // 移除与这个区域相关的所有标记
  const labelsToRemove = labelMarkers.value.filter((marker) =>
    region.positions.some((pos, idx) =>
      Cesium.Cartesian3.equals(
        marker.position,
        Cesium.Cartesian3.fromDegrees(parseFloat(pos[0]), parseFloat(pos[1]))
      )
    )
  );

  labelsToRemove.forEach((label) => viewer.entities.remove(label));
  labelMarkers.value = labelMarkers.value.filter(
    (label) => !labelsToRemove.includes(label)
  );

  tableDataRegion.value.splice(index, 1);
};

const submitForm = () => {
  let newArr = [];
  let outputArr = [];
  let regionArray = [];
  plaing_form.lnglats = [];
  plaing_form.avoidRegions = [];
  // console.log(tableData.value, startPoint, endPoint, tableDataRegion.value);
  let start = [startPoint.longitude, startPoint.latitude];
  let end = [endPoint.longitude, endPoint.latitude];

  // 获取避让区域
  tableDataRegion.value.forEach((e) => {
    regionArray.push(e.positions);
  });

  plaing_form.avoidRegions = regionArray;
  // console.log(plaing_form.avoidRegions);
  // plaing_form.lnglats

  tableData.value.forEach((element, index) => {
    newArr.push(element.longitude, element.latitude);
  });
  outputArr = newArr.reduce((acc, val, i, arr) => {
    if (i % 2 === 0) {
      acc.push([val, arr[i + 1]]);
    }
    return acc;
  }, []);
  // console.log(outputArr);
  if (outputArr && outputArr.length > 0) {
    plaing_form.lnglats.push(start, ...outputArr, end);
  } else {
    plaing_form.lnglats.push(start, end);
  }

  // console.log(plaing_form);

  lineRouterService(plaing_form).then((res) => {
    let newpath = [];
    if (res.plans && res.plans.length > 0) {
      // console.log(res.plans[0].routes, "res");
      res.plans[0].routes.forEach((e) => {
        newpath.push(...e.path);
      });
      addTrack(newpath.flat(2));
    } else {
      ElMessage({
        message: res.message,
        type: "warning",
        plain: true,
      });
    }
  });
};
const addTrack = (path) => {
  // 销毁之前的线条
  const oldLineDataSource = window.viewer.dataSources.getByName("routeLine")[0];
  if (oldLineDataSource) {
    window.viewer.dataSources.remove(oldLineDataSource);
  }
  // 创建线条数据源
  const lineDataSource = new Cesium.CustomDataSource("routeLine");
  lineDataSource.entities.add({
    name: "route",
    polyline: {
      positions: Cesium.Cartesian3.fromDegreesArray(path),
      width: 6,
      material: new Cesium.PolylineOutlineMaterialProperty({
        color: Cesium.Color.fromCssColorString("#11a4eded"), // 线条颜色
        outlineWidth: 2, // 外边框宽度
        outlineColor: Cesium.Color.fromCssColorString("#3CD5FF"), // 外边框颜色
      }),
      // clampToGround: true,
      zIndex: 10,
    },
  });

  // 添加数据源到视图
  window.viewer.dataSources.add(lineDataSource);
  // viewer.zoomTo(lineDataSource);
};

const resetForm = () => {
  tableData.value = [];
  tableDataRegion.value.forEach((region) => {
    if (region.entity) {
      viewer.entities.remove(region.entity);
    }
  });
  tableDataRegion.value = [];
  labelMarkers.value.forEach(removeLabelMarker);
  labelMarkers.value = [];
  bywaylabelMarkers.value.forEach(removeLabelMarker);
  bywaylabelMarkers.value = [];
  startlabelMarkers.value.forEach(removeLabelMarker);
  startlabelMarkers.value = [];
  startPoint.longitude = null;
  startPoint.latitude = null;
  endlabelMarkers.value.forEach(removeLabelMarker);
  endlabelMarkers.value = [];
  endPoint.longitude = null;
  endPoint.latitude = null;

  const oldLineDataSource = window.viewer.dataSources.getByName("routeLine")[0];
  if (oldLineDataSource) {
    window.viewer.dataSources.remove(oldLineDataSource);
  }
};

const createPolygon = (positions) => {
  return new Cesium.PolygonHierarchy(
    Cesium.Cartesian3.fromDegreesArray(positions)
  );
};
const positionPick = (val) => {
  if (pointpick_handler) {
    // console.log("you");

    pointpick_handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }
  /* if (JSON.stringify(pointpick_handler) !== "{}") {
    console.log("you");

    pointpick_handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
  } */

  pointpick_handler = new Cesium.ScreenSpaceEventHandler(
    window.viewer.scene.canvas
  );
  pointpick_handler.setInputAction((click) => {
    const cartesian = window.viewer.camera.pickEllipsoid(
      click.position,
      viewer.scene.globe.ellipsoid
    );
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    const longitude = Cesium.Math.toDegrees(cartographic.longitude);
    const latitude = Cesium.Math.toDegrees(cartographic.latitude);

    // Convert to fixed decimal places
    const longitudeFixed = parseFloat(longitude.toFixed(6));
    const latitudeFixed = parseFloat(latitude.toFixed(6));

    let position = Cesium.Cartesian3.fromDegrees(longitudeFixed, latitudeFixed);

    if (val === "start") {
      startlabelMarkers.value.forEach(removeLabelMarker);
      startlabelMarkers.value = [];
      startPoint.longitude = longitudeFixed;
      startPoint.latitude = latitudeFixed;
      // createLabelMarker(position, "起点");
      startlabelMarkers.value.push(createLabelMarker(position, "起点"));
    } else if (val === "end") {
      endlabelMarkers.value.forEach(removeLabelMarker);
      endlabelMarkers.value = [];
      endPoint.longitude = longitudeFixed;
      endPoint.latitude = latitudeFixed;
      // createLabelMarker(position, "终点");
      endlabelMarkers.value.push(createLabelMarker(position, "终点"));
    } else if (val === "center") {
      tableData.value.push({
        longitude: longitudeFixed,
        latitude: latitudeFixed,
      });
      // createLabelMarker(position, "必经点");
      bywaylabelMarkers.value.push(createLabelMarker(position, "必经点"));
    }

    removeHandler();
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
const positionPickPolygon = () => {
  positions = [];
  labelMarkers.value.forEach((marker) => viewer.entities.remove(marker));
  labelMarkers.value = [];

  if (polygonHandler.value) {
    polygonHandler.value.destroy();
    polygonHandler.value = null;
  }
  // if (polygonEntity.value) {
  //   viewer.entities.remove(polygonEntity.value);
  //   polygonEntity.value = null;
  // }

  polygonHandler.value = new Cesium.ScreenSpaceEventHandler(
    window.viewer.scene.canvas
  );
  polygonHandler.value.setInputAction((click) => {
    const cartesian = window.viewer.camera.pickEllipsoid(
      click.position,
      viewer.scene.globe.ellipsoid
    );
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian);

    const longitude = Cesium.Math.toDegrees(cartographic.longitude);
    const latitude = Cesium.Math.toDegrees(cartographic.latitude);

    const longitudeFixed = parseFloat(longitude.toFixed(6));
    const latitudeFixed = parseFloat(latitude.toFixed(6));

    positions.push(longitudeFixed, latitudeFixed);

    const markerPosition = Cesium.Cartesian3.fromDegrees(
      longitudeFixed,
      latitudeFixed
    );
    const label = createLabelMarker(
      markerPosition,
      `顶点 ${positions.length / 2}`
    );
    labelMarkers.value.push(label);

    if (positions.length >= 6) {
      if (polygonEntity.value) {
        viewer.entities.remove(polygonEntity.value);
      }
      const tempPositions = positions.concat([positions[0], positions[1]]);
      polygonEntity.value = viewer.entities.add({
        polygon: {
          hierarchy: createPolygon(tempPositions),
          material: Cesium.Color.RED.withAlpha(0.5),
        },
      });
    } else if (positions.length >= 2) {
      if (polygonEntity.value) {
        viewer.entities.remove(polygonEntity.value);
      }
      polygonEntity.value = viewer.entities.add({
        polygon: {
          hierarchy: createPolygon(positions),
          material: Cesium.Color.RED.withAlpha(0.5),
        },
      });
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  polygonHandler.value.setInputAction(() => {
    if (positions.length >= 6) {
      viewer.entities.remove(polygonEntity.value);
      labelMarkers.value.forEach((marker) => viewer.entities.remove(marker));
      labelMarkers.value = [];
      const tempPositions = positions.concat([positions[0], positions[1]]);
      const polygonHierarchy = createPolygon(tempPositions);

      polygonEntityFINAL.value = viewer.entities.add({
        polygon: {
          hierarchy: polygonHierarchy,
          material: Cesium.Color.RED.withAlpha(0.5),
        },
      });
    }
    tableDataRegion.value.push({
      name: "绕行区域",
      positions: positions,
      entity: polygonEntityFINAL.value,
    });

    polygonHandler.value.destroy();
    polygonHandler.value = null;
  }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
};
const removeHandler = () => {
  // entity.show = false;
  pointpick_handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
onDeactivated(() => {
  resetForm();
});
</script>
<style scoped lang="scss">
.router::before {
  content: "";
  position: absolute;
  width: 358px;
  // min-height: 728px;
  height: max-content;
  z-index: -1;
  box-sizing: border-box;

  // background: rgba(8, 76, 124, 0.5);
  // border: 2px solid;
  // border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%)
  //   2;
  // backdrop-filter: blur(10px);
}

.router {
  width: 358px;
  // min-height: 728px;
  height: max-content;
  display: flex;
  flex-direction: column;
  // justify-content: space-between;
  // background: rgba(8, 76, 124, 0.5);
  // box-sizing: border-box;
  // border: 2px solid;
  // border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%) 2;
  .router-path {
    // margin-top: 10px;
    // padding: 10px 0px;
  }
  .router-header {
    background: url("@/assets/xtui/tools/dialogopen.png") no-repeat center
      center;
    background-size: 100% 100%;
    width: 184.04px;
    height: 32px;
    padding: 5px 30px;
    margin: 5px 10px;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: bold;
    color: aliceblue;
  }

  .router-menu {
    padding: 10px 10px;

    .deleteIcon {
      width: 100%;
      height: 12px;
      background: url("@/assets/xtui/tools/deleteicon.png") no-repeat center
        center;
    }

    .router-menu-point {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      width: 100%;
      align-items: center;
      margin: 5px 0px;

      span {
        font-family: 思源黑体;
        font-size: 12px;
        font-weight: normal;
        line-height: 20px;
        text-align: right;
        color: #ffffff;
      }
    }
    :deep(.el-table) {
      background-color: transparent;
      // border-spacing: 0;
      // border-collapse: collapse;
      --el-table-border-color: rgba(203, 203, 203, 0);
      // 表格边框的样式，一般为实线或虚线，可以通过这个变量来设置表格的边框样式。
      --el-table-border: 1px solid rgba(203, 203, 203, 0);
    }
    :deep(.el-table tr) {
      background-color: transparent;
      // background: rgba(14, 95, 255, 0.2);
      height: 36px;
    }

    :deep(.el-table tr th) {
      // background-color: transparent;
      background-color: rgba(221, 96, 19, 0.808);
      --el-table-border-color: rgba(203, 203, 203, 0.687);
      color: rgb(255, 255, 255);
    }

    :deep(.el-table td) {
      // background-color: transparent !important;
      // border: oldlace 1px solid;
      // border-spacing: 0;
      // border-collapse: collapse;
      color: #ffffff;
      // height: 30px;
      padding: 0;
    }

    :deep(.el-table__row) {
      --el-table-row-hover-bg-color: rgba(55, 111, 225, 0.4);
      // 表格边框的颜色，可以通过这个变量来设置表格的边框颜色。
      --el-table-border-color: rgba(203, 203, 203, 0.687);
      // 表格边框的样式，一般为实线或虚线，可以通过这个变量来设置表格的边框样式。
      --el-table-border: 1px solid rgba(203, 203, 203, 0.687);

      // 表格的背景色，可以通过这个变量来设置表格的背景色。
      // --el-table-bg-color: rgba(255, 255, 255, 0);
      // 表格行的背景色，可以通过这个变量来设置表格行的背景色。
      // --el-table-tr-bg-color: rgba(255, 255, 255, 0);
    }
  }
  .router-setting {
    :deep(.el-form-item--default) {
      margin-bottom: 3px;
    }
    :deep(.el-form-item__label) {
      color: #ffffff;
    }

    :deep(.el-form-item--default) {
      padding: 0px 20px;
    }
    :deep(.el-radio) {
      color: #ffffff;
      margin-right: 22px;
    }
    :deep(.el-radio__inner) {
      border-radius: 0px;
    }
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #3cd5ff;
    }
    :deep(.el-radio__input.is-checked .el-radio__inner) {
      border-color: #37c2e7;
      background: #3cd5ff;
    }
    :deep(.el-form-item__content) {
      flex-wrap: nowrap;
    }
  }

  .header-with-add {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 15px;
    .positionPick-add {
      cursor: pointer;
      width: 32px;
      height: 32px;
      background-repeat: no-repeat;
      background-image: url("@/assets/xtui/tools/addimg.png");
    }
  }

  .router-button {
    margin-top: 10px;
    width: 100%;
    display: flex;
    justify-content: space-evenly;
    // position: relative;
    // bottom: 10px;
    .masbutton {
      width: 100px;
      height: 32px;
      color: #ffffff;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      text-align: center;
      display: flex;
      align-content: center;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      &.submit {
        background-image: url("@/assets/xtui/command/onduty/sunmitback.png");
      }
      &.cancer {
        background-image: url("@/assets/xtui/command/onduty/cancleback.png");
      }
    }
  }
}

.positionPick {
  cursor: pointer;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
  background-image: url("@/assets/xtui/tools/pickimg.png");
}
</style>
