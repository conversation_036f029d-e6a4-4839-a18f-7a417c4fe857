<template>
  <div class="right-base">right</div>
</template>

<script setup>
import { nextTick, ref, reactive, computed, onMounted } from "vue";
const state = reactive({ count: 0 });

onMounted(() => {});
</script>

<style scoped lang="scss">
.right-base {
  background-color: rgba(205, 163, 127, 0.13);
  height: 100vh;
  width: 350px;

  // background: url("@/assets/xtui/layout/leftbg.png");
  // background-size: 100% 100%;
}
</style>

