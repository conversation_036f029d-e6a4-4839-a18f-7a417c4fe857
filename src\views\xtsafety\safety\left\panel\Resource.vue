<template>
  <div class="resource">
    <div
      class="resource-list"
      v-for="(item, index) in resourceList"
      :key="item.id"
    >
      <div :class="item.imgClass"></div>
      <div class="resource-list-des">
        <span class="desvalue">{{ item.value }}</span>
        <span class="desword">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  resourceList: {
    type: Array,
    required: false,
    default: [
      {
        id: 1,
        imgClass: "resource-list-expert",
        value: "15",
        name: "应急专家",
      },
      {
        id: 2,
        imgClass: "resource-list-store",
        value: "15",
        name: "应急仓库",
      },
      /* {
        id: 3,
        imgClass: "resource-list-medical",
        value: "15",
        name: "救援机构",
      }, */
      {
        id: 4,
        imgClass: "resource-list-team",
        value: "15",
        name: "应急队伍",
      },
      {
        id: 5,
        imgClass: "resource-list-materials",
        value: "15",
        name: "应急物资",
      },
      /* {
        id: 6,
        imgClass: "resource-list-refuge",
        value: "15",
        name: "避难场所",
      }, */
    ],
  },
});
onMounted(() => {});
</script>

<style scoped lang="scss">
.resource {
  width: 400px;
  height: 180px;
  //   font-family: ysbthzt;
  font-family: Source Han Sans;
  font-size: 16px;
  font-weight: bold;
  line-height: normal;
  letter-spacing: 0em;
  color: #b0c1d5;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-around;
  align-items: center;
  align-content: space-evenly;
  .resource-list {
    display: flex;
    width: 43%;
    justify-content: center;
  }
  .resource-list-des {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-evenly;
    width: 100px;
    .desword {
      font-size: 15px;
      color: #ffffff;
      //   color: #3cd5ff;
    }
    .desvalue {
      // font-family: ysbthzt;
      font-style: normal;
      font-weight: normal;
      font-size: 28px;
      color: #3cd5ff;
    }
  }
  .resource-list-expert,
  .resource-list-store,
  .resource-list-medical,
  .resource-list-team,
  .resource-list-materials,
  .resource-list-refuge {
    height: 60px;
    width: 60px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .resource-list-expert {
    background: url("@/assets/xtui/command/onduty/expert.png");
  }

  .resource-list-store {
    background: url("@/assets/xtui/command/onduty/store.png");
  }

  .resource-list-medical {
    background: url("@/assets/xtui/command/onduty/medical.png");
  }

  .resource-list-team {
    background: url("@/assets/xtui/command/onduty/team.png");
  }
  .resource-list-materials {
    background: url("@/assets/xtui/command/onduty/materials.png");
  }

  .resource-list-refuge {
    background: url("@/assets/xtui/command/onduty/refuge.png");
  }
}
</style>
