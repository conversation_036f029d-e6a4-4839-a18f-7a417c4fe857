<template>
  <div class="bottom-base">
    <div class="bottom-base-top"></div>
    <el-table
      :data="eventList"
      class="table-size"
      width="100%"
      :row-style="tableRowStyle"
      :cell-style="cellStyle"
      :header-cell-style="tableHeaderColor"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column
        label="事故名称"
        prop="accidentName"
        align="left"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="事故装置"
        prop="accidentDevice"
        align="center"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="事故类型"
        prop="typeOfAccident"
        align="center"
        :show-overflow-tooltip="true"
        width="100"
      />
      <el-table-column
        label="涉及危化品"
        prop="hazardousChemicals"
        align="center"
        :show-overflow-tooltip="true"
        width="105"
      />
      <el-table-column
        label="关联企业名称"
        prop="companyId"
        align="left"
        :show-overflow-tooltip="true"
        width="180"
      />
      <el-table-column
        label="事故发生时间"
        prop="startTime"
        align="center"
        :show-overflow-tooltip="true"
      />
      <!-- <el-table-column
        label="初始处置状态"
        prop="dispositionStatus"
        align="center"
        :show-overflow-tooltip="true"
      >
         <template slot-scope="scope">
          <dict-tag
            align="center"
            :options="dict.type.xt_command_dispositionstatus"
            :value="scope.row.dispositionStatus"
          />
        </template>
      </el-table-column> -->

      <el-table-column
        label="事件状态"
        prop="status"
        :show-overflow-tooltip="true"
        align="center"
      >
        <template #default="scope">
          <span v-if="scope.row.status === '1'" style="color: #f71d1d">
            待处理</span
          >
          <span v-else-if="scope.row.status === '2'" style="color: #dfde1d">
            处理中</span
          >
          <span v-else="scope.row.status === '3'" style="color: #67c23a">
            已处理</span
          >
        </template>
        <!-- <template slot-scope="scope">
          <dict-tag
            :options="dict.type.xt_command_eventstatus"
            :value="scope.row.status"
          />
        </template> -->
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { getEventList } from "@/api/cockpit/index";
import { nextTick, ref, onMounted } from "vue";

onMounted(() => {
  getDataList();
});

const eventList = ref([]);
function getDataList() {
  getEventList().then((res) => {
    if (res.code === 200) {
      nextTick(() => {
        /* for (const i of res.rows) {
          if (i.status === "1") {
            i.status = "待处理";
          } else if (i.status === "2") {
            i.status = "处理中";
          } else if (i.status === "3") {
            i.status = "已处理";
          }
        } */
        eventList.value = res.rows;
      });
    } else {
      eventList.value = [];
    }
  });
}

const tableRowStyle = (row) => {
  return row.rowIndex % 2 === 0
    ? { background: "rgba(14, 95, 255, 0)", height: "36px" }
    : // : { background: "rgba(14, 95, 255, 0.3)" };
      { background: "rgba(64, 158, 255, 0.1)", height: "36px" };
};
const cellStyle = {
  // background: "transparent",
  color: "#ffffff",
};
const tableHeaderColor = {
  background: "rgba(64, 158, 255, 0.8)",
  // background: "rgba(14, 95, 255, 0.2)",
  color: "#ffffff",
  fontSize: "12px",
  textAlign: "center",
};
</script>

<style scoped lang="scss">
.bottom-base {
  width: 100%;
  height: 210px;
  .bottom-base-top {
    background-image: url("@/assets/xtui/cockpit/tbtop.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 26px;
  }
  :deep(.el-table) {
    background-color: transparent;
    overflow: auto;
    height: 190px;

    // border-spacing: 0;
    // border-collapse: collapse;
  }
  :deep(.el-table tr) {
    background-color: transparent;
    background: rgba(5, 27, 86, 0.4);
  }
}
</style>
