export const command = defineStore("command", {
  state: () => ({
    isPanelShow:false,
    handledata: [],
    onTimeData: [],
    xttidEventsInfo:{},
  }),
  actions: {
    setData(newData) {
      this.handledata = newData;
    },
    setonTimeData(newData) {
      this.onTimeData = newData;
    },
    setIsPanelVisible(val){
      this.isPanelShow = val
    },
    setXttidEventsInfo(val){
      this.xttidEventsInfo = val
    }
  },
});
