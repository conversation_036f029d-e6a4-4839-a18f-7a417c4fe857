<template>
  <div class="device-status">
    <div class="device-item left">
      <div class="txt-top left">设备在线数</div>
      <div class="txt-mid left">112</div>
    </div>
    <div class="device-item right">
      <div class="txt-top right">设备离线数</div>
      <div class="txt-mid right">7</div>
    </div>
  </div>
</template>

<script>
export default {};
</script>

<style lang="scss" scoped>
.device-status {
  width: 380px;
  height: 115px;
  display: flex;
  justify-content: space-evenly;
  .device-item {
    width: 160px;
    height: 90px;
    margin-top: 20px;
    margin-bottom: 20px;
    background-size: contain;
    background-repeat: no-repeat;

    &.left {
      //   background-color: #31734a;
      background-image: url("@/assets/xtui/cockpit/devicel.png");
    }
    &.right {
      background-image: url("@/assets/xtui/cockpit/devicer.png");
    }
    .txt-top {
      margin-top: 3px;
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: bold;
      line-height: normal;
      text-align: center;
      letter-spacing: 0em;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      &.left {
        color: #b2f9ff;
        text-shadow: 0px 0px 4px #3cd5ff;
      }
      &.right {
        color: #fff0da;
        text-shadow: 0px 0px 4px #f7d36f;
      }
    }
    .txt-mid {
      display: flex;
      justify-content: center;
      align-items: center;
      padding-top: 20px;
      color: #fff0da;
      font-size: 22px;
      font-weight: bold;
      &.left {
        color: #b2f9ff;
        text-shadow: 0px 0px 4px #3cd5ff;
      }
      &.right {
        color: #fff0da;
        text-shadow: 0px 0px 4px #f7d36f;
      }
    }
  }
}
</style>
