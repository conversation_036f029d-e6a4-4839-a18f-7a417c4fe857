import request from '@/utils/request'

// 查询监测小类
export function getMonitorSubcategory(query) {
  return request({
    url: '/mine/monitoringSubcategories',
    method: 'get',
    params: query
  })
}



export function addMonitorSubcategory(data) {
  return request({
    url: '/mine/monitoringSubcategories',
    method: 'post',
    data: data
  })
}


export function updateMonitorSubcategory(data) {
  return request({
    url: '/mine/monitoringSubcategories',
    method: 'post',
    data: data
  })
}


export function delMonitorSubcategory(ids) {
  return request({
    url: '/mine/monitoringSubcategories',
    method: 'delete',
    data: ids
  })
}
