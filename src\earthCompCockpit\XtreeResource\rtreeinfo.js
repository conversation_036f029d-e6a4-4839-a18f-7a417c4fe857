const resourceTreeData = [
	{
		id: 1,
		label: '车辆列表',
		children: [
			{
				id: 11,
				label: '晋A·UT9986',
				status: 0,
				disabled: 1,
				driver: '张华',
				type: '应急车',
				coor: { lon: 112.886121, lat: 36.099099 },
				lineArr: [111.11111, 36.099099, 111.11111, 36.099099],
				timer: null,
			},
			{
				id: 12,
				label: '晋A·CH5784',
				status: 1,
				disabled: false,
				driver: '李平',
				type: '巡检车',
				coor: { lon: 112.826121, lat: 36.099099 },
				lineArr: [111.11111, 36.099099, 111.11111, 36.099099],
				timer: null,
			},
			{
				id: 13,
				label: '晋A·PW8621',
				status: 1,
				disabled: false,
				driver: '王明',

				type: '巡检车',
				coor: { lon: 112.886121, lat: 36.099099 },
				lineArr: [111.11111, 36.099099, 111.11111, 36.099099],
				timer: null,
			},
		],
	},
];

export default resourceTreeData;

var ssdadaa = {
	id: '1781199430992207873',
	areaCode: '0244',
	parkCode: '0004',
	unitId: '02115',
	unitName: '大地救援队',
	unitType: '4',
	teamLevel: '1',
	businessArea: '2',
	affiliation: '和持科技有限公司',
	total: 142,
	director: '张春',
	telephone: '***********',
	address: ' 长治市长子县府前街大堡头中学北侧',
	longitude: 112.880773,
	latitude: 36.109011,
	deleted: '0',
	createDate: '2024-04-19 13:53:33',
	createBy: '管理员',
	updateDate: '2024-04-19 13:53:33',
	updateBy: '管理员',
};
const dfafaretw = {
	id: '1778723685232979970',
	name: '和记仓库',
	code: '1243',
	areaCode: '0244',
	affiliation: '和记科技有限公司',
	storeArea: 115332,
	address: '和记十字街道右侧500米',
	longitude: 112.879967,
	latitude: 36.115815,
	director: '张扬',
	telephone: '***********',
	deleted: '0',
	createDate: '2024-04-12 17:55:48',
	createBy: '管理员',
	updateDate: '2024-04-12 17:55:48',
	updateBy: '管理员',
};
