<template>
  <div class="cz_layers">
    <div class="czlc_header">企业列表</div>
    <el-input v-model="filterText" style="width: 210px" placeholder="搜索" />
    <div class="czlc_tree">
      <el-tree
        :data="layerTreeData"
        show-checkbox
        default-expand-all
        node-key="id"
        ref="tree"
        highlight-current
        :props="defaultProps"
        @check-change="handleCheckChange"
        :filter-node-method="filterNode"
      >
      </el-tree>
    </div>
  </div>
</template>

<script setup>
import { getCompanyTree } from "@/api/tree/index";
import { Company } from "@/earthCompStyle/company/company";
const COMPANY = new Company();
const filterText = ref("");
const tree = ref("");
const layerTreeData = ref([]);
const defaultProps = {
  children: "children",
  label: "label",
};
watch(filterText, (val) => {
  !tree.value.filter(val);
});

const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.includes(value);
};
const initTree = () => {
  getCompanyTree().then((res) => {
    if (res.code === 200) {
      const children = res.rows.map((item) => {
        return {
          id: item.id,
          value: item.companyName,
          label: item.companyName,
          props: item,
        };
      });
      layerTreeData.value = [
        {
          value: "拉依苏化工区",
          label: "拉依苏化工区",
          children: children,
        },
      ];
    }
  });
};
const handleCheckChange = (data, checked, indeterminate) => {
  // console.log(data, checked, indeterminate);
  // console.log(COMPANY.createFence);
  if (checked && !data.children) {
    const boundarys = data.props.boundary
      .map((point) => [point.lon, point.lat, 2])
      .flat(Infinity);
    const CompanyGroup = COMPANY.getGroup(data.id);
    // 创建围栏
    const CompanyFence = COMPANY.createFence(boundarys, "#06B6FF");
    // 添加围栏到datasource
    const entities = COMPANY.addGraphic(CompanyGroup, CompanyFence);
    window.viewer.flyTo(entities, 2000);
  } else {
    COMPANY.clearGroup(data.id);
  }
};

onMounted(() => {
  initTree();
});
</script>

<style scoped lang="scss">
.cz_layers {
  width: 230px;
  height: max-content;
  background: rgba(8, 76, 124, 0.5);
  box-sizing: border-box;
  border: 2px solid;
  border-image: linear-gradient(180deg, #3cd5ff 0%, rgba(60, 213, 255, 0) 100%) 2;
  backdrop-filter: blur(10px);
  padding: 8px;

  :deep(.el-input__wrapper) {
    background-color: #ffffff00;
    border: 1px solid rgb(90, 174, 226);
    box-shadow: 0 0 0 0 #91afce inset;
    border-radius: 0px !important;
  }
  :deep(.el-input__inner) {
    color: #ffffff;
  }
}
.czlc_header {
  background: url("@/assets/xtui/tools/dialogopen.png");
  background-size: 100% 100%;
  height: 32px;
  width: 100%;
  margin: 5px 0px 15px 0px;
  padding: 5px 30px;
  font-family: Source Han Sans;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 0px;
  color: aliceblue;
}
.czlc_tree {
  .el-tree {
    padding: 10px 0px;
    width: 100%;
    min-height: 500px;
    color: #cce3ff;
    background: rgba(26, 69, 86, 0);
    max-height: 18rem;
    overflow: auto;
    border: 0px solid rgb(53, 183, 234);
    font-size: 12px;
    font-weight: bold;
  }

  :deep(.el-tree-node__content:hover) {
    background: transparent !important;
    color: rgb(96, 167, 230);
  }

  :deep(.el-tree > .el-tree-node > .el-tree-node__content) {
    background: transparent !important;
    height: 30px;
  }

  :deep(.el-tree > .el-tree-node:focus > .el-tree-node__content) {
    background: transparent !important;
  }

  :deep(.el-tree
      > .el-tree-node
      > .el-tree-node__children
      > .el-tree-node
      > .el-tree-node__content) {
    background: transparent !important;
    height: 30px;
  }

  :deep(.el-tree
      > .el-tree-node
      > .el-tree-node__children
      > .el-tree-node
      > .el-tree-node__children
      > .el-tree-node
      > .el-tree-node__content) {
    background: transparent !important;
    padding: 5px;
    height: 30px;
    :hover {
      background: transparent !important;
      color: rgb(96, 167, 230);
    }
  }
}
</style>
