// import * as Cesium from "cesium";
export default class MapClipImage {
  constructor(viewer) {
    this.viewer = viewer;
    this.geoJsonDataSource = new Cesium.GeoJsonDataSource();
    this.generatedEntities = []; // 存储生成的区域实体，用于后续删除
    this.polygonEntity = null;
    this.lineEntity = null;
  }
  clipImage(boundary) {
    //viewer.scene.screenSpaceCameraController.minimumZoomDistance = 1000;
    this.viewer.scene.screenSpaceCameraController.maximumZoomDistance = 5600000;
    if (Cesium.FeatureDetection.supportsImageRenderingPixelated()) {
      //判断是否支持图像渲染像素化处理
      this.viewer.resolutionScale = window.devicePixelRatio;
    }
    this.viewer.scene.fxaa = false;
    this.viewer.scene.postProcessStages.fxaa.enabled = false;
    this.clip("sichuan", boundary);
  }
  // 掩膜
  clip(type, boundary) {
    // 再次添加前需清掉上次的对象
    if (this.polygonEntity) {
        this.viewer.entities.remove(this.polygonEntity);
    }
    if (this.lineEntity) {
        this.viewer.entities.remove(this.lineEntity);
    }
    let geojson = boundary;
    let arr = [];

    if (type == "sichuan") {
      geojson.features[0].geometry.coordinates[0][0].forEach((item) => {
        arr.push(item[0]);
        arr.push(item[1]);
      });
    } else {
      geojson.features[0].geometry.coordinates[3][0].forEach((item) => {
        arr.push(item[0]);
        arr.push(item[1]);
      });
    }

    // 遮罩
    this.polygonEntity = new Cesium.Entity({
      polygon: {
        hierarchy: {
          // 添加外部区域为1/4半圆，设置为180会报错
          positions: Cesium.Cartesian3.fromDegreesArray([
            0, 0, 0, 90, 179, 90, 179, 0,
          ]),
          // 中心挖空的“洞”
          holes: [
            {
              positions: Cesium.Cartesian3.fromDegreesArray(arr),
            },
          ],
        },
        material: new Cesium.Color(15 / 255.0, 15 / 255.0, 15 / 255.0, 0.8),
      },
    });

    // 边界线
    this.lineEntity = new Cesium.Entity({
      polyline: {
        positions: Cesium.Cartesian3.fromDegreesArray(arr),
        width: 2,
        material: Cesium.Color.fromCssColorString("#00ffff"),
      },
    });

    this.viewer.entities.add(this.polygonEntity);
    this.viewer.entities.add(this.lineEntity);
  }
}
