export class DrawCircle {
    constructor(viewer) {
      this.viewer = viewer;
      this.circlePrimitiveList = [];
      this.circlePrimitive = null;
      this.outlinePrimitiveList = [];
      this.outlinePrimitive = null;
    }
  
    draw(lat,lon, radius, color) {
      const center = Cesium.Cartesian3.fromDegrees(lon, lat);
      const circleGeometry = new Cesium.CircleGeometry({
        center,
        radius,
        vertexFormat: Cesium.MaterialAppearance.VERTEX_FORMAT,
      });
  
      const geometryInstance = new Cesium.GeometryInstance({
        geometry: circleGeometry,
        attributes: {
          color: Cesium.ColorGeometryInstanceAttribute.fromColor(
            color.withAlpha(0.5)
          ),
        },
      });
  
      this.circlePrimitive = new Cesium.Primitive({
        geometryInstances: geometryInstance,
        appearance: new Cesium.MaterialAppearance({
          material: Cesium.Material.fromType("Color", {
            color: color.withAlpha(0.5),
          }),
          translucent: true,
        }),
      });
  
      this.viewer.scene.primitives.add(this.circlePrimitive);
  
      this.circlePrimitiveList.push(this.circlePrimitive);
      const circleOutlineGeometry = new Cesium.CircleOutlineGeometry({
        center,
        radius,
      });
  
      const outlineInstance = new Cesium.GeometryInstance({
        geometry: circleOutlineGeometry,
        attributes: {
          color: Cesium.ColorGeometryInstanceAttribute.fromColor(color),
        },
      });
  
      this.outlinePrimitive = new Cesium.Primitive({
        geometryInstances: outlineInstance,
        appearance: new Cesium.PerInstanceColorAppearance({
          translucent: false,
          flat: true,
        }),
      });
  
      this.viewer.scene.primitives.add(this.outlinePrimitive);
      this.outlinePrimitiveList.push(this.outlinePrimitive);
    }
  
    remove() {
      if (this.circlePrimitiveList && this.circlePrimitiveList.length > 0) {
        this.circlePrimitiveList.forEach((e) => {
          this.viewer.scene.primitives.remove(e);
        });
        this.circlePrimitiveList = [];
      }
      if (this.outlinePrimitiveList && this.outlinePrimitiveList.length > 0) {
        this.outlinePrimitiveList.forEach((e) => {
          this.viewer.scene.primitives.remove(e);
        });
        this.outlinePrimitiveList = [];
      }
  
      // if (this.circlePrimitive) {
      //     this.viewer.scene.primitives.remove(this.circlePrimitive);
      //     this.circlePrimitive = null;
      // }
      // if (this.outlinePrimitive) {
      //     this.viewer.scene.primitives.remove(this.outlinePrimitive);
      //     this.outlinePrimitive = null;
      // }
      // this.viewer.scene.primitives.removeAll();
    }
  }
  