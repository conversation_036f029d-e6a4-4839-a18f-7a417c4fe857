<template>
  <div class="app-container main">
    <!-- 顶部标题 -->
    <span class="login-title">{{ header.title }}</span>
    <div class="datetime-display">
      <div class="time">{{ currentTime }}</div>
      <div class="date">
        <div class="date-text">{{ currentDate }}</div>
        <div class="week-text">{{ currentWeek }}</div>
      </div>
    </div>
    <div class="sign-out" title="退出" @click="signOut"></div>

    <!-- 替换原来的 swiper-container -->
    <div class="carousel-container" @wheel="handleWheel">
      <div class="carousel" ref="carousel">
        <div
          v-for="(item, index) in carouselItems"
          :key="index"
          class="carousel-item"
          :class="{ 'is-center': getItemStyle(index)['--is-center'] === 1 }"
          :style="getItemStyle(index)"
          @click="handleItemClick(item, index)"
        >
          <div class="card">
            <div class="flowing-light"></div>
            <div class="icon">
              <img :src="item.icon" :alt="item.title" />
            </div>
            <div class="name">{{ item.title }}</div>
          </div>
        </div>
      </div>
      <div class="carousel-btn prev" @click="rotate('prev')"></div>
      <div class="carousel-btn next" @click="rotate('next')"></div>
    </div>

    <div class="footerTItle">
      {{ header.footer }}
    </div>
  </div>
</template>

<script setup name="Main">
import { onMounted, ref, computed, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { getRouters } from "@/api/menu";
import { ElMessageBox } from "element-plus";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";
const userStore = useUserStore();
const settingsStore = useSettingsStore();
const router = useRouter();
const header = {
  title: "企业平台",
  footer: "平台入口",
};
// 添加时间相关的响应式数据
const currentTime = ref("");
const currentDate = ref("");
const currentWeek = ref("");
const carouselItems = ref([]);
// 批量导入图片资源
const images = import.meta.glob("@/assets/safetyAlerts/homebut*.png", { eager: true });

const baseBanners = Object.keys(images)
  .sort((a, b) => {
    const getNum = (str) => Number(str.match(/homebut(\d+)/)?.[1] || 0);
    return getNum(a) - getNum(b);
  })
  .map((key) => images[key].default);

const bannerIcons = baseBanners.flatMap((icon) => Array(8).fill(icon));

// 获取菜单
const getMenu = async () => {
  try {
    const res = await getRouters();
    if (!Array.isArray(res.data)) return;

    carouselItems.value = res.data.map((item, i) => ({
      id: item.id,
      title: item.meta?.title || item.name || "未命名",
      path: item.path,
      icon: bannerIcons[i % bannerIcons.length],
      ...item,
    }));
  } catch (err) {
    console.error("菜单获取失败:", err);
  }
};
const clickPath = (item) => {
  settingsStore.setHomeTitle(item.title);
  const findFirstVisiblePath = (route) => {
    if (!route?.children?.length) return route.path;
    const child = route.children.find((c) => !c.hidden);
    return child ? `${route.path}/${findFirstVisiblePath(child)}` : route.path;
  };
  router.push({ path: findFirstVisiblePath(item) });
};

const currentRotation = ref(0);
const itemAngle = computed(() => 360 / carouselItems.value.length);

const getItemStyle = (index) => {
  const angle = itemAngle.value * index + currentRotation.value;
  const radius = 300;
  const rad = (angle * Math.PI) / 180;

  const translateZ = radius * Math.cos(rad); // 控制前后深度
  const translateX = radius * 1.4 * Math.sin(rad); // 控制水平排列

  // 映射到 -180 ~ 180 范围，用于对称控制
  const centeredAngle = ((angle + 180) % 360) - 260;

  // 计算 Y 轴偏移，中间低、后面高
  const yAmplitude = 60; // 控制立体感高度
  const translateY = yAmplitude * Math.cos(rad);

  // 缩放控制：中间大，越偏越小
  const scale = 0.8 + 0.2 * Math.cos(rad); // cos(0)=1 -> scale=1.3；cos(180)=-1 -> scale=0.7

  // 透明度控制：后面更透明
  const opacity = 0.3 + 0.7 * ((translateZ + radius) / (2 * radius)); // 0.3~1

  // zIndex 中间最大，后面最小
  const zIndex = Math.round(100 + translateZ); // 近大远小排序

  return {
    transform: `translateX(${translateX}px) translateY(${translateY}px) translateZ(${translateZ}px) scale(${scale}) rotateY(${angle}deg)`,
    opacity,
    zIndex,
    filter: `blur(${1 - opacity}px)`,
    "--is-center": Math.abs(centeredAngle) < itemAngle.value / 2 ? 1 : 0,
  };
};

// 旋转控制
const rotate = (direction) => {
  const step = direction === "next" ? -itemAngle.value : itemAngle.value;
  currentRotation.value += step;
};

// 点击项目旋转到对应位置
const rotateToItem = (index) => {
  const targetRotation = -index * itemAngle.value;
  currentRotation.value = targetRotation;
};

// 更新时间的函数
const updateDateTime = () => {
  const now = new Date();

  // 格式化时间 HH:MM:SS
  currentTime.value = now.toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  });

  // 格式化日期 YYYY.MM.DD 星期几
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const weekDay = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"][
    now.getDay()
  ];
  currentDate.value = `${year}.${month}.${day}`;
  currentWeek.value = weekDay;
};

//退出
function signOut() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      userStore.logOut().then(() => {
        router.push({ path: "/login" });
      });
    })
    .catch(() => {});
}

// 添加鼠标滚轮处理函数
const handleWheel = (event) => {
  // 防抖处理，避免滚动太快
  if (wheelTimer) {
    return;
  }

  // 根据滚动方向旋转
  if (event.deltaY > 0) {
    rotate("next");
  } else {
    rotate("prev");
  }

  // 设置防抖延时
  wheelTimer = setTimeout(() => {
    wheelTimer = null;
  }, 100); // 100ms的防抖时间
};

// 添加防抖定时器
let wheelTimer = null;

// 组件卸载时清理定时器
onUnmounted(() => {
  if (wheelTimer) {
    clearTimeout(wheelTimer);
  }
});

onMounted(() => {
  // 更新时间

  updateDateTime();
  setInterval(updateDateTime, 1000);
  getMenu();
});

// 修改点击项目的处理函数
const handleItemClick = (item, index) => {
  // 先旋转到对应位置
  rotateToItem(index);

  // 添加延时，等待旋转动画完成后再跳转
  settingsStore.setHomeTitle(item.title);
  const findFirstVisiblePath = (route) => {
    if (!route?.children?.length) return route.path;
    const child = route.children.find((c) => !c.hidden);
    return child ? `${route.path}/${findFirstVisiblePath(child)}` : route.path;
  };
  router.push({ path: findFirstVisiblePath(item) });
};
</script>

<style scoped lang="scss">
.app-container {
  padding: 15px;
}
.main {
  width: 100%;
  height: 100vh;
  background-image: url("@/assets/safetyAlerts/homeIndex2.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  color: #fff;
  position: relative;
  overflow: hidden;

  .login-title {
    font-family: Alimama ShuHeiTi;
    font-size: 30px;
    font-weight: bold;
    line-height: 28px;
    letter-spacing: 0.12em;
    font-variation-settings: "opsz" auto;
    background: linear-gradient(180deg, #ffffff 0%, #9fd7ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    text-align: center;
  }
  .datetime-display {
    position: absolute;
    display: flex;
    top: -1%;
    left: 1%;
    color: #fff;
    padding: 10px;
    border-radius: 4px;

    .time {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 20px;
      transform: rotateY(30deg);
      // 删除原来的 color 属性
      background-image: linear-gradient(
        0deg,
        #f0fdff 0.4150390625%,
        #ffffff 37.158203125%,
        #90e9ff 100%
      );
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      -webkit-text-fill-color: transparent;
    }

    .date {
      .date-text {
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
        font-size: 12px;
        transform: rotateY(30deg);
        // 删除原来的 color 属性
        background-image: linear-gradient(
          0deg,
          #f0fdff 0.4150390625%,
          #ffffff 37.158203125%,
          #90e9ff 100%
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        -webkit-text-fill-color: transparent;
      }
      .week-text {
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
        font-size: 15px;
        transform: rotateY(30deg);
        // 删除原来的 color 属性
        background-image: linear-gradient(
          0deg,
          #f0fdff 0.4150390625%,
          #ffffff 37.158203125%,
          #90e9ff 100%
        );
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        -webkit-text-fill-color: transparent;
      }
    }
  }
  .sign-out {
    position: absolute;
    top: 1.5%;
    right: 2%;
    width: 25px;
    height: 25px;
    background-image: url("@/assets/safetyAlerts/avatar.png");
    background-size: 100% 100%;
    cursor: pointer;
    z-index: 9999;
  }

  .carousel-container {
    flex: 1;
    position: relative;
    perspective: 1200px; // 调整透视距离，从1500px改为1200px
    overflow: hidden;
    padding: 40px 0;
    user-select: none; // 防止拖动时选中文字

    .carousel {
      position: relative;
      width: 100%;
      height: 100%;
      transform-style: preserve-3d;
      transition: transform 0.5s ease;
    }

    .carousel-item {
      position: absolute;
      left: 50%;
      top: 50%;
      transform-origin: 50% 50% -300px;
      transition: all 0.5s ease;
      cursor: pointer;

      .card {
        position: absolute;
        width: 220px;
        height: 270px;
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        transition: all 0.3s;
        background-size: cover;
        background-repeat: no-repeat;
        transform: translateX(-50%) translateY(-50%);
        justify-content: space-around;
        &:hover {
          transform: translateX(-50%) translateY(-50%) scale(1.1);
          filter: brightness(1.2);
        }

        .flowing-light {
          position: absolute;
          width: 220px;
          height: 270px;
          background-image: url("@/assets/safetyAlerts/banner.png");
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          z-index: -1;
        }

        .icon {
          width: 180px;
          height: 145px;

          img {
            width: 180px;
            height: 145px;
            // width: 100%;
            // height: 100%;
            object-fit: contain;
          }
        }

        .name {
          font-size: 18px;
          font-weight: 700;
          color: #fff;
          margin-top: -24%;
          height: 29px;
          width: 67%;
          text-align: center;
          border: 1.15px solid;
          border-image: linear-gradient(
              270deg,
              #008cd7 -1%,
              rgba(0, 140, 215, 0) -1%,
              #01d5ff 50%,
              rgba(0, 140, 215, 0) 99%
            )
            1.15;
          //   margin-top: -35%;
        }
      }

      // 中心卡片样式
      &.is-center {
        .card {
          transform: translateX(-50%) translateY(-50%) scale(1.2);
          filter: brightness(1.2);

          .flowing-light {
            opacity: 1; // 显示光效
          }
        }
      }
    }

    .carousel-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 60px;
      height: 79px;
      cursor: pointer;
      z-index: 10;

      &.prev {
        left: 5%;
        background-image: url("@/assets/safetyAlerts/leftbut.png");
        background-size: cover;
      }

      &.next {
        right: 5%;
        background-image: url("@/assets/safetyAlerts/rightbut.png");
        background-size: cover;
      }
    }
  }

  .footer {
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    background: rgba(11, 27, 61, 0.8);

    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;

      img {
        width: 32px;
        height: 32px;
        margin-bottom: 5px;
      }

      span {
        font-size: 12px;
        color: #4accff;
      }
    }
  }
}
.footerTItle {
  width: 100%;
  position: relative;
  text-align: center;
  bottom: -5px;
  color: #ffffff;
  font-size: 36px;
  font-family: ysbthzt;
  letter-spacing: 0.04em;
}
</style>
