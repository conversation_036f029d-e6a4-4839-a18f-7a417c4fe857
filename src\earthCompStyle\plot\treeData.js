let t_data = [
	{
		id: 1,
		label: '应急符号',
		children: [
			{
				id: 11,
				label: '突发事件',
				type: 'p11',
				children: [
					{ id: 111, label: '自然灾害', type: 'p111' },
					{ id: 112, label: '事故灾难', type: 'p112' },
					{ id: 113, label: '公共卫生', type: 'p113' },
					{ id: 114, label: '社会安全', type: 'p114' },
				],
			},
			{
				id: 12,
				label: '危险源',
				type: 'p12',
				children: [{ id: 121, label: '事故灾难', type: 'p121' }],
			},
			{
				id: 13,
				label: '防护目标',
				type: 'p13',
				children: [
					{ id: 131, label: '重要部位', type: 'p131' },
					{ id: 132, label: '基础设施', type: 'p132' },
				],
			},
			{
				id: 14,
				label: '应急资源',
				type: 'p14',
				children: [
					{ id: 141, label: '应急机构', type: 'p141' },
					{ id: 142, label: '人力资源', type: 'p142' },
					{ id: 143, label: '装备物资', type: 'p143' },
					{ id: 144, label: '医疗卫生', type: 'p144' },
					{ id: 145, label: '物流运输', type: 'p145' },
					{ id: 146, label: '避难场所', type: 'p146' },
				],
			},
		],
	},
	{
		id: 2,
		label: '线状符号',
		children: [
			{
				id: 21,
				label: '隔离路线',
				type: 'L21',
			},
			{
				id: 22,
				label: '行动路线',
				type: 'L22',
			},
		],
	},
	{
		id: 3,
		label: '面状符号',
		children: [
			{
				id: 31,
				label: '多边形',
				type: 'P31',
			},
			{
				id: 32,
				label: '正方形',
				type: 'P32',
			},
			{
				id: 33,
				label: '圆形',
				type: 'P33',
			},
		],
	},
];
export default t_data;
