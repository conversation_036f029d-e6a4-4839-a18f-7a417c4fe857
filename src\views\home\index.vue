<template>
  <div
    class="carousel-container"
    @wheel.prevent="handleWheel"
    @mousedown="startDrag"
    @mousemove="onDrag"
    @mouseup="endDrag"
    @mouseleave="endDrag"
  >
    <div class="carousel-header">
      <el-image style="width: 55px; height: 27px" :src="header.icon" fit="contain" />
      <div class="headerTitle">{{ header.title }}</div>
    </div>
    <button class="nav-btn left" @click="handleAnimate('prev')" v-if="Lists.length > 1">
      ‹
    </button>

    <div class="carousel-wrapper">
      <div
        v-for="item in visibleItems"
        :key="item.id"
        class="carousel-item"
        :class="[getItemClass(item.position, item.isBlur), animationMap[item.index]]"
        :style="getItemStyle(item.position)"
        @click="clickPath(item)"
      >
        <div class="content">
          <img :src="item.icon" alt="" class="icon" />
          <div class="text">{{ item.title }}</div>
        </div>
      </div>
    </div>

    <button class="nav-btn right" @click="handleAnimate('next')" v-if="Lists.length > 1">
      ›
    </button>

    <div class="carousel-footer">{{ header.footer }}</div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { getRouters } from "@/api/menu";
import { getTenantId } from "@/utils/auth";
import headerIcon from "@/assets/safetyAlerts/homeIcon.png";
import banner from "@/assets/safetyAlerts/banner.png";
import homebut1 from "@/assets/safetyAlerts/homebut1.png";
import homebut2 from "@/assets/safetyAlerts/homebut2.png";
import useSettingsStore from "@/store/modules/settings";

const animationMap = reactive({});
const settingsStore = useSettingsStore();
const router = useRouter();
const tenantId = getTenantId();
const currentIndex = ref(0);
const Lists = ref([]);
const drag = reactive({ startX: null, deltaX: 0, isDragging: false });

const header = {
  icon: headerIcon,
  title: "企业平台",
  footer: "平台入口",
};

const baseBanners = [homebut1, homebut2].flatMap((icon) => [
  { icon, bg: banner },
  { icon, bg: banner },
]);

const clickPath = (item) => {
  settingsStore.setHomeTitle(item.title);
  const findFirstVisiblePath = (route) => {
    if (!route?.children?.length) return route.path;
    const child = route.children.find((c) => !c.hidden);
    return child ? `${route.path}/${findFirstVisiblePath(child)}` : route.path;
  };
  router.push({ path: findFirstVisiblePath(item) });
};

const visibleItems = computed(() => {
  const total = Lists.value.length;
  return total === 0
    ? []
    : Array.from({ length: total }, (_, i) => {
        const index = (currentIndex.value + i) % total;
        return {
          ...Lists.value[index],
          index,
          position: i - Math.floor(total / 2),
          isBlur: false,
        };
      });
});

function getMenu() {
  getRouters().then((res) => {
    if (!Array.isArray(res.data)) return;
    Lists.value = res.data.map((item, i) => ({
      id: item.id,
      title: item.meta?.title || item.name || "未命名",
      path: item.path,
      icon: baseBanners[i % baseBanners.length].icon,
      bg: baseBanners[i % baseBanners.length].bg,
      ...item,
    }));
  });
}

function handleAnimate(direction) {
  const total = Lists.value.length;
  if (total <= 1) return;

  const current = currentIndex.value;
  const next =
    direction === "next" ? (current + 1) % total : (current - 1 + total) % total;
  const pre = direction === "pre" ? (current - 1) % total : (current + 1 + total) % total;
  animationMap[current] = "fade-out";
  animationMap[next] = "fade-in";
  animationMap[pre] = "fade-out";
  currentIndex.value = next;

  nextTick(() => {
    setTimeout(() => {
      animationMap[current] = "";
      animationMap[next] = "";
      animationMap[pre] = "";
    }, 600);
  });
}

function handleWheel(e) {
  e.preventDefault();
  handleAnimate(e.deltaY > 0 ? "next" : "prev");
}

function startDrag(e) {
  drag.isDragging = true;
  drag.startX = e.clientX;
}

function onDrag(e) {
  if (drag.isDragging) drag.deltaX = e.clientX - drag.startX;
}

function endDrag() {
  if (!drag.isDragging) return;
  drag.isDragging = false;
  if (Math.abs(drag.deltaX) > 50) {
    handleAnimate(drag.deltaX > 0 ? "prev" : "next");
  }
  drag.startX = null;
  drag.deltaX = 0;
}

function getItemClass(position, isBlur) {
  return { blur: isBlur };
}

function getItemStyle(position) {
  const abs = Math.abs(position);
  const x = position * 320;
  const y = -abs * 15 + 50;
  const z = 200 - abs * 100;
  const rotateY = position * 5;
  const index = (currentIndex.value + position + Lists.value.length) % Lists.value.length;
  return {
    transform: `translateX(${x}px) translateY(${y}px) translateZ(${z}px) rotateY(${rotateY}deg)`,
    zIndex: 10 - abs,
    backgroundImage: `url(${Lists.value[index]?.bg || ""})`,
    transition: "transform 0.6s ease, z-index 0.6s, opacity 0.6s, filter 0.6s",
  };
}

onMounted(getMenu);
</script>

<style scoped>
.carousel-container {
  position: relative;
  width: 100%;
  height: 100vh;
  perspective: 1200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url("@/assets/safetyAlerts/homeIndex2.png");
  background-size: 100% 100%;
  overflow: hidden;
}
.carousel-header {
  width: 100%;
  position: absolute;
  text-align: center;
  top: 15px;
  display: flex;
  justify-content: center;
}
.headerTitle {
  font-family: Alimama ShuHeiTi;
  font-size: 30px;
  font-weight: bold;
  line-height: 28px;
  letter-spacing: 0.12em;
  font-variation-settings: "opsz" auto;
  background: linear-gradient(180deg, #ffffff 0%, #9fd7ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.carousel-footer {
  width: 100%;
  position: absolute;
  text-align: center;
  bottom: 10px;
  color: #ffffff;
  font-size: 36px;
  font-family: ysbthzt;
  letter-spacing: 0.04em;
}
.carousel-wrapper {
  display: flex;
  position: relative;
  transition: transform 0.5s;
  transform-style: preserve-3d;
  height: 100%;
  justify-content: center;
}

.carousel-item {
  position: absolute;
  top: 50%;
  width: 230px;
  height: 316px;
  margin-top: -158px;
  background-size: cover;
  background-position: center;
  /* transition: transform 0.6s ease, z-index 0.6s, opacity 0.6s, filter 0.6s; */
  /* transition: transform 0.5s; */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  will-change: transform;
}
.carousel-item.highlight {
  box-shadow: 0 0 30px rgba(0, 204, 255, 0.9);
  border: 2px solid rgba(0, 204, 255, 0.9);
}
.carousel-item.blur {
  filter: blur(2px);
  opacity: 0.5;
}

.carousel-item.highlight .text {
  color: #00d5ff;
  text-shadow: 0 0 10px #00d5ff;
  font-weight: bold;
}
.content {
  text-align: center;
  color: white;
}

.icon {
  width: 180px;
  height: 125px;
  margin-bottom: 10px;
}

.text {
  width: 220px;
  height: 40px;
  font-size: 22px;
  font-weight: bold;
  box-sizing: border-box;
  border: 1.15px solid;
  border-image: linear-gradient(
      270deg,
      #008cd7 -1%,
      rgba(0, 140, 215, 0) -1%,
      #01d5ff 50%,
      rgba(0, 140, 215, 0) 99%
    )
    1.15;
  background: linear-gradient(
    270deg,
    rgba(0, 140, 215, 0) 0%,
    rgba(0, 155, 255, 0.2) 48%,
    rgba(0, 155, 255, 0) 100%
  );
}

.nav-btn {
  background: transparent;
  color: #00bfff;
  border: none;
  font-size: 40px;
  cursor: pointer;
  padding: 0 20px;
  z-index: 99;
  user-select: none;
}

.nav-btn.left {
  position: absolute;
  left: 0;
}

.nav-btn.right {
  position: absolute;
  right: 0;
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateX(200px) scale(0.9) rotateY(-20deg);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1) rotateY(0);
  }
}

@keyframes slideOut {
  0% {
    opacity: 1;
    transform: translateX(0) scale(1) rotateY(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-200px) scale(0.9) rotateY(20deg);
  }
}

.carousel-item.fade-in {
  animation: slideIn 0.6s ease-out forwards;
  transform-origin: center center;
  will-change: transform, opacity;
  z-index: 2;
}

.carousel-item.fade-out {
  animation: slideOut 0.6s ease-in forwards;
  transform-origin: center center;
  will-change: transform, opacity;
  z-index: 1;
}
</style>

<style scoped>
.nav-btn.left {
  position: absolute;
  left: 0;
  width: 100px;
  height: 128px;
  background: url("@/assets/safetyAlerts/leftbut.png");
  background-size: 100% 100%;
}

.nav-btn.right {
  position: absolute;
  right: 0;
  width: 100px;
  height: 128px;
  background: url("@/assets/safetyAlerts/rightbut.png");
  background-size: 100% 100%;
}
.content,
.icon,
.text {
  user-select: none;
  -webkit-user-drag: none; /* 防止图片被拖动 */
}
</style>
