<template>
  <div class="carousel-container">
    <div class="carousel-header">
      <el-image style="width: 55px; height: 27px" :src="header.icon" fit="contain" />
      <div class="headerTitle">{{ header.title }}</div>
    </div>

    <carousel-3d
      :controls-visible="true"
      :clickable="true"
      :display="5"
      :animation-speed="500"
      :autoplay="false"
      :perspective="35"
      :space="400"
      :width="360"
      :height="422"
      @after-slide-change="onSlideChange"
    >
      <slide
        v-for="(item, index) in Lists"
        :key="item.id"
        :index="index"
        @click="clickPath(item)"
      >
        <div class="carousel-card" :style="{ backgroundImage: `url(${item.bg})` }">
          <img :src="item.icon" class="icon" />
          <div class="text">{{ item.title }}</div>
        </div>
      </slide>
    </carousel-3d>

    <div class="carousel-footer">{{ header.footer }}</div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { Carousel3d, Slide } from "vue3-carousel-3d";
import { getRouters } from "@/api/menu";
import { getTenantId } from "@/utils/auth";
import useSettingsStore from "@/store/modules/settings";

import headerIcon from "@/assets/safetyAlerts/homeIcon.png";
import banner from "@/assets/safetyAlerts/banner.png";
import homebut1 from "@/assets/safetyAlerts/homebut1.png";
import homebut2 from "@/assets/safetyAlerts/homebut2.png";

const Lists = ref([]);
const tenantId = getTenantId();
const settingsStore = useSettingsStore();
const router = useRouter();

const header = {
  icon: headerIcon,
  title: "企业平台",
  footer: "平台入口",
};

const baseBanners = [
  { icon: homebut1, bg: banner },
  { icon: homebut2, bg: banner },
  { icon: homebut1, bg: banner },
  { icon: homebut2, bg: banner },
];

function getMenu() {
  getRouters().then((res) => {
    if (!Array.isArray(res.data)) return;
    Lists.value = res.data.map((item, index) => {
      const base = baseBanners[index % baseBanners.length];
      return {
        id: item.id,
        title: item.meta?.title || item.name || "未命名",
        path: item.path,
        icon: base.icon,
        bg: base.bg,
        ...item,
      };
    });
  });
}

const clickPath = (item) => {
  settingsStore.setHomeTitle(item.title);

  const findFirstVisiblePath = (route) => {
    if (!route?.children || !route.children.length) return route.path;
    const visibleChild = route.children.find((child) => !child.hidden);
    if (!visibleChild) return route.path;
    return `${route.path}/${findFirstVisiblePath(visibleChild)}`;
  };

  const targetPath = findFirstVisiblePath(item);
  router.push({ path: targetPath });
};

function onSlideChange(index) {
  // 可选：切换时设置当前标题等
  const item = Lists.value[index];
  if (item) {
    settingsStore.setHomeTitle(item.title);
  }
}

onMounted(getMenu);
</script>

<style scoped>
:deep(.carousel-3d-container) {
  height: 624px;
}
.carousel-card {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 10px;
  overflow: hidden;
}

.icon {
  width: 180px;
  height: 125px;
  margin-bottom: 10px;
}

.text {
  width: 220px;
  height: 40px;
  font-size: 22px;
  font-weight: bold;
  text-align: center;
  color: white;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 5px;
  line-height: 40px;
}
</style>

<style scoped>
.nav-btn.left {
  position: absolute;
  left: 0;
  width: 100px;
  height: 128px;
  background: url("@/assets/safetyAlerts/leftbut.png");
  background-size: 100% 100%;
}

.nav-btn.right {
  position: absolute;
  right: 0;
  width: 100px;
  height: 128px;
  background: url("@/assets/safetyAlerts/rightbut.png");
  background-size: 100% 100%;
}
.content,
.icon,
.text {
  user-select: none;
  -webkit-user-drag: none; /* 防止图片被拖动 */
}
</style>
