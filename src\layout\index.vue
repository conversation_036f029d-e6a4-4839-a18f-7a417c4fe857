<template>
  <div :class="classObj" class="app-wrapper" :style="{ '--current-color': theme }">
    <!-- <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    /> -->
    <div class="newheader">
      <div class="newheader-left">
        <div class="newheader-main"></div>
        <div class="newheader-icon" @click="toggleSideBar"></div>
        <div class="newheader-title" @click="goHome">
          {{ settingsStore.hometitle || parentTitle }}
        </div>
      </div>
      <navbar @setLayout="setLayout" />
    </div>
    <sidebar v-if="!sidebar.hide" class="sidebar-container" />
    <div
      :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }"
      class="main-container"
    >
      <div :class="{ 'fixed-header': fixedHeader }">
        <!-- <tags-view v-if="needTagsView" /> -->
      </div>
      <app-main />
      <settings ref="settingRef" />
    </div>
  </div>
</template>

<script setup>
import { useWindowSize } from "@vueuse/core";
import Sidebar from "./components/Sidebar/index.vue";
import { AppMain, Navbar, Settings, TagsView } from "./components";
import defaultSettings from "@/settings";
import useAppStore from "@/store/modules/app";
import useSettingsStore from "@/store/modules/settings";
const router = useRouter();
const route = useRoute();
const appStore = useAppStore();
const headertitle = ref("企业端");
const settingsStore = useSettingsStore();
const theme = computed(() => settingsStore.theme);
const sideTheme = computed(() => settingsStore.sideTheme);
const sidebar = computed(() => useAppStore().sidebar);
const device = computed(() => useAppStore().device);
const needTagsView = computed(() => settingsStore.tagsView);
const fixedHeader = computed(() => settingsStore.fixedHeader);

const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === "mobile",
}));

const { width, height } = useWindowSize();
const WIDTH = 992; // refer to Bootstrap's responsive design

watchEffect(() => {
  if (device.value === "mobile" && sidebar.value.opened) {
    useAppStore().closeSideBar({ withoutAnimation: false });
  }
  if (width.value - 1 < WIDTH) {
    useAppStore().toggleDevice("mobile");
    useAppStore().closeSideBar({ withoutAnimation: true });
  } else {
    useAppStore().toggleDevice("desktop");
  }
});

function handleClickOutside() {
  useAppStore().closeSideBar({ withoutAnimation: false });
}

const settingRef = ref(null);
function setLayout() {
  settingRef.value.openSetting();
}
const goHome = () => {
  router.push({ path: "/home" });
};
function toggleSideBar() {
  appStore.toggleSideBar();
}
// 获取父路由的 title（只取第一级）
const parentTitle = computed(() => {
  const matched = route.matched;
  // 如果匹配到多个路由记录，取第一个（即父路由）
  if (matched.length > 0) {
    return matched[0].meta?.title || "默认标题";
  }
  return "默认标题";
});
</script>

<style lang="scss" scoped>
@import "@/assets/styles/mixin.scss";
@import "@/assets/styles/variables.module.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.sidebarHide .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}

.newheader {
  background: url("@/assets/commom/headerNew.png");
  background-size: 100% 100%;
  width: 100%;
  height: 88px;
  position: fixed;
  display: flex;
  justify-content: space-between;
  .newheader-left {
    display: flex;
    align-items: center;
    height: 68px;
    margin-left: 20px;
    .newheader-main {
    }
    .newheader-icon {
      width: 50px;
      height: 50px;
      background: url("@/assets/safetyAlerts/logomini.png");
      background-size: 100% 100%;
    }
    .newheader-title {
      width: 620px;
      height: 70px;
      display: flex;
      cursor: pointer;
      font-family: AlimamaShuHeiTi;
      // font-size: 26px;
      // font-weight: bold;
      font-size: 28px;
      font-weight: 500;
      line-height: 28px;
      letter-spacing: 0.12em;
      font-variation-settings: "opsz" auto;
      background: linear-gradient(180deg, #ffffff 0%, #9fd7ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
      /* text-shadow: 0px 4px 2px rgba(0, 45, 77, 0.66); */
      text-shadow: 1px 1px 1px rgb(187 182 182 / 27%);
      align-items: center;
      margin-left: 10px;
    }
  }
}
</style>
