window.xtmapConfig = {
  // xtBaseUrl: 'http://**************:8088',
  // xtBaseUrl: 'http://**********:8088',
  // xtBaseUrl: 'http://*************:8082',
  // 后台接口地址
  headerTitle: "晋南钢铁安全生产监测预警平台",
  footerName: "版权所有© 2006-2025 中科星图股份有限公司 京ICP备16063641号-1",
  //footerName: "技术支持:山西零碳数智科技股份有限公司",
  new_baseURL: "http://10.1.196.35:48080/admin-api/",
  carouselSettings: {
    perspective: 35,
    inverseScaling: 80,
    space: 400,
    width: 280,
    height: 444,
  },
  // xtBaseUrl: 'http://**************:8088',
  // xtBaseUrl: 'http://**********:8088',
  // xtBaseUrl: 'http://*************:8082',
  xtBaseUrl: "http://************:3100",
  publicPath: "/webapps/openpit/data",
  routePlanUrl: "http://**********:30018/router/RouterService",
  xtParkBaseUrl: "https://park.geovisearth.com/parkbase",
  // xtMidUrl: 'http://**************:8011/#/command/warningInfo',
  xtMidUrl: "http://**************:8355/webapps/singlepath/system/menu", // 设置为自己的前端IP地址
  billboard: {
    path: "/webapps/openpit/data/billboard/",
    h: 48,
    w: 46,
  },
  map: {
    TDU_Key: "0cc22d450bf67b7abb13e0e90ea233ae",
    XTY_Key: "5d11e4f3e6c74fc826af2739a843a2e6d0996efdffb0fb8ef58b75082cf3222d",
    /* XTY_Key:
			'd8f0b7024dbc0c43c7e7f080a8edc43043bb97df0d3b890480e400c2b69f69f7', */
  },
  plot: {
    path: "/webapps/openpit/",
  },
  weather: {
    apiKey: "18896ed542df67203d37466738ab1147",
    city: "140428",
    url: "https://restapi.amap.com/v3/weather/weatherInfo",
  },

  rmq: {
    // username: 'user',
    // pwd: 'user',
    username: "rabbit",
    pwd: "123456",
    urlA: "ws://**************:8085/ws",
    urlB: "wss://park.geovisearth.com/rabbitmq/ws",
    subA: "/exchange/tempaaa",
    subB: "/exchange/xttest",
  },
};
