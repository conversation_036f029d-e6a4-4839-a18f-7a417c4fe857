export default class PoiUtils {
	constructor() {}
	poiHanderLeft;
	/* 
	  center:中心点
	  pickName:该实体的名称，用来区分不同实体点击事件方法混乱
	  ptype:图片样式
	  labelName：实体的名称显示
	  properties：属性表
	  */
	static createPoi(options) {
	  const { center, pickName, ptype, labelName, properties } = options;
	  const entity_p = new Cesium.Entity({
		name: pickName,
		position: Cesium.Cartesian3.fromDegrees(center[0], center[1]),
		billboard: {
		  // image: type,
		  image: `${window.xtmapConfig.billboard.path}${ptype}.png`,
		  height: 45,
		  width: 45,
		},
		label: {
		  text: labelName,
		  font: "bold 13px MicroSoft YaHei",
		  outlineWidth: 2,
		  horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
		  pixelOffset: new Cesium.Cartesian2(0, -40), //偏移量
		  showBackground: true,
		  backgroundColor:
			Cesium.Color.fromCssColorString("#489DDB").withAlpha(0.8),
		  backgroundPadding: new Cesium.Cartesian2(6, 3),
		  distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 2000),
		},
		properties: properties,
	  });
  
	  return entity_p;
	}
	static addListener(viewer, callback) {
	  this.poiHanderLeft = new Cesium.ScreenSpaceEventHandler(
		viewer.scene.canvas
	  );
	  this.poiHanderLeft.setInputAction((e) => {
		const pick = viewer.scene.pick(e.position);
		if (
		  // 防止点击报错
		  Cesium.defined(pick) &&
		  Cesium.defined(pick.id) &&
		  Cesium.defined(pick.id.properties)
		) {
		  callback(viewer, pick);
		}
	  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
	}
  
	static removehanderListener() {
	  this.poiHanderLeft.removeInputAction(
		Cesium.ScreenSpaceEventType.LEFT_CLICK
	  ); //移除事件
	}
  }