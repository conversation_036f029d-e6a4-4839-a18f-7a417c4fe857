import request from '@/utils/request'

// 查询视频监控流列表
export function listVideoStream(query) {
  return request({
    url: '/contractor/videoStream',
    method: 'get',
    params: query
  })
}

// 查询视频监控流详细
export function getVideoStream(id) {
  return request({
    url: '/contractor/videoStream/' + id,
    method: 'get'
  })
}

// 新增视频监控流
export function addVideoStream(data) {
  return request({
    url: '/contractor/videoStream',
    method: 'post',
    data: data
  })
}

// 修改视频监控流
export function updateVideoStream(data) {
  return request({
    url: '/contractor/videoStream',
    method: 'post',
    data: data
  })
}

// 删除视频监控流
export function delVideoStream(data) {
  return request({
    url: '/contractor/videoStream/',
    method: 'delete',
    data: data
  })
}
