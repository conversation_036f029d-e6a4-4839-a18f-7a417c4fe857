<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="auto">
                        <el-form-item label="检测信息有效期" prop="inspectionValidDate">
                            <el-date-picker clearable
                                            v-model="queryParams.inspectionValidDate"
                                            type="datetime"
                                            value-format="YYYY-MM-DD HH:mm:ss"
                                            style="width: 100%;"
                                            placeholder="请选择检测信息有效期">
                            </el-date-picker>
                        </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                        type="primary"
                        plain
                        icon="Plus"
                        @click="handleAdd"
                        v-hasPermi="['contractor:tools:add']"
                >新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="danger"
                        plain
                        icon="Delete"
                        :disabled="multiple"
                        @click="handleDelete"
                        v-hasPermi="['contractor:tools:remove']"
                >删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="info"
                        plain
                        icon="Upload"
                        @click="handleImport"
                        v-hasPermi="['contractor:tools:import']"
                >导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="warning"
                        plain
                        icon="Download"
                        @click="handleExport"
                        v-hasPermi="['contractor:tools:export']"
                >导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="warning"
                        plain
                        icon="Download"
                        @click="importTemplate"
                >下载模板</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="toolsList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
                    <el-table-column label="承包商基本信息id" align="center" prop="contractorBasicId" />
                    <el-table-column label="施工机械" align="center" prop="constructionMachine" />
                    <el-table-column label="工器具" align="center" prop="tools" />
                    <el-table-column label="配件" align="center" prop="accessories" />
                    <el-table-column label="个人防护装备" align="center" prop="protectiveEquipment" />
                    <el-table-column label="物料" align="center" prop="materials" />
                    <el-table-column label="设备检测信息" align="center" prop="equipmentInspection" />
                    <el-table-column label="检测信息有效期" align="center" prop="inspectionValidDate" width="180">
                        <template #default="scope">
                            <span>{{ parseTime(scope.row.inspectionValidDate, '{y}-{m}-{d}') }}</span>
                        </template>
                    </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['contractor:tools:edit']">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['contractor:tools:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
                v-show="total>0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
        />

        <!-- 添加或修改承包商工器具对话框 -->
        <el-dialog :title="title" v-model="open" width="800px" append-to-body>
            <el-form ref="toolsRef" :model="form" :rules="rules" label-width="auto">
                                <el-form-item label="施工机械" prop="constructionMachine">
                                    <el-input v-model="form.constructionMachine" type="textarea" placeholder="请输入内容" />
                                </el-form-item>
                                <el-form-item label="工器具" prop="tools">
                                    <el-input v-model="form.tools" type="textarea" placeholder="请输入内容" />
                                </el-form-item>
                                <el-form-item label="配件" prop="accessories">
                                    <el-input v-model="form.accessories" type="textarea" placeholder="请输入内容" />
                                </el-form-item>
                                <el-form-item label="个人防护装备" prop="protectiveEquipment">
                                    <el-input v-model="form.protectiveEquipment" type="textarea" placeholder="请输入内容" />
                                </el-form-item>
                                <el-form-item label="物料" prop="materials">
                                    <el-input v-model="form.materials" type="textarea" placeholder="请输入内容" />
                                </el-form-item>
                                <el-form-item label="设备检测信息" prop="equipmentInspection">
                                    <el-input v-model="form.equipmentInspection" type="textarea" placeholder="请输入内容" />
                                </el-form-item>
                                <el-form-item label="检测信息有效期" prop="inspectionValidDate">
                                    <el-date-picker clearable
                                                    v-model="form.inspectionValidDate"
                                                    type="datetime"
                                                    value-format="YYYY-MM-DD HH:mm:ss"
                                                    style="width: 100%;"
                                                    placeholder="请选择检测信息有效期">
                                    </el-date-picker>
                                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 导入对话框 -->
        <el-dialog
                :title="upload.title"
                v-model="upload.open"
                width="400px"
                append-to-body
        >
            <el-upload
                    ref="uploadRef"
                    :limit="1"
                    accept=".xlsx, .xls"
                    :headers="upload.headers"
                    :action="upload.url"
                    :disabled="upload.isUploading"
                    :on-progress="handleFileUploadProgress"
                    :on-success="handleFileSuccess"
                    :auto-upload="false"
                    drag
            >
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitFileForm">确 定</el-button>
                    <el-button @click="upload.open = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Tools">
import { addTools, delTools, getTools, listTools, updateTools } from "@/api/xtcontractormag/BaseInfo/tools";
import useUserStore from "@/store/modules/user";
import { deepClone, formatDate } from "@/utils";
import { getToken } from "@/utils/auth";

    const userStore = useUserStore();

    const { proxy } = getCurrentInstance();

    const toolsList = ref([]);
    const open = ref(false);
    const loading = ref(true);
    const showSearch = ref(true);
    const ids = ref([]);
    const itemsArr = ref([]);
    const multiple = ref(true);
    const total = ref(0);
    const title = ref("");

    /*** 用户导入参数 */
    const upload = reactive({
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: window.xtmapConfig.xtBaseUrl + "/contractor/tools/importData",
    });

    const data = reactive({
        form: {},
        queryParams: {
            pageNum: 1,
            pageSize: 10,
                        contractorBasicId: null,
                        constructionMachine: null,
                        tools: null,
                        accessories: null,
                        protectiveEquipment: null,
                        materials: null,
                        equipmentInspection: null,
                        inspectionValidDate: null,
        },
        rules: {
                        constructionMachine: [
                        { required: true, message: "施工机械不能为空", trigger: "blur" }
                    ],
                        tools: [
                        { required: true, message: "工器具不能为空", trigger: "blur" }
                    ],
                        accessories: [
                        { required: true, message: "配件不能为空", trigger: "blur" }
                    ],
                        protectiveEquipment: [
                        { required: true, message: "个人防护装备不能为空", trigger: "blur" }
                    ],
                        materials: [
                        { required: true, message: "物料不能为空", trigger: "blur" }
                    ],
                        equipmentInspection: [
                        { required: true, message: "设备检测信息不能为空", trigger: "blur" }
                    ],
                        inspectionValidDate: [
                        { required: true, message: "检测信息有效期不能为空", trigger: "blur" }
                    ],
                        deleted: [
                        { required: true, message: "删除标记不能为空", trigger: "blur" }
                    ]
        }
    });

    const { queryParams, form, rules } = toRefs(data);

    /** 查询承包商工器具列表 */
    function getList() {
        loading.value = true;
        listTools(queryParams.value).then(response => {
                toolsList.value = response.rows;
            total.value = response.total;
            loading.value = false;
        });
    }

    // 取消按钮
    function cancel() {
        open.value = false;
        reset();
    }

    // 表单重置
    function reset() {
        form.value = {
                        id: null,
                        tenantId: null,
                        createBy: null,
                        createTime: null,
                        updateBy: null,
                        updateTime: null,
                        contractorBasicId: null,
                        constructionMachine: null,
                        tools: null,
                        accessories: null,
                        protectiveEquipment: null,
                        materials: null,
                        equipmentInspection: null,
                        inspectionValidDate: null,
                        deleted: null
        };
        proxy.resetForm("toolsRef");
    }

    /** 搜索按钮操作 */
    function handleQuery() {
        queryParams.value.pageNum = 1;
        getList();
    }

    /** 重置按钮操作 */
    function resetQuery() {
        proxy.resetForm("queryRef");
        handleQuery();
    }

    // 多选框选中数据
    function handleSelectionChange(selection) {
        ids.value = selection.map(item => item.id);
        itemsArr.value = deepClone(selection);
        multiple.value = !selection.length;
    }

    /** 导入按钮操作 */
    function handleImport() {
        upload.title = "导入";
        upload.open = true;
    }

    /** 下载模板操作 */
    function importTemplate() {
        proxy.download(
                "contractor/tools/export/template",
                {},
                `tools_${new Date().getTime()}.xlsx`
        );
    }

    /**文件上传中处理 */
    const handleFileUploadProgress = (event, file, fileList) => {
        upload.isUploading = true;
    };

    /** 文件上传成功处理 */
    const handleFileSuccess = (response, file, fileList) => {
        upload.open = false;
        upload.isUploading = false;
        proxy.$refs["uploadRef"].handleRemove(file);
        proxy.$alert(
                "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
                response.msg +
                "</div>",
                "导入结果",
                { dangerouslyUseHTMLString: true }
        );
        getList();
    };

    /** 提交上传文件 */
    function submitFileForm() {
        upload.url = `${window.xtmapConfig.xtBaseUrl}/contractor/tools/importData?userId=${userStore.id}`;
        proxy.$refs["uploadRef"].submit();
    }

    /** 新增按钮操作 */
    function handleAdd() {
        reset();
        open.value = true;
        title.value = "添加承包商工器具";
    }

    /** 修改按钮操作 */
    function handleUpdate(row) {
        reset();
        const _id = row.id || ids.value
        getTools(_id).then(response => {
            form.value = response.data;
            open.value = true;
            title.value = "修改承包商工器具";
        });
    }

    /** 提交按钮 */
    function submitForm() {
        proxy.$refs["toolsRef"].validate(valid => {
            if (valid) {
                if (form.value.id != null) {
                    const data = {
                        ...form.value,
                        tenantId: userStore.tenantId,
                        updateBy: userStore.name,
                        updateTime: formatDate(new Date()),
                    };
                    updateTools(data).then(response => {
                        proxy.$modal.msgSuccess("修改成功");
                        open.value = false;
                        getList();
                    });
                } else {
                    const data = {
                        ...form.value,
                        tenantId: userStore.tenantId,
                        createBy: userStore.name,
                        createTime: formatDate(new Date()),
                        updateBy: userStore.name,
                        updateTime: formatDate(new Date()),
                        deleted: '0'
                    };
                    addTools(data).then(response => {
                        proxy.$modal.msgSuccess("新增成功");
                        open.value = false;
                        getList();
                    });
                }
            }
        });
    }

    /** 删除按钮操作 */
    function handleDelete(row) {
        const _ids = row.id ? [row.id] : ids.value;
        proxy.$modal.confirm('是否确认删除承包商工器具编号为"' + _ids + '"的数据项？').then(function() {
            return delTools(_ids);
        }).then(() => {
            getList();
            proxy.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    }

    /** 导出按钮操作 */
    function handleExport() {
        const params = itemsArr.value.length > 0 ? itemsArr.value : undefined;
        proxy.codeDownload(
                'contractor/tools/export',
                params,
                `tools_${new Date().getTime()}.xlsx`
        );
    }

    getList();
</script>
