<template>
  <div class="car-posi">
    <el-input
      v-model="filterText"
      style="width: 240px"
      placeholder="请输入车牌号"
      class="tree-input"
    />

    <el-tree
      class="tree-list"
      ref="treeRef"
      style="max-width: 600px"
      :data="carTreeData"
      :props="defaultProps"
      @node-click="handleNodeClick"
      @check-change="handleCheckChange"
      show-checkbox
      node-key="id"
      default-expand-all
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
    >
      <template #default="{ node, data }">
        <span class="custom-tree-node">
          <span>{{ node.label }}</span>

          <span class="custom-tree-node-span" v-if="!data.children">
            <a style="color: greenyellow" v-if="data.login"> 在线 </a>
            <a style="color: red" v-else> 离线 </a>
          </span>
        </span>
      </template>
    </el-tree>

    <!-- <div class="car-info">
      <div>
        <span>姓名：{{ currentCarInfo.driver }}</span>
      </div>
      <div>
        <span>类型：{{ currentCarInfo.type }}</span>
      </div>
    </div> -->
  </div>
</template>

<script setup>
import { el } from "element-plus/es/locales.mjs";
import { status } from "nprogress";
import { nextTick, ref, reactive, computed, onMounted } from "vue";
import { getCarGPS, getCarTree } from "@/api/xtsatellite/index";
import { ElLoading, ElMessage } from "element-plus";
const { proxy } = getCurrentInstance();
const filterText = ref("");
const treeRef = ref();

watch(filterText, (val) => {
  treeRef.value.filter(val);
});

const filterNode = (value, data) => {
  if (!value) return true;
  return data.number_plate.includes(value);
};

const currentCarInfo = reactive({ driver: "", type: "" });
const carTreeData = ref([]);
const defaultProps = {
  children: "children",
  label: "number_plate",
};
getCarTree().then((res) => {
  // console.log(res, "cccccccccc");
  carTreeData.value = res.records;
  for (const i of carTreeData.value) {
    i.lon = parseFloat(i.lon);
    i.lat = parseFloat(i.lat);
    i.coor = { lon: i.lon, lat: i.lat };
    i.lineArr = [111.11111, 33.33333, 111.11111, 33.33333];
    // i.lineArr = [i.lon, i.lat, i.lon, i.lat];
    i.timer = null;
    i.login = i.login === "true" ? true : false;
    // i.disabled = !i.login;
  }
});
/* const carTreeData = ref([
  {
    id: 1,
    label: "车辆列表",
    children: [
      {
        id: 11,
        label: "晋A·UT9986",
        status: 0,
        disabled: 1,
        driver: "张华",
        type: "应急车",
        coor: { lon: 112.886121, lat: 36.099099 },
        lineArr: [111.11111, 36.099099, 111.11111, 36.099099],
        timer: null,
      },
      {
        id: 12,
        label: "晋A·CH5784",
        status: 1,
        disabled: false,
        driver: "李平",
        type: "巡检车",
        coor: { lon: 112.826121, lat: 36.099099 },
        lineArr: [111.11111, 36.099099, 111.11111, 36.099099],
        timer: null,
      },
      {
        id: 13,
        label: "晋A·PW8621",
        status: 1,
        disabled: false,
        driver: "王明",

        type: "巡检车",
        coor: { lon: 112.886121, lat: 36.099099 },
        lineArr: [111.11111, 36.099099, 111.11111, 36.099099],
        timer: null,
      },
    ],
  },
]); */

const append = (data) => {
  /* const newChild = { id: id++, label: "testtest", children: [] };
  if (!data.children) {
    data.children = [];
  }
  data.children.push(newChild);
  dataSource.value = [...dataSource.value]; */
};

const remove = (node, data) => {
  const parent = node.parent;
  const children = parent.data.children || parent.data;
  const index = children.findIndex((d) => d.id === data.id);
  children.splice(index, 1);
  dataSource.value = [...dataSource.value];
};

const handleNodeClick = (data, node, event) => {
  // console.log(data, node, event);
  // currentCarInfo.driver = data.driver;
  // currentCarInfo.type = data.type;
};

const handleCheckChange = (data, checked, indeterminate) => {
  // console.log("Node:", data);
  // console.log("Checked:", checked);
  // console.log("Indeterminate:", indeterminate);
  if (checked) {
    drawEntity(data);
    setTimeout(() => {
      moveEntity(data);
      data.timer = setInterval(() => {
        moveEntity(data);
        // }, 20 * 1000);
      }, 2 * 60 * 1000);
    }, 500);
  } else {
    deleteEntity(data);
  }
};
// ===============================================================

let carCustomDS = null;

function czInitFn() {
  carCustomDS = new Cesium.CustomDataSource("cds-data");
  window.viewer.dataSources.add(carCustomDS);
  // carCustomDS.entities.add(tempPoint);
  // carCustomDS.entities.removeAll();
}

function drawEntity(item) {
  const e = addIconAndLine(item);
  carCustomDS.entities.add(e);
  // item.lineArr = [];
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(item.lon, item.lat, 1500),
  });
}
function moveEntity(item) {
  let tempArr = [];
  // item.timer = setInterval(() => {
  // console.log("timer start");
  const loading = ElLoading.service({
    lock: true,
    text: "获取GPS信息中...请稍等",
    background: "rgba(0, 0, 0, 0.7)",
  });

  /* console.log(
    proxy.dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
    proxy
      .dayjs(new Date().getTime() - 24 * 60 * 60 * 1000)
      .format("YYYY-MM-DD HH:mm:ss"),
    "HH:mm:ss"
  ); */

  const p = {
    gpsInfoId: item.id,
    start: proxy
      .dayjs(new Date().getTime() - 24 * 60 * 60 * 1000)
      .format("YYYY-MM-DD HH:mm:ss"),
    end: proxy.dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
  };
  getCarGPS(p).then((res) => {
    // console.log(res);
    loading.close();
    if (res.records.length === 0) {
      // alert("暂无数据");
      ElMessage({
        message: `暂无GPS位置轨迹信息`,
        type: "warning",
        plain: true,
      });
      return;
    }
    /* for (const i of res.records) {
      tempArr.push(parseFloat(i.lon), parseFloat(i.lat));
    } */
    for (let i = 0; i < res.records.length; i++) {
      if (i % 2 === 0) {
        tempArr.push(parseFloat(res.records[i].lon), parseFloat(res.records[i].lat));
      }
    }
    // console.log(tempArr, "tempArrtempArr");

    carCustomDS.entities.getById(item.id).position._value = Cesium.Cartesian3.fromDegrees(
      tempArr[0],
      tempArr[1]
    );

    /* for (const i of tempArr) {
      item.lineArr.push(i);
    } */

    if (item.lineArr[0] === 111.11111) {
      //如果是第一次请求
      const t = item.lineArr.concat(tempArr);
      // for (const i of tempArr) {
      //   item.lineArr.push(i);
      // }
      item.lineArr = [...t.slice(4)];
      // console.log(item.lineArr, "tempArrtempArr22222222222");
    } else {
      //如果不是第一次请求
      item.lineArr = tempArr;
    }
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(tempArr[0], tempArr[1], 1700),
    });
  });
}

function deleteEntity(item) {
  carCustomDS.entities.removeById(item.id);
  // item.lineArr = [];
  clearInterval(item.timer);
  item.timer = null;
  // console.log("timer clear");
}

/* const line_update = function () {
  return Cesium.Cartesian3.fromDegreesArray(item.lineArr);
}; */
function addIconAndLine(item) {
  const line_update = function () {
    return Cesium.Cartesian3.fromDegreesArray(item.lineArr);
  };
  const e = new Cesium.Entity({
    id: item.id,
    position: Cesium.Cartesian3.fromDegrees(item.lon, item.lat, 1),
    billboard: {
      image: `${window.xtmapConfig.publicPath}/billboard/cargps.png`, // default: undefined
      show: true, // default
      scale: 0.4, // default: 1.0
      width: 128,
      height: 128,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
    },
    label: {
      text: item.number_plate,
      font: "12pt sans-serif",
      // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.BASELINE,
      fillColor: Cesium.Color.BLACK,
      showBackground: true,
      backgroundColor: new Cesium.Color(1, 1, 1, 0.7),
      backgroundPadding: new Cesium.Cartesian2(8, 4),
      pixelOffset: new Cesium.Cartesian2(0, -48),
      // scaleByDistance: new Cesium.NearFarScalar(150000, 0.8, 250000, 0),
    },
    polyline: {
      show: true,
      // 定义线条的 Cartesian3 位置的数组
      positions: new Cesium.CallbackProperty(line_update, false),
      // positions: Cesium.Cartesian3.fromDegreesArray(peopleLineArr),
      width: 5,
      material: Cesium.Color.AQUA,
      clampToGround: true, // 是否贴地
      // shadows: Cesium.ShadowMode.DISABLED, // 折线是投射还是接收光源的阴影
      // classificationType: Cesium.ClassificationType.BOTH,
      // 指定用于订购地面几何形状的z索引。仅在多边形为常数且未指定高度或拉伸高度的情况下才有效  type:ConstantProperty
      // zIndex: 0,
    },
  });
  return e;
}

onMounted(() => {
  emitter.on("viewerLoad", () => {});
  czInitFn();
});
</script>

<style scoped lang="scss">
.car-posi {
  padding: 0 10px 10px 10px;
  width: 250px;
  // height: 280px;
  // background-color: rgba(100, 148, 237, 0.534);

  .tree-input {
    margin-bottom: 10px;
    :deep(.el-input__wrapper) {
      background-color: transparent;
      border: 1px solid rgb(90, 174, 226);
      box-shadow: none;
      border-radius: 0;
    }
  }
  .tree-list {
    height: 300px;
    overflow: scroll;
  }
  :deep(.el-tree) {
    background-color: rgba(0, 51, 255, 0);

    padding: 13px;

    overflow: auto;
    :deep(.el-tree-node__label) {
      font-size: 16px;
      color: rgb(255, 255, 255);
    }
  }

  :deep(.el-tree-node__content) {
    background-color: rgba(137, 43, 226, 0);
  }
  :deep(.el-tree-node.is-current > .el-tree-node__content) {
    /* 当前选中后的颜色 */
    background-color: rgba(48, 138, 234, 0.356);
  }
  :deep(.el-tree-node .el-tree-node__content .el-tree-node__label) {
    color: rgb(255, 255, 255);
  }
  :deep(.el-tree-node .el-tree-node__content:hover) {
    /* 鼠标浮动的颜色 */
    background-color: rgba(37, 107, 183, 0.2);
    /* background-color: rgba(37, 107, 183, 0.469); */
  }
  :deep(.el-tree-node.is-current.is-focusable:hover) {
    /* 鼠标浮动的颜色 */
    background-color: rgba(135, 183, 234, 0);
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // font-size: 16px;
    padding-right: 8px;
    color: white;
    .custom-tree-node-span {
      // display: flex;
      // align-items: center;
      color: white;
    }
  }

  .car-info {
    display: flex;
    flex-direction: column;
  }
}
</style>
