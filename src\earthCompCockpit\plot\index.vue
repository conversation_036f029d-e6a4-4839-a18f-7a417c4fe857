<template>
  <div class="draw-icon">
    <div class="draw-tree-menu">
      <!--  	@check-change="checkTreeNode"-->
      <!-- default-expand-all -->
      <el-tree
        :data="treeData"
        accordion
        ref="resTree"
        highlight-current
        :props="defaultProps"
        @node-click="openInfo"
      >
      </el-tree>
    </div>
    <div class="draw-tree-content">
      <ul>
        <li v-for="item in imgList" @click="clickDraw(item)">
          <img :src="`${picPath}data/image/emergIcon/${iconType}/${item.imgName}.png`" />
        </li>
      </ul>
      <!-- <div>
        <el-button size="small" @click="getData">获取</el-button>
        <el-button size="small" @click="clearData">清空</el-button>
        <el-button size="small" @click="setData">复现</el-button>
      </div> -->
    </div>
  </div>
</template>

<script setup>
import axios from "axios";
import { onMounted, reactive, toRaw } from "vue";

import { PlotBillBoard, PlotLine, PlotPolygon, PlotRectangle, PlotCircle } from "./pt.js";

import t_data from "./treeData";
const defaultProps = reactive({
  children: "children",
  label: "label",
});

const treeData = ref([]);
const imgList = ref([]);
const iconType = ref("");
const picPath = window.xtmapConfig.plot.path;
treeData.value = t_data;

function openInfo(node) {
  if (!node.children) {
    // console.log(node.type, '----------ytyep');
    axios(`${window.xtmapConfig.plot.path}data/geojson/drawIcon/${node.type}.json`).then(
      (res) => {
        iconType.value = node.type || res.data.type; //icon type 就是节点的type
        imgList.value = res.data.content; // 选择的时候 图片只有type和name两个属性就够了，
        // 绘制之后，动态调整大小之后，在bill.js中去设置要存储的各个具体属性就行
      }
    );
  }
}

// ---------------------------plot------------------------------
const plotEntityCollection = ref([]);
const plotCollectionArr = ref([]);
const plotCollectionObj = ref({});

const plotBillBoard = ref();
const plotLine = ref();
const plotPolygon = ref();
const plotRectangle = ref();
const plotCircle = ref();

function initPlotObj(viewer) {
  // 必须要初始化 如果只是点击图标的时候才创建，另一个界面如果没点过，就没法复现
  plotBillBoard.value = new PlotBillBoard(viewer);
  plotLine.value = new PlotLine(viewer);
  plotPolygon.value = new PlotPolygon(viewer);
  plotRectangle.value = new PlotRectangle(viewer);
  plotCircle.value = new PlotCircle(viewer);
  plotCollectionArr.value = [
    plotBillBoard.value,
    plotLine.value,
    plotPolygon.value,
    plotRectangle.value,
    plotCircle.value,
  ];
  plotCollectionObj.value = {
    plotBillBoard: plotBillBoard.value,
    plotLine: plotLine.value,
    plotPolygon: plotPolygon.value,
    plotRectangle: plotRectangle.value,
    plotCircle: plotCircle.value,
  };
}

function clickDraw(i) {
  plotCollectionObj.value[i.czType]
    .draw({ picType: i.imgName, czType: i.czType, busiType: iconType.value })
    .then((res) => {
      plotEntityCollection.value.push(res);
    });
}

function clearData() {
  plotCollectionArr.value.forEach((item) => {
    item.clearData();
  });
}

function getData() {
  return toRaw(plotEntityCollection.value);
}
function setData() {
  for (const i of plotEntityCollection.value) {
    plotCollectionObj.value[i.czType].setPlotData(i);
  }
}
window.plotPlugin = {
  clear: clearData,
  get: getData,
  set: setData,
};

onMounted(() => {
  initPlotObj(window.viewer);
});
</script>

<style lang="scss" scoped>
.draw-icon {
  background-color: rgba(214, 115, 54, 0);
  width: 245px;
  .draw-tree-menu {
    padding: 10px;

    :deep(.el-tree) {
      background-color: rgba(0, 51, 255, 0.1);

      padding: 13px;
      height: 260px;
      overflow: auto;
    }
    :deep(.el-tree-node__label) {
      font-size: 16px;
    }
    :deep(.el-tree-node__content) {
      background-color: rgba(137, 43, 226, 0);
    }
    :deep(.el-tree-node.is-current > .el-tree-node__content) {
      /* 当前选中后的颜色 */
      background-color: rgba(48, 138, 234, 0.356);
    }
    :deep(.el-tree-node .el-tree-node__content .el-tree-node__label) {
      color: rgb(255, 255, 255);
    }
    :deep(.el-tree-node .el-tree-node__content:hover) {
      /* 鼠标浮动的颜色 */
      background-color: rgba(37, 107, 183, 0.2);
      /* background-color: rgba(37, 107, 183, 0.469); */
    }
    :deep(.el-tree-node.is-current.is-focusable:hover) {
      /* 鼠标浮动的颜色 */
      background-color: rgba(135, 183, 234, 0);
    }
  }
  /* -----------------------content---------------------- */
  .draw-tree-content {
    height: 180px;
    /* background-color: #f06c0074; */
    padding: 10px;

    ul {
      margin: 0;
      padding: 6px;
      border: 1px solid rgba(81, 145, 218, 0.817);
      border-radius: 4px;
      height: 175px;
      overflow: auto;
      display: grid;

      grid-template-columns: repeat(3, 1fr);
      grid-auto-flow: row;
      grid-auto-rows: 70px;
      grid-row-gap: 0px;
      // grid-row-gap: 10px;

      justify-items: center;
      align-items: center;

      li {
        width: 64px;
        height: 64px;
        list-style: none;
        border: solid 1px rgba(18, 159, 230, 0.54);

        img {
          width: 64px;
          height: 64px;
        }
      }
    }
  }

  ::-webkit-scrollbar {
    width: 3px;
  }
  ::-webkit-scrollbar-track {
    background-color: #9cbef2af;
  }
  ::-webkit-scrollbar-thumb {
    background-color: #5ab3d992;
  }
  ::-webkit-scrollbar-thumb:hover {
    background-color: #468af075;
  }
  ::-webkit-scrollbar-thumb:active {
    background-color: #f06c00;
  }
}
</style>
