<template>
  <div class="pipline-tree">
    <!-- <el-input
      v-model="filterText"
      style="width: 240px"
      placeholder="管网数据"
      class="tree-input"
    /> -->

    <el-tree
      class="tree-list"
      ref="treeRef"
      style="max-width: 400px"
      :data="piplineTreeData"
      :props="defaultProps"
      @node-click="handleNodeClick"
      @check-change="handleCheckChange"
      show-checkbox
      node-key="id"
      default-expand-all
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
    >
      <template #default="{ node, data }">
        <span class="custom-tree-node">
          <span>{{ node.label }}</span>
          <div
            class="custom-tree-color"
            :style="{ backgroundColor: data.color }"
            v-if="!data.children"
          ></div>
        </span>
      </template>
    </el-tree>

    <!-- <div class="car-info">
      <div>
        <span>姓名：{{ currentCarInfo.driver }}</span>
      </div>
      <div>
        <span>类型：{{ currentCarInfo.type }}</span>
      </div>
    </div> -->
  </div>
</template>

<script setup>
import { nextTick, ref, reactive, computed, onMounted } from "vue";
import { getCarGPS, getCarTree } from "@/api/xtsatellite/index";
import { ElLoading, ElMessage } from "element-plus";
const { proxy } = getCurrentInstance();
const filterText = ref("");
const treeRef = ref();

watch(filterText, (val) => {
  treeRef.value.filter(val);
});

const filterNode = (value, data) => {
  if (!value) return true;
  return data.number_plate.includes(value);
};

const defaultProps = {
  children: "children",
  label: "label",
};

const piplineTreeData = ref([
  {
    id: 1,
    label: "管网列表",
    name: "all",
    color: "red",
    e: null,
    children: [
      {
        id: 11,
        label: "燃气管网",
        name: "gaspip",
        color: "rgb(223,199,65)",
        e: null,
      },
      {
        id: 12,
        label: "供水管网",
        name: "waterpip",
        color: "rgb(47,216,131)",
      },
      {
        id: 13,
        label: "供热管网",
        name: "heatpip",
        color: "rgb(234,76,31)",
      },
      {
        id: 14,
        label: "电网",
        name: "elecpip",
        color: "rgb(55,169,231)",
      },
    ],
  },
]);

const handleNodeClick = (data, node, event) => {
  // console.log(data, node, event);
  // currentCarInfo.driver = data.driver;
  // currentCarInfo.type = data.type;
};

const handleCheckChange = (data, checked, indeterminate) => {
  // console.log("Node:", data);
  // console.log("Checked:", checked);
  // console.log("Indeterminate:", indeterminate);
  if (checked) {
    addPipline(data);
  } else {
    removePipline(data);
  }
};
// ===============================================================

let piplineCustomDS = null;

function czInitFn() {
  piplineCustomDS = new Cesium.CustomDataSource("pipeline-data");
  window.viewer.dataSources.add(piplineCustomDS);
  // piplineCustomDS.entities.add(tempPoint);
  // piplineCustomDS.entities.removeAll();
}

function addPipline(i) {
  console.log(i, "iii");

  if (!i.e) {
    i.e = Cesium.GeoJsonDataSource.load(
      `${window.xtmapConfig.publicPath}/geojson/pips/${i.name}.geojson`,
      {
        stroke: Cesium.Color.fromCssColorString(i.color).withAlpha(0.83), // 边框颜色
        // fill: Cesium.Color.RED.withAlpha(0.2), // 填充颜色
        strokeWidth: 4, // 边框宽度
        // zIndex: 1,
      }
    );

    i.e.then((dataSource) => {
      viewer.dataSources.add(i.e);
      dataSource.name = i.name;
      console.log(dataSource.entities, "333");
      dataSource.entities.values.forEach((entity) => {
        if (entity) {
          // console.log(entity, "555");
          entity.polygon.zIndex = 5;
        }
      });
      // viewer.flyTo(dataSource);
      // viewer.zoomTo(dataSource);
    });
    viewer.flyTo(i.e);
  }
}
function removePipline(i) {
  viewer.dataSources.remove(viewer.dataSources.getByName(i.name)[0]);
  i.e = null;
}

onMounted(() => {
  emitter.on("viewerLoad", () => {});
  czInitFn();
});
</script>

<style scoped lang="scss">
.pipline-tree {
  padding: 0 10px 10px 10px;
  width: 200px;
  // height: 280px;
  // background-color: rgba(100, 148, 237, 0.534);

  .tree-input {
    margin-bottom: 10px;
    :deep(.el-input__wrapper) {
      background-color: transparent;
      border: 1px solid rgb(90, 174, 226);
      box-shadow: none;
      border-radius: 0;
    }
  }
  .tree-list {
    height: 230px;
    overflow: scroll;
  }
  :deep(.el-tree) {
    background-color: rgba(0, 51, 255, 0);

    padding: 13px;

    overflow: auto;
    :deep(.el-tree-node__label) {
      font-size: 16px;
      color: rgb(255, 255, 255);
    }
  }

  :deep(.el-tree-node__content) {
    background-color: rgba(137, 43, 226, 0);
  }
  :deep(.el-tree-node.is-current > .el-tree-node__content) {
    /* 当前选中后的颜色 */
    background-color: rgba(48, 138, 234, 0.356);
  }
  :deep(.el-tree-node .el-tree-node__content .el-tree-node__label) {
    color: rgb(255, 255, 255);
  }
  :deep(.el-tree-node .el-tree-node__content:hover) {
    /* 鼠标浮动的颜色 */
    background-color: rgba(37, 107, 183, 0.2);
    /* background-color: rgba(37, 107, 183, 0.469); */
  }
  :deep(.el-tree-node.is-current.is-focusable:hover) {
    /* 鼠标浮动的颜色 */
    background-color: rgba(135, 183, 234, 0);
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // font-size: 16px;
    padding-right: 8px;
    color: white;
    .custom-tree-node-span {
      // display: flex;
      // align-items: center;
      color: white;
    }
    .custom-tree-color {
      width: 10px;
      height: 10px;
      border-radius: 80%;
      background-color: white;
    }
  }

  .car-info {
    display: flex;
    flex-direction: column;
  }
}
</style>
