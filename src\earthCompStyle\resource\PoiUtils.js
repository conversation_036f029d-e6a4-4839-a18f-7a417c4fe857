export default class PoiUtils {
  constructor() {}
  poiHanderLeft;
  static createPoi(i, type,label) {
    const entity_p = new Cesium.Entity({
      name: "reTree",
      position: Cesium.Cartesian3.fromDegrees(i.longitude, i.latitude),
      billboard: {
        image: `${window.xtmapConfig.billboard.path}${type}.png`,
        height: window.xtmapConfig.billboard.h,
        width: window.xtmapConfig.billboard.w,
      },
      label: {
        text: label,
        font: "bold 14px MicroSoft YaHei",
        outlineWidth: 2,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        pixelOffset: new Cesium.Cartesian2(0, -40), //偏移量
        showBackground: true,
        backgroundColor:
          Cesium.Color.fromCssColorString("#489DDB").withAlpha(0.8),
        backgroundPadding: new Cesium.Cartesian2(6, 3),
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 9000),
      },
      properties: {
        type: type,
        ...i,
      },
    });
    return entity_p;
  }

  static addListener(viewer, callback) {
    this.poiHanderLeft = new Cesium.ScreenSpaceEventHandler(
      viewer.scene.canvas
    );
    this.poiHanderLeft.setInputAction((e) => {
      const pick = viewer.scene.pick(e.position);
      if (
        // 防止点击报错
        Cesium.defined(pick) &&
        Cesium.defined(pick.id) &&
        Cesium.defined(pick.id.properties)
      ) {
        callback(viewer, pick);
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }
  static removehanderListener() {
    this.poiHanderLeft.removeInputAction(
      Cesium.ScreenSpaceEventType.LEFT_CLICK
    ); //移除事件
  }
}
