import request from '@/utils/request'

// 查询监测大类
export function getGroup(query) {
  return request({
    url: '/mine/group',
    method: 'get',
    params: query
  })
}



export function addGroup(data) {
  return request({
    url: '/mine/group',
    method: 'post',
    data: data
  })
}


export function updateGroup(data) {
  return request({
    url: '/mine/group',
    method: 'post',
    data: data
  })
}


export function delGroup(ids) {
  return request({
    url: '/mine/group',
    method: 'delete',
    data: ids
  })
}
