<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="auto"
    >
      <el-form-item label="所属模型ID" prop="modelId">
        <el-input
          v-model="queryParams.modelId"
          placeholder="请输入所属模型ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="指标名称" prop="indicatorName">
        <el-input
          v-model="queryParams.indicatorName"
          placeholder="请输入指标名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['contractor:evaluationModel:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['contractor:evaluationModel:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['contractor:evaluationModel:import']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['contractor:evaluationModel:export']"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="importTemplate"
          >下载模板</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="evaluationModelList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="所属模型ID" align="center" prop="modelId" />
      <el-table-column label="指标名称" align="center" prop="indicatorName" />
      <el-table-column label="指标类型" align="center" prop="indicatorType" />
      <el-table-column label="权重" align="center" prop="weight" />
      <el-table-column label="最大得分" align="center" prop="scoreMax" />
      <el-table-column label="单位" align="center" prop="unit" />
      <el-table-column label="说明" align="center" prop="description" />
      <el-table-column label="排序号" align="center" prop="sortOrder" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['contractor:evaluationModel:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['contractor:evaluationModel:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改评价模型指标配置对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="evaluationModelRef" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="所属模型ID" prop="modelId">
          <el-input v-model="form.modelId" placeholder="请输入所属模型ID" />
        </el-form-item>
        <el-form-item label="指标名称" prop="indicatorName">
          <el-input v-model="form.indicatorName" placeholder="请输入指标名称" />
        </el-form-item>
        <el-form-item label="权重" prop="weight">
          <el-input v-model="form.weight" placeholder="请输入权重" />
        </el-form-item>
        <el-form-item label="最大得分" prop="scoreMax">
          <el-input v-model="form.scoreMax" placeholder="请输入最大得分" />
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-input v-model="form.unit" placeholder="请输入单位" />
        </el-form-item>
        <el-form-item label="说明" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="排序号" prop="sortOrder">
          <el-input v-model="form.sortOrder" placeholder="请输入排序号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="EvaluationModel">
import {
  listEvaluationModel,
  getEvaluationModel,
  delEvaluationModel,
  addEvaluationModel,
  updateEvaluationModel,
} from "@/api/xtcontractormag/EvaluationManage/evaluationModel";
import { deepClone, formatDate } from "@/utils";
import { getToken } from "@/utils/auth";
import useUserStore from "@/store/modules/user";

const userStore = useUserStore();

const { proxy } = getCurrentInstance();

const evaluationModelList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const itemsArr = ref([]);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层
  open: false,
  // 弹出层标题
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: window.xtmapConfig.xtBaseUrl + "/contractor/evaluationModel/importData",
});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    modelId: null,
    indicatorName: null,
  },
  rules: {
    modelId: [{ required: true, message: "所属模型ID不能为空", trigger: "blur" }],
    indicatorName: [{ required: true, message: "指标名称不能为空", trigger: "blur" }],
    indicatorType: [{ required: true, message: "指标类型不能为空", trigger: "change" }],
    weight: [{ required: true, message: "权重不能为空", trigger: "blur" }],
    scoreMax: [{ required: true, message: "最大得分不能为空", trigger: "blur" }],
    unit: [{ required: true, message: "单位不能为空", trigger: "blur" }],
    description: [{ required: true, message: "说明不能为空", trigger: "blur" }],
    sortOrder: [{ required: true, message: "排序号不能为空", trigger: "blur" }],
    createBy: [{ required: true, message: "创建人不能为空", trigger: "blur" }],
    createTime: [{ required: true, message: "创建时间不能为空", trigger: "blur" }],
    updateBy: [{ required: true, message: "更新人不能为空", trigger: "blur" }],
    updateTime: [{ required: true, message: "更新时间不能为空", trigger: "blur" }],
    deleted: [{ required: true, message: "是否删除不能为空", trigger: "blur" }],
    tenantId: [{ required: true, message: "租户ID不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询评价模型指标配置列表 */
function getList() {
  loading.value = true;
  listEvaluationModel(queryParams.value).then((response) => {
    evaluationModelList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    modelId: null,
    indicatorName: null,
    indicatorType: null,
    weight: null,
    scoreMax: null,
    unit: null,
    description: null,
    sortOrder: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    deleted: null,
    tenantId: null,
  };
  proxy.resetForm("evaluationModelRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  itemsArr.value = deepClone(selection);
  multiple.value = !selection.length;
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "导入";
  upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download(
    "contractor/evaluationModel/export/template",
    {},
    `evaluationModel_${new Date().getTime()}.xlsx`
  );
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
      response.msg +
      "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  upload.url = `${window.xtmapConfig.xtBaseUrl}/contractor/evaluationModel/importData?userId=${userStore.id}`;
  proxy.$refs["uploadRef"].submit();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加评价模型指标配置";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getEvaluationModel(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改评价模型指标配置";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["evaluationModelRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        const data = {
          ...form.value,
          tenantId: userStore.tenantId,
          updateBy: userStore.name,
          updateTime: formatDate(new Date()),
        };
        updateEvaluationModel(data).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        const data = {
          ...form.value,
          tenantId: userStore.tenantId,
          createBy: userStore.name,
          createTime: formatDate(new Date()),
          updateBy: userStore.name,
          updateTime: formatDate(new Date()),
          deleted: "0",
        };
        addEvaluationModel(data).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id ? [row.id] : ids.value;
  proxy.$modal
    .confirm('是否确认删除评价模型指标配置编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delEvaluationModel(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  const params = itemsArr.value.length > 0 ? itemsArr.value : undefined;
  proxy.codeDownload(
    "contractor/evaluationModel/export",
    params,
    `evaluationModel_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
