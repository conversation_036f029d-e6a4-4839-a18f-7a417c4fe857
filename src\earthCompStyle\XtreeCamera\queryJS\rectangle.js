import { getCoorFromPx, cart3ToDegree } from './getCoorFromPx.js';
// 有三种type : cztype（哪种entity）,pictype(哪张图),busitype(图是哪一类）
// 					)
class PlotRectangle {
	constructor(viewer) {
		this.viewer = viewer;

		this.plotHandler = null;

		this.myCollection = null;
		this.yourCollection = null;

		// 点集和线是分开绘制的，所以为左键点集合声明一个CustomDataSource
		this.tempPointArr;

		this.floatingPoint = undefined;
		this.poiArr = [];
		this.rectangleEntity = undefined; //当前正在绘制的  随着鼠标移动变化的线
		this.init(this.viewer);

		this.jsonCollection = []; // 用json组织数据  json结构需要统一
		// class extend 形式 写个绘制基类  其他的线、面都去extend
	}
	init(viewer) {
		this.myCollection = new Cesium.CustomDataSource('myplotdata');
		this.yourCollection = new Cesium.CustomDataSource('yourplotdata');
		this.viewer.dataSources.add(this.myCollection);
		this.viewer.dataSources.add(this.yourCollection);
		this.tempPointArr = new Cesium.CustomDataSource('poiPolygonData');
		this.viewer.dataSources.add(this.tempPointArr);
	}
	draw(options = { picType: 'p1111', czType: '', busiType: '' }) {
		this.plotHandler = new Cesium.ScreenSpaceEventHandler(
			this.viewer.scene.canvas
		);
		this.plotHandler.setInputAction(
			this.leftEvent(options),
			Cesium.ScreenSpaceEventType.LEFT_CLICK
		);
		this.plotHandler.setInputAction(
			this.moveEvent,
			Cesium.ScreenSpaceEventType.MOUSE_MOVE
		);
		return new Promise((resolve, reject) => {
			this.plotHandler.setInputAction(
				this.rightEvent(resolve, reject, options),
				Cesium.ScreenSpaceEventType.RIGHT_CLICK
			);
		});
	}
	leftEvent(options) {
		const r = (e) => {
			let earthPosition = getCoorFromPx({
				viewer: this.viewer,
				pxCoor: e.position,
				resultType: 'xyz',
			});

			if (Cesium.defined(earthPosition)) {
				let tempPoint;
				if (this.poiArr.length === 0) {
					this.floatingPoint = this.createPoint(
						earthPosition,
						'左键绘制，右键结束'
					);
					this.viewer.entities.add(this.floatingPoint);

					/* let dynamicPositions = new Cesium.CallbackProperty(() => {
						// return this.poiArr;
						return Cesium.Rectangle.fromCartesianArray(this.poiArr);
					}, false); */

					this.rectangleEntity = this.createRectangle(
						this.poiArr,
						options
					);
					this.myCollection.entities.add(this.rectangleEntity);

					this.poiArr.push(earthPosition);
					tempPoint = this.createPoint(earthPosition);
				} else {
					tempPoint = this.createPoint(earthPosition);
				}

				this.tempPointArr.entities.add(tempPoint);
				this.poiArr.push(earthPosition);
			}
		};
		return r;
	}
	moveEvent = (e) => {
		let newPosition = getCoorFromPx({
			viewer: this.viewer,
			pxCoor: e.endPosition,
			resultType: 'xyz',
		});

		if (Cesium.defined(this.floatingPoint) && Cesium.defined(newPosition)) {
			this.floatingPoint.position.setValue(newPosition);
			this.poiArr.pop();
			this.poiArr.push(newPosition);
		}
	};
	rightEvent(resolve, reject, options) {
		const r = (e) => {
			if (this.poiArr.length === 0) {
				return;
			}
			let Position = getCoorFromPx({
				viewer: this.viewer,
				pxCoor: e.position,
				resultType: 'xyz',
			});
			if (Cesium.defined(Position)) {
				this.poiArr.pop();
				this.poiArr.push(Position);

				let endPoint = this.createPoint(Position, '');
				this.tempPointArr.entities.add(endPoint);

				this.viewer.entities.remove(this.floatingPoint);
				this.floatingPoint = null;

				const singleArr = [...this.poiArr];
				const singleEntity = this.createRectangle(singleArr, options);
				this.myCollection.entities.add(singleEntity);

				this.myCollection.entities.remove(this.rectangleEntity);
				this.rectangleEntity = null;

				this.tempPointArr.entities.removeAll();
				this.poiArr.length = 0;
				let poiResultArr = cart3ToDegree(singleArr);
				poiResultArr = [
					{
						longitude: poiResultArr[0][0],
						latitude: poiResultArr[0][1],
					},
					{
						longitude: poiResultArr[1][0],
						latitude: poiResultArr[0][1],
					},
					{
						longitude: poiResultArr[1][0],
						latitude: poiResultArr[1][1],
					},
					{
						longitude: poiResultArr[0][0],
						latitude: poiResultArr[1][1],
					},
					{
						longitude: poiResultArr[0][0],
						latitude: poiResultArr[0][1],
					},
				];

				const plotInfo = {
					name: 'xxx',
					position: poiResultArr,
					czType: options.czType,
					busiType: options.busiType,
					picType: options.picType,
					entityOptions: options,
				};

				this.destroyHandler();
				resolve(plotInfo);
				reject('err----');
			}
		};
		return r;
	}
	reDraw(options) {
		const e = this.createRectangle(options.position, options.entityOptions);
		this.yourCollection.entities.add(e);
	}

	createRectangle(position, options) {
		const shape = new Cesium.Entity({
			rectangle: {
				coordinates: new Cesium.CallbackProperty(() => {
					return Cesium.Rectangle.fromCartesianArray(position);
				}, false),
				// Cesium.Rectangle.fromDegrees(120.0, 40, 125, 45), // west, south, east, north
				material:
					Cesium.Color.fromCssColorString(
						'rgb(20, 120, 255)'
					).withAlpha(0.33),
				classificationType: Cesium.ClassificationType.BOTH,

				// outline: true, // height must be set for outline to display
				// outlineColor: Cesium.Color.RED,
				// extrudedHeight: 10000,
			},
		});
		return shape;
	}
	createPoint(poi, txt) {
		let point = new Cesium.Entity({
			position: poi,
			point: {
				color: Cesium.Color.YELLOW.withAlpha(0.6),
				pixelSize: 6,
				// heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
			},
			label: {
				text: txt || '',
				font: 'bold 12px MicroSoft YaHei',
				outlineWidth: 2,

				horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
				verticalOrigin: Cesium.VerticalOrigin.TOP,
				pixelOffset: new Cesium.Cartesian2(15, 0),
				// pixelOffset: new Cesium.Cartesian2(-15, 16), //偏移量
				showBackground: true,
				backgroundColor: new Cesium.Color(0, 0, 0, 0.7),
				backgroundPadding: new Cesium.Cartesian2(6, 3),
				heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
			},
		});
		return point;
	}
	getPlotData() {
		return this.jsonCollection;
	}
	setPlotData(data) {
		this.reDraw(data);

		/* for (const i of data) {
			this.reDraw(i);
		} */
	}
	clearData() {
		this.myCollection.entities.removeAll();
		this.yourCollection.entities.removeAll();
	}
	destroyHandler() {
		// this.plotHandler.removeInputAction(
		// 	Cesium.ScreenSpaceEventType.LEFT_CLICK
		// );

		if (this.plotHandler) {
			this.plotHandler.destroy();
		}
		this.plotHandler = null;
	}
}

export { PlotRectangle };
